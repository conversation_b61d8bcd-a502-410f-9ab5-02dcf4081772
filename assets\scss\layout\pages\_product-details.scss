@use "../../utils" as *;

/*----------------------------------------*/
/*  Product Details
/*----------------------------------------*/
.product-details-area {
  .left {
    .common-product-design {
      .product-details {
        .top {
          display: flex;
          justify-content: space-between;
          align-items: end;
          gap: 10px;
          margin-bottom: 18px;

          @media #{$xs} {
            flex-direction: column;
            justify-content: start;
            align-items: start;
            gap: 16px;
          }

          @media #{$sm} {
            flex-direction: row;
            justify-content: space-between;
            align-items: end;
            gap: 10px;
          }

          .product-breadcrumb {
            ul {
              display: flex;
              align-items: center;
              gap: 6px;

              li {
                a {
                  color: var(--td-heading);
                  font-size: 14px;
                  font-weight: 500;
                  line-height: normal;

                  &:hover,
                  &.active {
                    color: var(--td-secondary);
                  }
                }

                .icon {
                  display: flex;
                  align-items: center;

                  .arrow-right {
                    color: var(--td-heading);
                    font-size: 16px;
                  }
                }
              }
            }
          }

          .price-share {
            h3 {
              color: var(--td-heading);
              font-size: 30px;
              font-weight: 600;
              line-height: normal;
              margin-bottom: 12px;
            }

            .wish-share {
              display: flex;
              align-items: center;
              gap: 20px;

              .wish {
                display: flex;
                align-items: center;
                gap: 2px;
                color: var(--td-heading);
                font-size: 14px;
                font-weight: 500;
                line-height: normal;

                svg {
                  path {
                    fill: var(--td-text-secondary);
                  }
                }

                &.active {
                  svg {
                    path {
                      fill: #FF6229;
                    }
                  }
                }
              }

              .share {
                display: flex;
                align-items: center;
                gap: 2px;
                color: var(--td-heading);
                font-size: 14px;
                font-weight: 500;
                line-height: normal;

                svg {
                  fill: #26B4FB;
                }
              }
            }
          }
        }

        .delivery-method-and-speed {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-top: 32px;

          @media #{$xs} {
            flex-direction: column;
            align-items: start;
            gap: 16px;
          }

          @media #{$sm} {
            flex-direction: row;
            align-items: end;
            gap: 10px;
          }

          .delivery-method {
            display: flex;
            align-items: center;
            gap: 6px;

            p {
              color: var(--td-heading);
              font-size: 13px;
              font-weight: 500;
              line-height: normal;
            }
          }

          .speed {
            display: flex;
            align-items: center;
            gap: 6px;

            p {
              color: var(--td-heading);
              font-size: 13px;
              font-weight: 500;
              line-height: normal;
            }
          }
        }

        .details-saperator {
          border-color: rgba(48, 48, 48, 0.16);
          margin: 30px 0;
        }

        .product-details-box {
          h2 {
            color: var(--td-heading);
            font-size: 20px;
            font-weight: 600;
            line-height: lh(22, 16);
            margin-top: 30px;
          }

          h3 {
            color: var(--td-heading);
            font-size: 18px;
            font-weight: 600;
            line-height: lh(22, 16);
            margin-top: 30px;
          }

          h4 {
            color: var(--td-heading);
            font-size: 16px;
            font-weight: 600;
            line-height: lh(22, 16);
            margin-top: 30px;
          }

          h5 {
            color: var(--td-heading);
            font-size: 14px;
            font-weight: 600;
            line-height: lh(22, 16);
            margin-top: 30px;
          }

          h6 {
            color: var(--td-heading);
            font-size: 12px;
            font-weight: 600;
            line-height: lh(22, 16);
            margin-top: 30px;
          }

          p {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 400;
            line-height: lh(22, 14);
            margin-top: 16px;
          }

          ul {
            list-style-type: disc;
            padding-left: 20px;
            margin-top: 16px;

            li {
              color: var(--td-text-primary);
              font-size: 14px;
              font-weight: 400;
              line-height: lh(22, 14);
              margin-bottom: 5px;
            }
          }

        }
      }
    }
  }

  .right {
    .game-details-right {
      .your-order-card {
        padding: 24px;
        border-radius: 20px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #FFF;

        h5 {
          color: var(--td-heading);
          font-size: 20px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 20px;
        }

        .price-increase-decrease {
          p {
            color: var(--td-text-primary);
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
          }

          .calculator-box {
            margin: 0 70px;

            @media #{$lg} {
              margin: 0 20px;
            }

            @media #{$md,$xs} {
              margin: 0 130px;
            }

            @media #{$xs} {
              margin: 0 0px;
            }

            @media #{$sm} {
              margin: 0 100px;
            }

            .calculator {
              position: relative;

              .calculator-btn {
                display: flex;
                width: 36px;
                height: 36px;
                justify-content: center;
                align-items: center;
                gap: 12px;
                flex-shrink: 0;
                border-radius: 7px 0px 0px 7px;
                background: var(--td-secondary);

                i {
                  font-size: 12px;
                  color: var(--td-white);
                }
              }

              .form-control {
                height: 36px;
                width: 100%;
                border-radius: 6px;
                border: 1px solid rgba(255, 98, 41, 0.14);
                background: rgba(255, 98, 41, 0.08);
                color: var(--td-heading);
                text-align: center;
                font-size: 16px;
                font-weight: 400;
                line-height: 20px;
                border-radius: 10px;

              }

              .increase-btn {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                inset-inline-end: 0;
                border-radius: 0 12px 12px 0;

                @include rtl {
                  border-radius: 12px 0 0 12px;
                }
              }

              .decrease-btn {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                inset-inline-start: 0;
                border-radius: 12px 0 0 12px;

                @include rtl {
                  border-radius: 0 12px 12px 0;
                }
              }
            }

            .error-msg {
              font-size: rem(12);
            }
          }

          .voucher {
            width: 100%;
            margin-top: 20px;

            input[type=text],
            input[type=number] {
              width: 100%;
              display: flex;
              height: 44px;
              padding: 0px 4px 0px 16px;
              align-items: center;
              align-self: stretch;
              border-radius: 10px;
              border: 1px solid rgba(48, 48, 48, 0.16);
              font-size: 14px;
              font-weight: 500;
              line-height: 20px;
              color: var(--td-heading);

              &::placeholder {
                color: rgba(48, 48, 48, 0.60);
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
              }
            }
          }

          .total-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;

            .left {
              h6 {
                color: var(--td-heading);
                font-size: 18px;
                font-weight: 600;
                line-height: normal;
              }
            }

            .right {
              h6 {
                color: var(--td-heading);
                font-size: 18px;
                font-weight: 600;
                line-height: normal;
              }
            }
          }

          .buy-now {
            margin-top: 30px;
          }
        }
      }

      .seller-details-card {
        padding: 24px;
        border-radius: 16px;
        background: #F1F1F1;
        margin-top: 24px;

        .seller-card-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-radius: 12px;
          background: #FFF;
          padding: 12px;

          .left {
            display: flex;
            align-items: center;
            gap: 8px;

            .img-box {
              width: 50px;
              height: 50px;
              border-radius: 7px;
              overflow: hidden;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .text {
              h3 {
                color: var(--td-heading);
                font-size: 16px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 8px;

                span {
                  display: inline-flex;
                  height: 24px;
                  padding: 0px 8px;
                  justify-content: center;
                  align-items: center;
                  gap: 10px;
                  color: #FFF;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 13.986px;
                  border-radius: 30px;
                  background: #31B269;
                }
              }

              p {
                color: var(--td-text-primary);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 13px;
              }
            }
          }
        }

        .chat-seller {
          margin-top: 16px;
        }
      }
    }
  }
}

.has-sticky-component {
  position: sticky;
  top: 395px;
}

.suggested-product-area {
  background: #F9F9F9;
}