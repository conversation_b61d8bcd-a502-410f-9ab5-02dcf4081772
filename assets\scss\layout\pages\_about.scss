@use '../../utils' as *;

/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.about-us-page-area {
  .top-part {
    background: linear-gradient(58deg, rgba(255, 255, 255, 0.10) 0.14%, rgba(255, 41, 241, 0.10) 100.47%);
    position: relative;

    .extra-pb-for-img {
      padding-bottom: 70px;

      @media #{$md,$xs} {
        padding-bottom: 0px;
      }
    }

    .company-stats {
      padding: 16px;
      border-radius: 16px;
      border: 1px dashed var(--td-secondary);
      background: #FFF;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.10);
      position: absolute;
      bottom: 30%;
      right: 5%;
      width: 345px;

      @media #{$md,$xs} {
        display: none;
      }

      .seller-count {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 16px;

        .icon {
          display: inline-flex;
          width: 50px;
          height: 50px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 8px;
          background: rgba(255, 101, 54, 0.15);

          .count-icon {
            font-size: 40px;
            color: var(--td-secondary);
          }
        }

        .text {
          h4 {
            color: var(--td-heading);
            font-size: 30px;
            font-weight: 700;
            line-height: normal;
          }

          p {
            color: var(--td-heading);
            font-size: 16px;
            font-weight: 500;
            line-height: normal;
          }
        }
      }

      p {
        color: var(--td-heading);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
      }
    }

    .right {
      margin-left: 16px;
      position: relative;

      @media #{$md,$xs} {
        margin-left: 0px;
      }

      .top-img {
        width: 100%;
        height: 170px;
        margin-bottom: 30px;

        @media #{$xs} {
          margin-bottom: 16px;
        }

        img {
          width: 100%;
          height: 100%;
          border-radius: 16px;
          object-fit: cover;
        }
      }

      .middle-img {
        width: 100%;
        height: 312px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 18px;
          object-fit: cover;
        }
      }

      .bottom-img {
        width: 241px;
        height: 250px;
        border-radius: 18px;
        border: 15px solid #FFF6FE;
        position: absolute;
        bottom: -70px;
        left: -100px;
        overflow: hidden;

        @media #{$md,$xs} {
          display: none;
        }

        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          overflow: hidden;
        }
      }
    }

    .left {
      h2 {
        color: var(--td-heading);
        font-size: 40px;
        font-weight: 700;
        line-height: lh(50, 40);
        width: 80%;
        margin-bottom: 16px;

        @media #{$lg} {
          font-size: 35px;
        }

        @media #{$md} {
          font-size: 30px;
          width: 100%;
        }

        @media #{$xs} {
          font-size: 20px;
          width: 100%;
        }

        @media #{$sm} {
          font-size: 24px;
          width: 100%;
        }
      }

      .des-1 {
        color: rgba(48, 48, 48, 0.80);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-top: 18px;

        @media #{$xs} {
          margin-top: 10px;
          font-size: 14px;
        }
      }

      .des-2 {
        color: rgba(48, 48, 48, 0.80);
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);
        margin-top: 18px;
        width: 90%;

        @media #{$xs} {
          margin-top: 10px;
          font-size: 14px;
        }
      }

      .action-btn {
        margin-top: 30px;
      }
    }
  }

  .bottom-part {
    h4 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 700;
      line-height: normal;
    }

    p {
      color: rgba(48, 48, 48, 0.80);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: lh(26, 16);
      margin-top: 10px;
    }
  }
}

.about-stats-area {
  background: #F1F1F1;

  .about-stats-area-content {
    h2 {
      color: var(--td-heading);
      text-align: center;
      font-size: 30px;
      font-weight: 700;
      line-height: normal;

      @media #{$md} {
        font-size: 26px;

      }

      @media #{$xs} {
        font-size: 24px;

      }
    }

    .all-stats-card {
      padding: 65px 30px;
      border-radius: 20px;
      background: #FFF;
      margin-top: 16px;

      @media #{$xs} {
        padding: 30px 10px;

      }

      .stats-card {
        h2 {
          color: var(--td-secondary);
          text-align: center;
          font-size: 40px;
          font-weight: 700;
          line-height: normal;
          margin-bottom: 20px;

          @media #{$xs} {
            font-size: 22px;
            margin-bottom: 10px;

          }
        }

        p {
          color: var(--td-heading);
          text-align: center;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;

          @media #{$xs} {
            font-size: 14px;
          }
        }
      }
    }
  }

}