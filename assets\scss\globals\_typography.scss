@use "../utils" as u;
@use "../utils" as *;

/*----------------------------------------*/
/*   1.3 Typography
/*----------------------------------------*/
* {
	margin: 0;
	padding: 0;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

body {
	font-family: var(--td-body-font);
	font-size: 15px;
	font-weight: normal;
	line-height: 1.5;
	color: var(--td-text-primary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--td-heading-font);
	color: var(--td-heading);
	margin-top: 0px;
	line-height: 1.3;
	margin-bottom: 0;
	word-break: break-word;
}

h1,
.h1 {
	font-size: clamp(1.5rem, 2vw + 1rem, 3.75rem); // Small screen (320px): 1.75rem, Large screen (1440px): 2.76rem
	line-height: 1.2;
	font-weight: 500;
}

h2,
.h2 {
	font-size: clamp(1.25rem, 0.75vw + 1rem, 48px); // Small screen (320px): 1.68rem, Large screen (1440px): 2.33rem */
	line-height: 1.3;
	font-weight: 500;
}

h3,
.h3 {
	font-size: clamp(1.125rem, 1vw + 1rem, 1.875rem); //  Small screen (320px): 1.32rem, Large screen (1440px): 2rem
	line-height: 1.4;
	font-weight: 500;
}

h4,
.h4 {
	font-size: clamp(1.25rem, 1vw + 1rem, 1.5rem); //Small screen (320px): 1.4rem, Large screen (1440px): 1.66rem
	line-height: 1.5;
	font-weight: 500;
}

h5,
.h5 {
	font-size: clamp(1rem, 0.5vw + 1rem, 1.125rem); // Small screen (320px): 1.06rem, Large screen (1440px): 1.5rem
	line-height: 1.6;
	font-weight: 500;
}

h6,
.h6 {
	font-size: clamp(0.875rem, 0.4vw + 1rem, 1rem); // Small screen (320px): 1.04rem, Large screen (1440px): 1.25rem
	line-height: 1.6;
	font-weight: 500;
}

ul {
	margin: 0px;
	padding: 0px;
	list-style-type: none;
}

p {
	font-size: clamp(0.75rem, 0.4vw + 0.875rem, 0.875rem);
	line-height: 1.625;
	color: var(--td-text-primary);
	margin-bottom: 0;
}

a {
	text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
	@include td-transition;
}

a:focus,
.button:focus {
	text-decoration: none;
	outline: none;
}

a:focus,
a:hover {
	text-decoration: none;
}

a,
button {
	color: inherit;
	outline: none;
	border: none;
	background: transparent;
}

.o-x-clip {
	overflow-x: clip;
}

img {
	max-width: 100%;
	object-fit: cover;
}

button {
	font-family: var(--td-ff-body) !important;
}

button:hover {
	cursor: pointer;
}

button:focus {
	outline: 0;
	font-family: var(--td-ff-body);
}

.uppercase {
	text-transform: uppercase;
}

.capitalize {
	text-transform: capitalize;
}

hr:not([size]) {
	border-color: var(--td-card-bg-1);
	opacity: 1;
	border-width: 1px;
}

*::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::-moz-selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

::selection {
	background: var(--td-black);
	color: var(--td-white);
	text-shadow: none;
}

*::-moz-placeholder {
	opacity: 1;
	font-size: 14px;
}

*::placeholder {
	opacity: 1;
	font-size: 14px;
	font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img {
	& img {
		width: 100%;
	}
}

.m-img {
	& img {
		max-width: 100%;
	}
}

.fix {
	overflow: hidden;
}

.clear {
	clear: both;
}

.f-left {
	float: left;
}

.f-right {
	float: right;
}

.z-index-1 {
	z-index: 1;
}

.z-index-11 {
	z-index: 11;
}

.p-relative {
	position: relative;
}

.p-absolute {
	position: absolute;
}

.position-absolute {
	position: absolute;
}

.include-bg {
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat;
}

.hr-1 {
	border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
	overflow-x: clip;
}

.o-visible {
	overflow: visible;
}

.valign {
	@include flexbox();
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
}
