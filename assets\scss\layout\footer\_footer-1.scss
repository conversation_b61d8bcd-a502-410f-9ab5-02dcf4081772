@use '../../utils' as *;

/*----------------------------------------*/
/*  5.1 footer-1
/*----------------------------------------*/
.footer-1 {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;

  .footer-top {
    .left {
      .logo {
        margin-bottom: 30px;

        img {
          height: 36px;
        }
      }

      p {
        color: rgba(255, 255, 255, 0.80);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: lh(22, 14);
      }

      .email {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 40px;

        @media #{$md,$xs} {
          margin-top: 20px;
        }

        a {
          color: #FFF;
          font-size: 24px;
          font-weight: 600;
          line-height: (34, 24);
          display: flex;
          align-items: center;
          gap: 10px;
          position: relative;
          padding-bottom: 16px;

          @media #{$md,$xs} {
            padding-bottom: 10px;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background-color: rgba(255, 255, 255, 0.10);
          }

          span {
            display: flex;
            align-items: center;

            .arrow-right {
              color: #fff;
              font-size: 30px;
            }
          }
        }
      }
    }

    .right {
      margin-left: 40px;

      @media #{$md,$xs} {
        margin-left: 0px;
      }

      .social-icons {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        a {
          display: flex;
          align-items: center;
          gap: 2px;
          color: rgba(255, 255, 255, 0.80);
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: lh(22, 14);
          transition: all 0.3s ease-in-out;

          .social-icon {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.80);
          }

          &:hover {
            color: var(--td-secondary);

            .social-icon {
              color: var(--td-secondary);
            }
          }
        }
      }

      .footer-links {
        margin-top: 80px;

        @media #{$xs} {
          margin-top: 45px;
        }

        .footer-menu {
          h6 {
            color: rgba(255, 255, 255, 0.80);
            font-size: 14px;
            font-weight: 500;
            line-height: lh(22, 14);
            margin-bottom: 22px;

            @media #{$xs} {
              margin-bottom: 16px;
            }
          }

          p {
            color: rgba(255, 255, 255, 0.80);
            font-size: 14px;
            font-weight: 500;
            line-height: lh(22, 14);
            width: 80%;
          }

          ul {
            li {
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              a {
                color: rgba(255, 255, 255, 0.80);
                font-size: 14px;
                font-weight: 500;
                line-height: lh(22, 14);
                transition: all 0.3s ease-in-out;

                &:hover {
                  color: var(--td-secondary);
                }
              }
            }
          }
        }
      }


    }
  }

  .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.10);

    @media #{$xs} {
      flex-direction: column;
      justify-content: start;
      align-items: start;
    }

    .left {
      p {
        color: rgba(255, 255, 255, 0.80);
        font-size: 13px;
        font-weight: 500;
        line-height: lh(18, 13);
      }
    }

    .right {
      @media #{$xs} {
        margin-top: 10px;
      }

      ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 60px;

        @media #{$md,$xs} {
          gap: 40px;
        }

        @media #{$xs} {
          gap: 10px;
        }

        @media #{$sm} {
          gap: 40px;
        }

        li {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            inset-inline-end: -30px;
            transform: translateY(-50%);
            width: 1px;
            height: 15px;
            background-color: rgba(255, 255, 255, 0.10);

            @media #{$md,$xs} {
              inset-inline-end: -20px;
            }

            @media #{$xs} {
              display: none;
            }
          }

          &:last-child::after {
            display: none;
          }

          a {
            color: rgba(255, 255, 255, 0.80);
            font-size: 13px;
            font-weight: 500;
            line-height: lh(18, 13);
            text-transform: uppercase;
            transition: all 0.3s ease-in-out;

            &:hover {
              color: var(--td-secondary);
            }
          }
        }
      }
    }
  }
}