@use '../../utils' as *;

/*----------------------------------------*/
/*  main-page
/*----------------------------------------*/
.activity-and-referral-link {
  margin-top: 30px;

  .activity {
    .activity-header {
      .activity-chart {
        #chart-container {
          padding: 20px;
          max-width: 900px;
          margin: 0 auto;

          @media #{$xs} {
            padding: 20px 0px;
          }
        }

        .button-and-date-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          @media #{$xs} {
            flex-direction: column;
            justify-content: start;
            align-items: start;
            gap: 14px;
          }

          .date-content {
            display: flex;
            align-items: center;
            gap: 10px;

            @media #{$xs} {
              flex-direction: column;
              justify-content: start;
              align-items: start;
              gap: 14px;
            }

            .date-year {
              display: flex;
              align-items: center;
              gap: 10px;


              h6 {
                color: var(--td-heading);
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
              }

              .month-year-select {
                select {
                  height: 30px;
                  line-height: 30px;
                  border-radius: 8px;
                  border: 1px solid #D0D0D0;
                  ;
                  background: #FFF;
                  padding: 0 3px;
                  font-size: 14px;
                  color: var(--td-heading);
                  cursor: pointer;
                }
              }
            }
          }
        }

        #button-container {
          display: flex;
          justify-content: center;
          gap: 20px;

          @media #{$xs} {
            gap: 5px;
          }
        }

        .chart-btn {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: normal;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;

          .circle-toggle {
            width: 16px;
            height: 16px;
            border: 2px solid #D0D0D0;
            ;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.3s ease;

            .inner-circle {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #D0D0D0;
              ;
              transition: background-color 0.3s ease;
            }
          }

          &.active {
            .circle-toggle {
              border-color: var(--td-secondary);

              .inner-circle {
                background-color: var(--td-secondary);
              }
            }
          }

        }

        #activityLineChart {
          padding: 20px;
          border-radius: 12px;
          background: #FFF;
          box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);
        }


      }
    }
  }

  .referral-link {
    margin-top: 70px;

    @media #{$xl,$lg,$md,$xs} {
      margin-top: 0px;
    }

    .referral-header {
      h4 {
        color: var(--td-heading);
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 20px;
      }
    }
  }

  .total-referrals {
    margin-top: 26px;

    .total-referrals-card {
      padding: 24px;
      border-radius: 12px;
      background: #FFF;
      box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);

      p {
        color: #303030;
        font-size: 15px;
        font-weight: 500;
        line-height: lh(20, 15);
        margin-bottom: 10px;
      }

      h4 {
        color: var(--td-secondary);
        font-size: 26px;
        font-weight: 600;
        line-height: normal;
      }
    }
  }

  .share-link {
    margin-top: 26px;
    display: flex;
    align-items: center;
    gap: 7px;

    h5 {
      color: var(--td-heading);
      color: #303030;
      font-size: 17px;
      font-weight: 600;
      line-height: 20px;
    }

    .share-btn {
      .share-btn-item {
        display: flex;
        width: 30px;
        height: 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 6px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        transition: all 0.3s ease;

        .share-icon {
          font-size: 18px;
          color: #838383;
        }

        &:hover {
          background: var(--td-secondary);
          border-color: var(--td-secondary);

          .share-icon {
            color: var(--td-white);
          }
        }
      }
    }
  }
}

.referral-content {
  padding: 30px;
  border-radius: 12px;
  background: #FFF;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);

  @media #{$xs} {
    padding: 16px;
  }

  .referral-link-content {
    padding: 44px 30px;
    border-radius: 12px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #FFF;

    @media #{$xs} {
      padding: 20px 14px;
    }

    .referral-link-input {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 5px;
      position: relative;

      input {
        flex-grow: 1;
        height: 52px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: transparent;
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        padding: 0 10px;
        outline: none;

        &::placeholder {
          color: var(--td-text-secondary);
        }
      }

      .copy-btn {
        display: flex;
        height: 36px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 8px;
        background: var(--td-secondary);
        color: #FFF;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        line-height: lh(20, 14);
        cursor: pointer;
        transition: all 0.3s ease;
        position: absolute;
        inset-inline-end: 8px;

        &:hover {
          background: var(--td-secondary);
          color: var(--td-white);
        }
      }
    }

    p {
      color: rgba(48, 48, 48, 0.80);
      font-size: 13px;
      font-weight: 400;
      line-height: lh(20, 14);
    }
  }
}

.common-table-card {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;
  }
}

.purchase-order {
  margin-top: 30px;
}

.sold-orders-box {
  .sort-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;

    @media #{$xs} {
      flex-direction: column;
      align-items: start;
      justify-content: start;
      gap: 10px;
    }

    h4 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
    }

    .table-sort {
      display: flex;
      align-items: center;
      gap: 10px;

      .left {
        p {
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 600;
          line-height: normal;
        }
      }
    }
  }
}

.transactions-history-box {
  .sort-title {
    h4 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 16px;
    }

    .table-sort {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;

      @media #{$md,$xs} {
        flex-direction: column;
        align-items: start;
        justify-content: start;
        gap: 10px;
      }

      .sort-dropdown{
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .input-search {
        width: 200px;

        input[type=text],
        input[type=search],
        input[type=email],
        input[type=tel],
        input[type=number],
        input[type=password],
        textarea {
          outline: none;
          height: 40px;
          width: 100%;
          padding: 0 15px;
          border-radius: 12px;
          border: 1px solid rgba(48, 48, 48, 0.16);
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 500;
          line-height: normal;

          &::placeholder {
            color: rgba(48, 48, 48, 0.60);
          }
        }
      }
    }
  }
}