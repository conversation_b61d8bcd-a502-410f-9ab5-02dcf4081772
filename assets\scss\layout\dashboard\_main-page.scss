@use "../../utils" as *;

/*----------------------------------------*/
/*  3.4.7 Main Page
/*----------------------------------------*/
.seller-overview {
  .title {
    margin-bottom: 30px;

    h4 {
      color: var(--td-heading);
      font-size: 30px;
      font-weight: 600;
      line-height: normal;
    }
  }
}
.activity-and-referral-link {
  margin-top: 30px;

  .activity {
    .activity-header {
      .activity-chart {
        #chart-container {
          padding: 20px;
          max-width: 900px;
          margin: 0 auto;

          @media #{$xs} {
            padding: 20px 0px;
          }
        }

        .button-and-date-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          @media #{$xs} {
            flex-direction: column;
            justify-content: start;
            align-items: start;
            gap: 14px;
          }

          .date-content {
            display: flex;
            align-items: center;
            gap: 10px;

            @media #{$xs} {
              flex-direction: column;
              justify-content: start;
              align-items: start;
              gap: 14px;
            }

            .date-year {
              display: flex;
              align-items: center;
              gap: 10px;

              h6 {
                color: var(--td-heading);
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
              }

              .month-year-select {
                select {
                  height: 30px;
                  line-height: 30px;
                  border-radius: 6px;
                  border: 1px solid #d0d0d0;
                  background: #fff;
                  padding: 0 3px;
                  font-size: 14px;
                  color: var(--td-heading);
                  cursor: pointer;
                }
              }
            }
          }
        }

        #button-container {
          display: flex;
          justify-content: center;
          gap: 20px;

          @media #{$xs} {
            gap: 5px;
          }
        }

        .chart-btn {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: normal;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;

          .circle-toggle {
            width: 16px;
            height: 16px;
            border: 2px solid #d0d0d0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.3s ease;

            .inner-circle {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #d0d0d0;
              transition: background-color 0.3s ease;
            }
          }

          &.active {
            .circle-toggle {
              border-color: var(--td-secondary);

              .inner-circle {
                background-color: var(--td-secondary);
              }
            }
          }
        }
      }
    }
  }

  .referral-link {
    .referral-header {
      h4 {
        color: var(--td-heading);
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 20px;
      }
    }
  }

  .report-box {
    .title {
      h4 {
        color: var(--td-heading);
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 20px;
      }
    }

    .report-box-chart-content {
      padding: 30px;
      border-radius: 10px;
      background: #fff;
      box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);
      border: 1px solid var(--td-border-2);

      .filter-date {
        display: flex;
        justify-content: end;
        align-items: end;

        @media #{$xs} {
          justify-content: start;
          align-items: start;
        }
        @media #{$sm} {
          justify-content: end;
          align-items: end;
        }

        .date-content {
          display: flex;
          align-items: center;
          gap: 10px;

          @media #{$xs} {
            flex-direction: column;
            justify-content: start;
            align-items: start;
            gap: 14px;
          }
          @media #{$sm} {
            flex-direction: row;
            justify-content: center;
            align-items: center;
            gap: 14px;
          }

          .date-year {
            display: flex;
            align-items: center;
            gap: 10px;

            h6 {
              color: var(--td-heading);
              font-size: 14px;
              font-weight: 500;
              line-height: normal;
            }

            .month-year-select {
              select {
                height: 30px;
                line-height: 30px;
                border-radius: 8px;
                border: 1px solid #d0d0d0;
                background: #fff;
                padding: 0 3px;
                font-size: 14px;
                color: var(--td-heading);
                cursor: pointer;
              }
            }
          }
        }
      }
      .report-chart {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 34px;
      }
    }
  }

  .total-referrals {
    margin-top: 26px;

    .total-referrals-card {
      padding: 24px;
      border-radius: 8px;
      background: #fff;
      box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);
      display: flex;
      flex-direction: column;
      align-items: center;

      p {
        color: #303030;
        font-size: 15px;
        font-weight: 500;
        line-height: lh(20, 15);
      }

      h4 {
        color: var(--td-secondary);
        font-size: 40px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;
      }
    }
  }

  .share-link {
    margin-top: 65px;
    display: flex;
    align-items: center;
    gap: 7px;

    h5 {
      color: var(--td-heading);
      color: #303030;
      font-size: 17px;
      font-weight: 600;
      line-height: 20px;
    }

    .share-btn {
      .share-btn-item {
        display: flex;
        width: 30px;
        height: 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 6px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        transition: all 0.3s ease;

        .share-icon {
          font-size: 18px;
          color: #838383;
        }

        &:hover {
          background: var(--td-secondary);
          border-color: var(--td-secondary);

          .share-icon {
            color: var(--td-white);
          }
        }
      }
    }
  }
}

.referral-content {
  padding: 30px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--td-border-2);

  @media #{$xs} {
    padding: 16px;
  }

  .referral-link-content {
    padding: 44px 30px;
    border-radius: 8px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #fff;

    @media #{$xs} {
      padding: 20px 14px;
    }

    .referral-link-input {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 5px;
      position: relative;

      input {
        flex-grow: 1;
        height: 52px;
        border-radius: 8px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: transparent;
        color: var(--td-secondary);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        padding: 0 10px;
        outline: none;

        &::placeholder {
          color: var(--td-text-secondary);
        }
      }

      .copy-btn {
        display: flex;
        height: 36px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 6px;
        background: var(--td-secondary);
        color: #fff;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        line-height: lh(20, 14);
        cursor: pointer;
        transition: all 0.3s ease;
        position: absolute;
        inset-inline-end: 8px;

        &:hover {
          background: var(--td-secondary);
          color: var(--td-white);
        }
      }
    }

    p {
      color: rgba(48, 48, 48, 0.8);
      font-size: 13px;
      font-weight: 400;
      line-height: lh(20, 14);
    }
  }
}

.common-table-card {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;
  }
}

.purchase-order {
  margin-top: 30px;
}

.sold-orders-box {
  .sort-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;

    @media #{$xs} {
      flex-direction: column;
      align-items: start;
      justify-content: start;
      gap: 10px;
    }

    h4 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
    }

    .table-sort {
      display: flex;
      align-items: center;
      gap: 10px;

      .left {
        p {
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 600;
          line-height: normal;
        }
      }
    }
  }
}

.transactions-history-box {
  .sort-title {
    h4 {
      color: var(--td-heading);
      font-size: 20px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 16px;
    }

    .table-sort {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;

      @media #{$md,$xs} {
        flex-direction: column;
        align-items: start;
        justify-content: start;
        gap: 10px;
      }

      @media #{$md} {
        width: 50%;
      }
      @media #{$xs} {
        width: 100%;
      }

      .sort-dropdown {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .input-search {
        width: 200px;

        @media #{$md,$xs} {
          width: 100%;
        }

        input[type="text"],
        input[type="search"],
        input[type="email"],
        input[type="tel"],
        input[type="number"],
        input[type="password"],
        textarea {
          outline: none;
          height: 40px;
          width: 100%;
          padding: 0 15px;
          border-radius: 8px;
          border: 1px solid rgba(48, 48, 48, 0.16);
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 500;
          line-height: normal;

          &::placeholder {
            color: rgba(48, 48, 48, 0.6);
          }
        }
      }

      .calender {
        @media #{$md,$xs} {
          width: 100%;
        }
      }

      .sort-dropdown {
        @media #{$md,$xs} {
          width: 100%;
        }
      }

      .sort-filter {
        @media #{$md,$xs} {
          width: 100%;
        }

        .nice-select {
          @media #{$md,$xs} {
            width: 100%;
          }

          .list {
            @media #{$md,$xs} {
              width: 100%;
            }
          }
        }
      }

      &-2 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media #{$md,$xs} {
          flex-direction: column;
          align-items: start;
          justify-content: start;
          gap: 10px;
          width: 100%;
        }

        .left {
          display: flex;
          align-items: center;
          gap: 16px;

          @media #{$xs} {
            flex-direction: column;
            align-items: start;
            justify-content: start;
            gap: 10px;
            width: 100%;
          }

          .input-search {
            @media #{$xs} {
              width: 100%;
            }
          }
          .calender {
            @media #{$xs} {
              width: 100%;
            }
          }
          .action-btn {
            @media #{$xs} {
              width: 100%;
            }

            .primary-button {
              @media #{$xs} {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}

.payment-form {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;
  }

  .full-payment-form {
    border-radius: 12px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;

    @media #{$xs} {
      padding: 10px;
    }

    .inside {
      max-width: 620px;
      width: 100%;

      .payment-box {
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        padding: 34px 20px;
        border-radius: 10px;
        background: rgba(var(--td-secondary-rgb), 0.1);
        margin-bottom: 20px;

        .withdraw-box {
          display: flex;
          flex-direction: column;
          align-items: center;

          p {
            color: var(--td-heading);
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            line-height: lh(20, 16);
            margin-bottom: 16px;
          }

          h3 {
            color: var(--td-secondary);
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            line-height: lh(36, 32);

            @media #{$xs} {
              font-size: 24px;
            }
          }
        }
      }

      .withdraw-form {
        padding: 30px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);

        @media #{$xs} {
          padding: 10px;
        }

        .title {
          border-bottom: 1px solid rgba(48, 48, 48, 0.16);
          margin-bottom: 30px;

          h4 {
            color: var(--td-heading);
            font-size: 20px;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 16px;

            @media #{$xs} {
              font-size: 16px;
              margin-bottom: 10px;
            }
          }
        }

        .action-btn {
          margin-top: 20px;

          @media #{$xs} {
            margin-top: 10px;
          }
        }
      }
    }
  }

  .add-payment-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
}

.follow {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;
  }
}

.referral-program {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 16px;
  }

  .referral-program-box {
    padding: 30px;
    border-radius: 10px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: rgba(var(--td-secondary-rgb), 0.1);

    @media #{$xs} {
      padding: 14px;
    }

    @media #{$sm} {
      padding: 30px;
    }

    .account-balance {
      h5 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: normal;
      }

      .card-box {
        padding: 44px 10px;
        border-radius: 10px;
        background: #fff;
        margin-top: 20px;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;

        p {
          color: var(--td-heading);
          text-align: center;
          font-size: 16px;
          font-weight: 500;
          line-height: lh(20, 16);
          margin-bottom: 16px;
        }

        h2 {
          color: var(--td-secondary);
          text-align: center;
          font-size: 32px;
          font-weight: 600;
          line-height: lh(36, 32);
        }
      }

      &.active {
        .card-box {
          background-color: rgba(var(--td-secondary-rgb), 0.1);
        }
      }
    }

    .successful-referrals {
      h5 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: normal;
      }

      .card-box {
        padding: 44px 10px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #fff;
        margin-top: 20px;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;

        @media #{$xs} {
          padding: 26px 10px;
        }

        @media #{$sm} {
          padding: 44px 10px;
        }

        p {
          color: var(--td-heading);
          text-align: center;
          font-size: 16px;
          font-weight: 500;
          line-height: lh(20, 16);
          margin-bottom: 16px;
        }

        h2 {
          color: var(--td-secondary);
          text-align: center;
          font-size: 32px;
          font-weight: 600;
          line-height: lh(36, 32);
        }
      }
    }

    .link-share-box {
      .referral-link-content {
        padding: 20px 30px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #fff;
        margin-top: 43px;

        @media #{$md,$xs} {
          margin-top: 0px;
        }

        @media #{$xs} {
          padding: 10px 20px;
        }

        @media #{$sm} {
          padding: 20px 30px;
        }

        .referral-link-input {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 5px;
          position: relative;

          input {
            flex-grow: 1;
            height: 52px;
            border-radius: 8px;
            border: 1px solid rgba(48, 48, 48, 0.16);
            background: transparent;
            color: var(--td-heading);
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            padding: 0 10px;
            outline: none;

            &::placeholder {
              color: var(--td-text-secondary);
            }
          }

          .copy-btn {
            display: flex;
            height: 36px;
            padding: 0px 16px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 6px;
            background: var(--td-secondary);
            color: #fff;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            line-height: lh(20, 14);
            cursor: pointer;
            transition: all 0.3s ease;
            position: absolute;
            inset-inline-end: 8px;

            &:hover {
              background: var(--td-secondary);
              color: var(--td-white);
            }
          }
        }

        p {
          color: rgba(48, 48, 48, 0.8);
          font-size: 13px;
          font-weight: 400;
          line-height: lh(20, 14);
        }

        .share-link {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-top: 14px;

          h5 {
            color: var(--td-heading);
            font-size: 16px;
            font-weight: 500;
            line-height: lh(20, 16);
          }

          .share-btn {
            display: flex;
            align-items: center;
            gap: 10px;

            .share-btn-item {
              display: flex;
              width: 30px;
              height: 30px;
              justify-content: center;
              align-items: center;
              gap: 2px;
              border-radius: 6px;
              border: 1px solid rgba(48, 48, 48, 0.16);

              .share-icon {
                font-size: 14px;
                color: var(--td-heading);
              }

              &:hover {
                border-color: var(--td-secondary);
                background: var(--td-secondary);

                .share-icon {
                  color: var(--td-white);
                }
              }
            }
          }
        }
      }
    }

    .how-to-earn {
      margin-top: 30px;

      h4 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: normal;
      }

      .earn-box {
        padding: 30px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: rgba(var(--td-secondary-rgb), 0.05);

        @media #{$xs} {
          padding: 20px;
        }

        @media #{$sm} {
          padding: 30px;
        }

        .earn-list {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          h6 {
            color: var(--td-heading);
            font-size: 16px;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 9px;
          }

          p {
            color: rgba(48, 48, 48, 0.8);
            font-size: 15px;
            font-weight: 400;
            line-height: normal;
            padding-inline-start: 14px;
          }
        }
      }
    }
  }
}

.listing-delivery-item {
  padding: 30px;
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
