/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else {
		var a = factory();
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function() {
return /******/ (function() { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/@yaireo/tagify/dist/tagify.min.js":
/*!********************************************************!*\
  !*** ./node_modules/@yaireo/tagify/dist/tagify.min.js ***!
  \********************************************************/
/***/ (function(module) {

eval("/**\n * Tagify (v 4.16.4) - tags input component\n * By undefined\n * https://github.com/yairEO/tagify\n * Permission is hereby granted, free of charge, to any person obtaining a copy\r\n * of this software and associated documentation files (the \"Software\"), to deal\r\n * in the Software without restriction, including without limitation the rights\r\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\n * copies of the Software, and to permit persons to whom the Software is\r\n * furnished to do so, subject to the following conditions:\r\n * \r\n * The above copyright notice and this permission notice shall be included in\r\n * all copies or substantial portions of the Software.\r\n * \r\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\r\n * THE SOFTWARE.\r\n * \r\n * THE SOFTWARE IS NOT PERMISSIBLE TO BE SOLD.\n */\n\n!function(t,e){ true?module.exports=e():0}(this,(function(){\"use strict\";function t(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,s)}return i}function e(e){for(var s=1;s<arguments.length;s++){var a=null!=arguments[s]?arguments[s]:{};s%2?t(Object(a),!0).forEach((function(t){i(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function i(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}const s=(t,e,i,s)=>(t=\"\"+t,e=\"\"+e,s&&(t=t.trim(),e=e.trim()),i?t==e:t.toLowerCase()==e.toLowerCase()),a=(t,e)=>t&&Array.isArray(t)&&t.map((t=>n(t,e)));function n(t,e){var i,s={};for(i in t)e.indexOf(i)<0&&(s[i]=t[i]);return s}function o(t){var e=document.createElement(\"div\");return t.replace(/\\&#?[0-9a-z]+;/gi,(function(t){return e.innerHTML=t,e.innerText}))}function r(t){return(new DOMParser).parseFromString(t.trim(),\"text/html\").body.firstElementChild}function l(t,e){for(e=e||\"previous\";t=t[e+\"Sibling\"];)if(3==t.nodeType)return t}function d(t){return\"string\"==typeof t?t.replace(/&/g,\"&amp;\").replace(/</g,\"&lt;\").replace(/>/g,\"&gt;\").replace(/\"/g,\"&quot;\").replace(/`|'/g,\"&#039;\"):t}function h(t){var e=Object.prototype.toString.call(t).split(\" \")[1].slice(0,-1);return t===Object(t)&&\"Array\"!=e&&\"Function\"!=e&&\"RegExp\"!=e&&\"HTMLUnknownElement\"!=e}function g(t,e,i){function s(t,e){for(var i in e)if(e.hasOwnProperty(i)){if(h(e[i])){h(t[i])?s(t[i],e[i]):t[i]=Object.assign({},e[i]);continue}if(Array.isArray(e[i])){t[i]=Object.assign([],e[i]);continue}t[i]=e[i]}}return t instanceof Object||(t={}),s(t,e),i&&s(t,i),t}function p(){const t=[],e={};for(let i of arguments)for(let s of i)h(s)?e[s.value]||(t.push(s),e[s.value]=1):t.includes(s)||t.push(s);return t}function c(t){return String.prototype.normalize?\"string\"==typeof t?t.normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g,\"\"):void 0:t}var u=()=>/(?=.*chrome)(?=.*android)/i.test(navigator.userAgent);function m(){return([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,(t=>(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16)))}function v(t){return t&&t.classList&&t.classList.contains(this.settings.classNames.tag)}var f={delimiters:\",\",pattern:null,tagTextProp:\"value\",maxTags:1/0,callbacks:{},addTagOnBlur:!0,onChangeAfterBlur:!0,duplicates:!1,whitelist:[],blacklist:[],enforceWhitelist:!1,userInput:!0,keepInvalidTags:!1,createInvalidTags:!0,mixTagsAllowedAfter:/,|\\.|\\:|\\s/,mixTagsInterpolator:[\"[[\",\"]]\"],backspace:!0,skipInvalid:!1,pasteAsTags:!0,editTags:{clicks:2,keepInvalid:!0},transformTag:()=>{},trim:!0,a11y:{focusableTags:!1},mixMode:{insertAfterTag:\" \"},autoComplete:{enabled:!0,rightKey:!1},classNames:{namespace:\"tagify\",mixMode:\"tagify--mix\",selectMode:\"tagify--select\",input:\"tagify__input\",focus:\"tagify--focus\",tagNoAnimation:\"tagify--noAnim\",tagInvalid:\"tagify--invalid\",tagNotAllowed:\"tagify--notAllowed\",scopeLoading:\"tagify--loading\",hasMaxTags:\"tagify--hasMaxTags\",hasNoTags:\"tagify--noTags\",empty:\"tagify--empty\",inputInvalid:\"tagify__input--invalid\",dropdown:\"tagify__dropdown\",dropdownWrapper:\"tagify__dropdown__wrapper\",dropdownHeader:\"tagify__dropdown__header\",dropdownFooter:\"tagify__dropdown__footer\",dropdownItem:\"tagify__dropdown__item\",dropdownItemActive:\"tagify__dropdown__item--active\",dropdownItemHidden:\"tagify__dropdown__item--hidden\",dropdownInital:\"tagify__dropdown--initial\",tag:\"tagify__tag\",tagText:\"tagify__tag-text\",tagX:\"tagify__tag__removeBtn\",tagLoading:\"tagify__tag--loading\",tagEditing:\"tagify__tag--editable\",tagFlash:\"tagify__tag--flash\",tagHide:\"tagify__tag--hide\"},dropdown:{classname:\"\",enabled:2,maxItems:10,searchKeys:[\"value\",\"searchBy\"],fuzzySearch:!0,caseSensitive:!1,accentedSearch:!0,includeSelectedTags:!1,highlightFirst:!1,closeOnSelect:!0,clearOnSelect:!0,position:\"all\",appendTarget:null},hooks:{beforeRemoveTag:()=>Promise.resolve(),beforePaste:()=>Promise.resolve(),suggestionClick:()=>Promise.resolve()}};function T(){this.dropdown={};for(let t in this._dropdown)this.dropdown[t]=\"function\"==typeof this._dropdown[t]?this._dropdown[t].bind(this):this._dropdown[t];this.dropdown.refs()}var w={refs(){this.DOM.dropdown=this.parseTemplate(\"dropdown\",[this.settings]),this.DOM.dropdown.content=this.DOM.dropdown.querySelector(\"[data-selector='tagify-suggestions-wrapper']\")},getHeaderRef(){return this.DOM.dropdown.querySelector(\"[data-selector='tagify-suggestions-header']\")},getFooterRef(){return this.DOM.dropdown.querySelector(\"[data-selector='tagify-suggestions-footer']\")},getAllSuggestionsRefs(){return[...this.DOM.dropdown.content.querySelectorAll(this.settings.classNames.dropdownItemSelector)]},show(t){var e,i,a,n=this.settings,o=\"mix\"==n.mode&&!n.enforceWhitelist,r=!n.whitelist||!n.whitelist.length,l=\"manual\"==n.dropdown.position;if(t=void 0===t?this.state.inputText:t,!(r&&!o&&!n.templates.dropdownItemNoMatch||!1===n.dropdown.enable||this.state.isLoading||this.settings.readonly)){if(clearTimeout(this.dropdownHide__bindEventsTimeout),this.suggestedListItems=this.dropdown.filterListItems(t),t&&!this.suggestedListItems.length&&(this.trigger(\"dropdown:noMatch\",t),n.templates.dropdownItemNoMatch&&(a=n.templates.dropdownItemNoMatch.call(this,{value:t}))),!a){if(this.suggestedListItems.length)t&&o&&!this.state.editing.scope&&!s(this.suggestedListItems[0].value,t)&&this.suggestedListItems.unshift({value:t});else{if(!t||!o||this.state.editing.scope)return this.input.autocomplete.suggest.call(this),void this.dropdown.hide();this.suggestedListItems=[{value:t}]}i=\"\"+(h(e=this.suggestedListItems[0])?e.value:e),n.autoComplete&&i&&0==i.indexOf(t)&&this.input.autocomplete.suggest.call(this,e)}this.dropdown.fill(a),n.dropdown.highlightFirst&&this.dropdown.highlightOption(this.DOM.dropdown.content.querySelector(n.classNames.dropdownItemSelector)),this.state.dropdown.visible||setTimeout(this.dropdown.events.binding.bind(this)),this.state.dropdown.visible=t||!0,this.state.dropdown.query=t,this.setStateSelection(),l||setTimeout((()=>{this.dropdown.position(),this.dropdown.render()})),setTimeout((()=>{this.trigger(\"dropdown:show\",this.DOM.dropdown)}))}},hide(t){var e=this.DOM,i=e.scope,s=e.dropdown,a=\"manual\"==this.settings.dropdown.position&&!t;if(s&&document.body.contains(s)&&!a)return window.removeEventListener(\"resize\",this.dropdown.position),this.dropdown.events.binding.call(this,!1),i.setAttribute(\"aria-expanded\",!1),s.parentNode.removeChild(s),setTimeout((()=>{this.state.dropdown.visible=!1}),100),this.state.dropdown.query=this.state.ddItemData=this.state.ddItemElm=this.state.selection=null,this.state.tag&&this.state.tag.value.length&&(this.state.flaggedTags[this.state.tag.baseOffset]=this.state.tag),this.trigger(\"dropdown:hide\",s),this},toggle(t){this.dropdown[this.state.dropdown.visible&&!t?\"hide\":\"show\"]()},render(){var t,e,i,s=(t=this.DOM.dropdown,(i=t.cloneNode(!0)).style.cssText=\"position:fixed; top:-9999px; opacity:0\",document.body.appendChild(i),e=i.clientHeight,i.parentNode.removeChild(i),e),a=this.settings;return\"number\"==typeof a.dropdown.enabled&&a.dropdown.enabled>=0?(this.DOM.scope.setAttribute(\"aria-expanded\",!0),document.body.contains(this.DOM.dropdown)||(this.DOM.dropdown.classList.add(a.classNames.dropdownInital),this.dropdown.position(s),a.dropdown.appendTarget.appendChild(this.DOM.dropdown),setTimeout((()=>this.DOM.dropdown.classList.remove(a.classNames.dropdownInital)))),this):this},fill(t){t=\"string\"==typeof t?t:this.dropdown.createListHTML(t||this.suggestedListItems);var e,i=this.settings.templates.dropdownContent.call(this,t);this.DOM.dropdown.content.innerHTML=(e=i)?e.replace(/\\>[\\r\\n ]+\\</g,\"><\").replace(/(<.*?>)|\\s+/g,((t,e)=>e||\" \")):\"\"},fillHeaderFooter(){this.settings.templates;var t=this.dropdown.filterListItems(this.state.dropdown.query),e=this.parseTemplate(\"dropdownHeader\",[t]),i=this.parseTemplate(\"dropdownFooter\",[t]),s=this.dropdown.getHeaderRef(),a=this.dropdown.getFooterRef();e&&s?.parentNode.replaceChild(e,s),i&&a?.parentNode.replaceChild(i,a)},refilter(t){t=t||this.state.dropdown.query||\"\",this.suggestedListItems=this.dropdown.filterListItems(t),this.dropdown.fill(),this.suggestedListItems.length||this.dropdown.hide(),this.trigger(\"dropdown:updated\",this.DOM.dropdown)},position(t){var e=this.settings.dropdown;if(\"manual\"!=e.position){var i,s,a,n,o,r,l=this.DOM.dropdown,d=e.placeAbove,h=e.appendTarget===document.body,g=h?window.pageYOffset:e.appendTarget.scrollTop,p=document.fullscreenElement||document.webkitFullscreenElement||document.documentElement,c=p.clientHeight,u=Math.max(p.clientWidth||0,window.innerWidth||0)>480?e.position:\"all\",m=this.DOM[\"input\"==u?\"input\":\"scope\"];if(t=t||l.clientHeight,this.state.dropdown.visible){if(\"text\"==u?(a=(i=this.getCaretGlobalPosition()).bottom,s=i.top,n=i.left,o=\"auto\"):(r=function(t){for(var e=0,i=0;t&&t!=p;)e+=t.offsetLeft||0,i+=t.offsetTop||0,t=t.parentNode;return{left:e,top:i}}(e.appendTarget),s=(i=m.getBoundingClientRect()).top-r.top,a=i.bottom-1-r.top,n=i.left-r.left,o=i.width+\"px\"),!h){let t=function(){for(var t=0,i=e.appendTarget.parentNode;i;)t+=i.scrollTop||0,i=i.parentNode;return t}();s+=t,a+=t}s=Math.floor(s),a=Math.ceil(a),d=void 0===d?c-i.bottom<t:d,l.style.cssText=\"left:\"+(n+window.pageXOffset)+\"px; width:\"+o+\";\"+(d?\"top: \"+(s+g)+\"px\":\"top: \"+(a+g)+\"px\"),l.setAttribute(\"placement\",d?\"top\":\"bottom\"),l.setAttribute(\"position\",u)}}},events:{binding(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var e=this.dropdown.events.callbacks,i=this.listeners.dropdown=this.listeners.dropdown||{position:this.dropdown.position.bind(this,null),onKeyDown:e.onKeyDown.bind(this),onMouseOver:e.onMouseOver.bind(this),onMouseLeave:e.onMouseLeave.bind(this),onClick:e.onClick.bind(this),onScroll:e.onScroll.bind(this)},s=t?\"addEventListener\":\"removeEventListener\";\"manual\"!=this.settings.dropdown.position&&(document[s](\"scroll\",i.position,!0),window[s](\"resize\",i.position),window[s](\"keydown\",i.onKeyDown)),this.DOM.dropdown[s](\"mouseover\",i.onMouseOver),this.DOM.dropdown[s](\"mouseleave\",i.onMouseLeave),this.DOM.dropdown[s](\"mousedown\",i.onClick),this.DOM.dropdown.content[s](\"scroll\",i.onScroll)},callbacks:{onKeyDown(t){if(this.state.hasFocus&&!this.state.composing){var e=this.DOM.dropdown.querySelector(this.settings.classNames.dropdownItemActiveSelector),i=this.dropdown.getSuggestionDataByNode(e);switch(t.key){case\"ArrowDown\":case\"ArrowUp\":case\"Down\":case\"Up\":t.preventDefault();var s=this.dropdown.getAllSuggestionsRefs(),a=\"ArrowUp\"==t.key||\"Up\"==t.key;e&&(e=this.dropdown.getNextOrPrevOption(e,!a)),e&&e.matches(this.settings.classNames.dropdownItemSelector)||(e=s[a?s.length-1:0]),i=this.dropdown.getSuggestionDataByNode(e),this.dropdown.highlightOption(e,!0);break;case\"Escape\":case\"Esc\":this.dropdown.hide();break;case\"ArrowRight\":if(this.state.actions.ArrowLeft)return;case\"Tab\":if(\"mix\"!=this.settings.mode&&e&&!this.settings.autoComplete.rightKey&&!this.state.editing){t.preventDefault();var n=this.dropdown.getMappedValue(i);return this.input.autocomplete.set.call(this,n),!1}return!0;case\"Enter\":t.preventDefault(),this.settings.hooks.suggestionClick(t,{tagify:this,tagData:i,suggestionElm:e}).then((()=>{if(e)return this.dropdown.selectOption(e),e=this.dropdown.getNextOrPrevOption(e,!a),void this.dropdown.highlightOption(e);this.dropdown.hide(),\"mix\"!=this.settings.mode&&this.addTags(this.state.inputText.trim(),!0)})).catch((t=>t));break;case\"Backspace\":{if(\"mix\"==this.settings.mode||this.state.editing.scope)return;const t=this.input.raw.call(this);\"\"!=t&&8203!=t.charCodeAt(0)||(!0===this.settings.backspace?this.removeTags():\"edit\"==this.settings.backspace&&setTimeout(this.editTag.bind(this),0))}}}},onMouseOver(t){var e=t.target.closest(this.settings.classNames.dropdownItemSelector);e&&this.dropdown.highlightOption(e)},onMouseLeave(t){this.dropdown.highlightOption()},onClick(t){if(0==t.button&&t.target!=this.DOM.dropdown&&t.target!=this.DOM.dropdown.content){var e=t.target.closest(this.settings.classNames.dropdownItemSelector),i=this.dropdown.getSuggestionDataByNode(e);this.state.actions.selectOption=!0,setTimeout((()=>this.state.actions.selectOption=!1),50),this.settings.hooks.suggestionClick(t,{tagify:this,tagData:i,suggestionElm:e}).then((()=>{e?this.dropdown.selectOption(e,t):this.dropdown.hide()})).catch((t=>console.warn(t)))}},onScroll(t){var e=t.target,i=e.scrollTop/(e.scrollHeight-e.parentNode.clientHeight)*100;this.trigger(\"dropdown:scroll\",{percentage:Math.round(i)})}}},getSuggestionDataByNode(t){var e=t&&t.getAttribute(\"value\");return this.suggestedListItems.find((t=>t.value==e))||null},getNextOrPrevOption(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var i=this.dropdown.getAllSuggestionsRefs(),s=i.findIndex((e=>e===t));return e?i[s+1]:i[s-1]},highlightOption(t,e){var i,s=this.settings.classNames.dropdownItemActive;if(this.state.ddItemElm&&(this.state.ddItemElm.classList.remove(s),this.state.ddItemElm.removeAttribute(\"aria-selected\")),!t)return this.state.ddItemData=null,this.state.ddItemElm=null,void this.input.autocomplete.suggest.call(this);i=this.dropdown.getSuggestionDataByNode(t),this.state.ddItemData=i,this.state.ddItemElm=t,t.classList.add(s),t.setAttribute(\"aria-selected\",!0),e&&(t.parentNode.scrollTop=t.clientHeight+t.offsetTop-t.parentNode.clientHeight),this.settings.autoComplete&&(this.input.autocomplete.suggest.call(this,i),this.dropdown.position())},selectOption(t,e){var i=this.settings.dropdown,s=i.clearOnSelect,a=i.closeOnSelect;if(!t)return this.addTags(this.state.inputText,!0),void(a&&this.dropdown.hide());e=e||{};var n=t.getAttribute(\"value\"),o=\"noMatch\"==n,r=this.suggestedListItems.find((t=>(t.value||t)==n));this.trigger(\"dropdown:select\",{data:r,elm:t,event:e}),n&&(r||o)?(this.state.editing?this.onEditTagDone(null,g({__isValid:!0},this.normalizeTags([r])[0])):this[\"mix\"==this.settings.mode?\"addMixTags\":\"addTags\"]([r||this.input.raw.call(this)],s),this.DOM.input.parentNode&&(setTimeout((()=>{this.DOM.input.focus(),this.toggleFocusClass(!0),this.setRangeAtStartEnd(!1)})),a&&setTimeout(this.dropdown.hide.bind(this)),t.addEventListener(\"transitionend\",(()=>{this.dropdown.fillHeaderFooter(),setTimeout((()=>t.remove()),100)}),{once:!0}),t.classList.add(this.settings.classNames.dropdownItemHidden))):a&&setTimeout(this.dropdown.hide.bind(this))},selectAll(t){this.suggestedListItems.length=0,this.dropdown.hide(),this.dropdown.filterListItems(\"\");var e=this.dropdown.filterListItems(\"\");return t||(e=this.state.dropdown.suggestions),this.addTags(e,!0),this},filterListItems(t,e){var i,s,a,n,o,r=this.settings,l=r.dropdown,d=(e=e||{},[]),g=[],p=r.whitelist,u=l.maxItems>=0?l.maxItems:1/0,m=l.searchKeys,v=0;if(!(t=\"select\"==r.mode&&this.value.length&&this.value[0][r.tagTextProp]==t?\"\":t)||!m.length)return d=l.includeSelectedTags?p:p.filter((t=>!this.isTagDuplicate(h(t)?t.value:t))),this.state.dropdown.suggestions=d,d.slice(0,u);function f(t,e){return e.toLowerCase().split(\" \").every((e=>t.includes(e.toLowerCase())))}for(o=l.caseSensitive?\"\"+t:(\"\"+t).toLowerCase();v<p.length;v++){let t,r;i=p[v]instanceof Object?p[v]:{value:p[v]};let u=!Object.keys(i).some((t=>m.includes(t)))?[\"value\"]:m;l.fuzzySearch&&!e.exact?(a=u.reduce(((t,e)=>t+\" \"+(i[e]||\"\")),\"\").toLowerCase().trim(),l.accentedSearch&&(a=c(a),o=c(o)),t=0==a.indexOf(o),r=a===o,s=f(a,o)):(t=!0,s=u.some((t=>{var s=\"\"+(i[t]||\"\");return l.accentedSearch&&(s=c(s),o=c(o)),l.caseSensitive||(s=s.toLowerCase()),r=s===o,e.exact?s===o:0==s.indexOf(o)}))),n=!l.includeSelectedTags&&this.isTagDuplicate(h(i)?i.value:i),s&&!n&&(r&&t?g.push(i):\"startsWith\"==l.sortby&&t?d.unshift(i):d.push(i))}return this.state.dropdown.suggestions=g.concat(d),\"function\"==typeof l.sortby?l.sortby(g.concat(d),o):g.concat(d).slice(0,u)},getMappedValue(t){var e=this.settings.dropdown.mapValueTo;return e?\"function\"==typeof e?e(t):t[e]||t.value:t.value},createListHTML(t){return g([],t).map(((t,i)=>{\"string\"!=typeof t&&\"number\"!=typeof t||(t={value:t});var s=this.dropdown.getMappedValue(t);return s=\"string\"==typeof s?d(s):s,this.settings.templates.dropdownItem.apply(this,[e(e({},t),{},{mappedValue:s}),this])})).join(\"\")}};const b=\"@yaireo/tagify/\";var y,x={empty:\"empty\",exceed:\"number of tags exceeded\",pattern:\"pattern mismatch\",duplicate:\"already exists\",notAllowed:\"not allowed\"},D={wrapper:(t,e)=>`<tags class=\"${e.classNames.namespace} ${e.mode?`${e.classNames[e.mode+\"Mode\"]}`:\"\"} ${t.className}\"\\n                    ${e.readonly?\"readonly\":\"\"}\\n                    ${e.disabled?\"disabled\":\"\"}\\n                    ${e.required?\"required\":\"\"}\\n                    ${\"select\"===e.mode?\"spellcheck='false'\":\"\"}\\n                    tabIndex=\"-1\">\\n            <span ${!e.readonly&&e.userInput?\"contenteditable\":\"\"} tabIndex=\"0\" data-placeholder=\"${e.placeholder||\"&#8203;\"}\" aria-placeholder=\"${e.placeholder||\"\"}\"\\n                class=\"${e.classNames.input}\"\\n                role=\"textbox\"\\n                aria-autocomplete=\"both\"\\n                aria-multiline=\"${\"mix\"==e.mode}\"></span>\\n                &#8203;\\n        </tags>`,tag(t,e){let i=e.settings;return`<tag title=\"${t.title||t.value}\"\\n                    contenteditable='false'\\n                    spellcheck='false'\\n                    tabIndex=\"${i.a11y.focusableTags?0:-1}\"\\n                    class=\"${i.classNames.tag} ${t.class||\"\"}\"\\n                    ${this.getAttributes(t)}>\\n            <x title='' class=\"${i.classNames.tagX}\" role='button' aria-label='remove tag'></x>\\n            <div>\\n                <span class=\"${i.classNames.tagText}\">${t[i.tagTextProp]||t.value}</span>\\n            </div>\\n        </tag>`},dropdown(t){var e=t.dropdown,i=\"manual\"==e.position,s=`${t.classNames.dropdown}`;return`<div class=\"${i?\"\":s} ${e.classname}\" role=\"listbox\" aria-labelledby=\"dropdown\">\\n                    <div data-selector='tagify-suggestions-wrapper' class=\"${t.classNames.dropdownWrapper}\"></div>\\n                </div>`},dropdownContent(t){var e=this.settings,i=this.state.dropdown.suggestions;return`\\n            ${e.templates.dropdownHeader.call(this,i)}\\n            ${t}\\n            ${e.templates.dropdownFooter.call(this,i)}\\n        `},dropdownItem(t){return`<div ${this.getAttributes(t)}\\n                    class='${this.settings.classNames.dropdownItem} ${t.class?t.class:\"\"}'\\n                    tabindex=\"0\"\\n                    role=\"option\">${t.mappedValue||t.value}</div>`},dropdownHeader(t){return`<header data-selector='tagify-suggestions-header' class=\"${this.settings.classNames.dropdownHeader}\"></header>`},dropdownFooter(t){var e=t.length-this.settings.dropdown.maxItems;return e>0?`<footer data-selector='tagify-suggestions-footer' class=\"${this.settings.classNames.dropdownFooter}\">\\n                ${e} more items. Refine your search.\\n            </footer>`:\"\"},dropdownItemNoMatch:null};var O={customBinding(){this.customEventsList.forEach((t=>{this.on(t,this.settings.callbacks[t])}))},binding(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];var e,i=this.events.callbacks,s=t?\"addEventListener\":\"removeEventListener\";if(!this.state.mainEvents||!t){for(var a in this.state.mainEvents=t,t&&!this.listeners.main&&(this.events.bindGlobal.call(this),this.settings.isJQueryPlugin&&jQuery(this.DOM.originalInput).on(\"tagify.removeAllTags\",this.removeAllTags.bind(this))),e=this.listeners.main=this.listeners.main||{focus:[\"input\",i.onFocusBlur.bind(this)],keydown:[\"input\",i.onKeydown.bind(this)],click:[\"scope\",i.onClickScope.bind(this)],dblclick:[\"scope\",i.onDoubleClickScope.bind(this)],paste:[\"input\",i.onPaste.bind(this)],drop:[\"input\",i.onDrop.bind(this)],compositionstart:[\"input\",i.onCompositionStart.bind(this)],compositionend:[\"input\",i.onCompositionEnd.bind(this)]})this.DOM[e[a][0]][s](a,e[a][1]);clearInterval(this.listeners.main.originalInputValueObserverInterval),this.listeners.main.originalInputValueObserverInterval=setInterval(i.observeOriginalInputValue.bind(this),500);var n=this.listeners.main.inputMutationObserver||new MutationObserver(i.onInputDOMChange.bind(this));n&&n.disconnect(),\"mix\"==this.settings.mode&&n.observe(this.DOM.input,{childList:!0})}},bindGlobal(t){var e,i=this.events.callbacks,s=t?\"removeEventListener\":\"addEventListener\";if(t||!this.listeners.global)for(e of(this.listeners.global=this.listeners&&this.listeners.global||[{type:this.isIE?\"keydown\":\"input\",target:this.DOM.input,cb:i[this.isIE?\"onInputIE\":\"onInput\"].bind(this)},{type:\"keydown\",target:window,cb:i.onWindowKeyDown.bind(this)},{type:\"blur\",target:this.DOM.input,cb:i.onFocusBlur.bind(this)}],this.listeners.global))e.target[s](e.type,e.cb)},unbindGlobal(){this.events.bindGlobal.call(this,!0)},callbacks:{onFocusBlur(t){var e=this.settings,i=t.target?this.trim(t.target.textContent):\"\",s=this.value?.[0]?.[e.tagTextProp],a=t.type,n=e.dropdown.enabled>=0,o={relatedTarget:t.relatedTarget},r=this.state.actions.selectOption&&(n||!e.dropdown.closeOnSelect),l=this.state.actions.addNew&&n,d=t.relatedTarget&&v.call(this,t.relatedTarget)&&this.DOM.scope.contains(t.relatedTarget);if(\"blur\"==a){if(t.relatedTarget===this.DOM.scope)return this.dropdown.hide(),void this.DOM.input.focus();this.postUpdate(),e.onChangeAfterBlur&&this.triggerChangeEvent()}if(!r&&!l)if(this.state.hasFocus=\"focus\"==a&&+new Date,this.toggleFocusClass(this.state.hasFocus),\"mix\"!=e.mode){if(\"focus\"==a)return this.trigger(\"focus\",o),void(0!==e.dropdown.enabled&&e.userInput||this.dropdown.show(this.value.length?\"\":void 0));\"blur\"==a&&(this.trigger(\"blur\",o),this.loading(!1),\"select\"==e.mode&&(d&&(this.removeTags(),i=\"\"),s===i&&(i=\"\")),i&&!this.state.actions.selectOption&&e.addTagOnBlur&&this.addTags(i,!0)),this.DOM.input.removeAttribute(\"style\"),this.dropdown.hide()}else\"focus\"==a?this.trigger(\"focus\",o):\"blur\"==t.type&&(this.trigger(\"blur\",o),this.loading(!1),this.dropdown.hide(),this.state.dropdown.visible=void 0,this.setStateSelection())},onCompositionStart(t){this.state.composing=!0},onCompositionEnd(t){this.state.composing=!1},onWindowKeyDown(t){var e,i=document.activeElement;if(v.call(this,i)&&this.DOM.scope.contains(document.activeElement))switch(e=i.nextElementSibling,t.key){case\"Backspace\":this.settings.readonly||(this.removeTags(i),(e||this.DOM.input).focus());break;case\"Enter\":setTimeout(this.editTag.bind(this),0,i)}},onKeydown(t){var e=this.settings;if(!this.state.composing&&e.userInput){\"select\"==e.mode&&e.enforceWhitelist&&this.value.length&&\"Tab\"!=t.key&&t.preventDefault();var i=this.trim(t.target.textContent);if(this.trigger(\"keydown\",{event:t}),\"mix\"==e.mode){switch(t.key){case\"Left\":case\"ArrowLeft\":this.state.actions.ArrowLeft=!0;break;case\"Delete\":case\"Backspace\":if(this.state.editing)return;var s,a,n,r=document.getSelection(),d=\"Delete\"==t.key&&r.anchorOffset==(r.anchorNode.length||0),h=r.anchorNode.previousSibling,g=1==r.anchorNode.nodeType||!r.anchorOffset&&h&&1==h.nodeType&&r.anchorNode.previousSibling,p=o(this.DOM.input.innerHTML),c=this.getTagElms();if(\"edit\"==e.backspace&&g)return s=1==r.anchorNode.nodeType?null:r.anchorNode.previousElementSibling,setTimeout(this.editTag.bind(this),0,s),void t.preventDefault();if(u()&&g)return n=l(g),g.hasAttribute(\"readonly\")||g.remove(),this.DOM.input.focus(),void setTimeout((()=>{this.placeCaretAfterNode(n),this.DOM.input.click()}));if(\"BR\"==r.anchorNode.nodeName)return;if((d||g)&&1==r.anchorNode.nodeType?a=0==r.anchorOffset?d?c[0]:null:c[Math.min(c.length,r.anchorOffset)-1]:d?a=r.anchorNode.nextElementSibling:g&&(a=g),3==r.anchorNode.nodeType&&!r.anchorNode.nodeValue&&r.anchorNode.previousElementSibling&&t.preventDefault(),(g||d)&&!e.backspace)return void t.preventDefault();if(\"Range\"!=r.type&&!r.anchorOffset&&r.anchorNode==this.DOM.input&&\"Delete\"!=t.key)return void t.preventDefault();if(\"Range\"!=r.type&&a&&a.hasAttribute(\"readonly\"))return void this.placeCaretAfterNode(l(a));clearTimeout(y),y=setTimeout((()=>{var t=document.getSelection(),e=o(this.DOM.input.innerHTML),i=!d&&t.anchorNode.previousSibling;if(e.length>=p.length&&i)if(v.call(this,i)&&!i.hasAttribute(\"readonly\")){if(this.removeTags(i),this.fixFirefoxLastTagNoCaret(),2==this.DOM.input.children.length&&\"BR\"==this.DOM.input.children[1].tagName)return this.DOM.input.innerHTML=\"\",this.value.length=0,!0}else i.remove();this.value=[].map.call(c,((t,e)=>{var i=this.tagData(t);if(t.parentNode||i.readonly)return i;this.trigger(\"remove\",{tag:t,index:e,data:i})})).filter((t=>t))}),20)}return!0}switch(t.key){case\"Backspace\":\"select\"==e.mode&&e.enforceWhitelist&&this.value.length?this.removeTags():this.state.dropdown.visible&&\"manual\"!=e.dropdown.position||\"\"!=t.target.textContent&&8203!=i.charCodeAt(0)||(!0===e.backspace?this.removeTags():\"edit\"==e.backspace&&setTimeout(this.editTag.bind(this),0));break;case\"Esc\":case\"Escape\":if(this.state.dropdown.visible)return;t.target.blur();break;case\"Down\":case\"ArrowDown\":this.state.dropdown.visible||this.dropdown.show();break;case\"ArrowRight\":{let t=this.state.inputSuggestion||this.state.ddItemData;if(t&&e.autoComplete.rightKey)return void this.addTags([t],!0);break}case\"Tab\":{let s=\"select\"==e.mode;if(!i||s)return!0;t.preventDefault()}case\"Enter\":if(this.state.dropdown.visible&&\"manual\"!=e.dropdown.position)return;t.preventDefault(),setTimeout((()=>{this.state.actions.selectOption||this.addTags(i,!0)}))}}},onInput(t){this.postUpdate();var e=this.settings;if(\"mix\"==e.mode)return this.events.callbacks.onMixTagsInput.call(this,t);var i=this.input.normalize.call(this),s=i.length>=e.dropdown.enabled,a={value:i,inputElm:this.DOM.input},n=this.validateTag({value:i});\"select\"==e.mode&&this.toggleScopeValidation(n),a.isValid=n,this.state.inputText!=i&&(this.input.set.call(this,i,!1),-1!=i.search(e.delimiters)?this.addTags(i)&&this.input.set.call(this):e.dropdown.enabled>=0&&this.dropdown[s?\"show\":\"hide\"](i),this.trigger(\"input\",a))},onMixTagsInput(t){var e,i,s,a,n,o,r,l,d=this.settings,h=this.value.length,p=this.getTagElms(),c=document.createDocumentFragment(),m=window.getSelection().getRangeAt(0),v=[].map.call(p,(t=>this.tagData(t).value));if(\"deleteContentBackward\"==t.inputType&&u()&&this.events.callbacks.onKeydown.call(this,{target:t.target,key:\"Backspace\"}),this.value.slice().forEach((t=>{t.readonly&&!v.includes(t.value)&&c.appendChild(this.createTagElem(t))})),c.childNodes.length&&(m.insertNode(c),this.setRangeAtStartEnd(!1,c.lastChild)),p.length!=h)return this.value=[].map.call(this.getTagElms(),(t=>this.tagData(t))),void this.update({withoutChangeEvent:!0});if(this.hasMaxTags())return!0;if(window.getSelection&&(o=window.getSelection()).rangeCount>0&&3==o.anchorNode.nodeType){if((m=o.getRangeAt(0).cloneRange()).collapse(!0),m.setStart(o.focusNode,0),s=(e=m.toString().slice(0,m.endOffset)).split(d.pattern).length-1,(i=e.match(d.pattern))&&(a=e.slice(e.lastIndexOf(i[i.length-1]))),a){if(this.state.actions.ArrowLeft=!1,this.state.tag={prefix:a.match(d.pattern)[0],value:a.replace(d.pattern,\"\")},this.state.tag.baseOffset=o.baseOffset-this.state.tag.value.length,l=this.state.tag.value.match(d.delimiters))return this.state.tag.value=this.state.tag.value.replace(d.delimiters,\"\"),this.state.tag.delimiters=l[0],this.addTags(this.state.tag.value,d.dropdown.clearOnSelect),void this.dropdown.hide();n=this.state.tag.value.length>=d.dropdown.enabled;try{r=(r=this.state.flaggedTags[this.state.tag.baseOffset]).prefix==this.state.tag.prefix&&r.value[0]==this.state.tag.value[0],this.state.flaggedTags[this.state.tag.baseOffset]&&!this.state.tag.value&&delete this.state.flaggedTags[this.state.tag.baseOffset]}catch(t){}(r||s<this.state.mixMode.matchedPatternCount)&&(n=!1)}else this.state.flaggedTags={};this.state.mixMode.matchedPatternCount=s}setTimeout((()=>{this.update({withoutChangeEvent:!0}),this.trigger(\"input\",g({},this.state.tag,{textContent:this.DOM.input.textContent})),this.state.tag&&this.dropdown[n?\"show\":\"hide\"](this.state.tag.value)}),10)},onInputIE(t){var e=this;setTimeout((function(){e.events.callbacks.onInput.call(e,t)}))},observeOriginalInputValue(){this.DOM.originalInput.parentNode||this.destroy(),this.DOM.originalInput.value!=this.DOM.originalInput.tagifyValue&&this.loadOriginalValues()},onClickScope(t){var e=this.settings,i=t.target.closest(\".\"+e.classNames.tag),s=+new Date-this.state.hasFocus;if(t.target!=this.DOM.scope){if(!t.target.classList.contains(e.classNames.tagX))return i?(this.trigger(\"click\",{tag:i,index:this.getNodeIndex(i),data:this.tagData(i),event:t}),void(1!==e.editTags&&1!==e.editTags.clicks||this.events.callbacks.onDoubleClickScope.call(this,t))):void(t.target==this.DOM.input&&(\"mix\"==e.mode&&this.fixFirefoxLastTagNoCaret(),s>500)?this.state.dropdown.visible?this.dropdown.hide():0===e.dropdown.enabled&&\"mix\"!=e.mode&&this.dropdown.show(this.value.length?\"\":void 0):\"select\"==e.mode&&!this.state.dropdown.visible&&this.dropdown.show());this.removeTags(t.target.parentNode)}else this.state.hasFocus||this.DOM.input.focus()},onPaste(t){t.preventDefault();var e,i,s=this.settings;if(\"select\"==s.mode&&s.enforceWhitelist||!s.userInput)return!1;s.readonly||(e=t.clipboardData||window.clipboardData,i=e.getData(\"Text\"),s.hooks.beforePaste(t,{tagify:this,pastedText:i,clipboardData:e}).then((e=>{void 0===e&&(e=i),e&&(this.injectAtCaret(e,window.getSelection().getRangeAt(0)),\"mix\"==this.settings.mode?this.events.callbacks.onMixTagsInput.call(this,t):this.settings.pasteAsTags?this.addTags(this.state.inputText+e,!0):this.state.inputText=e)})).catch((t=>t)))},onDrop(t){t.preventDefault()},onEditTagInput(t,e){var i=t.closest(\".\"+this.settings.classNames.tag),s=this.getNodeIndex(i),a=this.tagData(i),n=this.input.normalize.call(this,t),o={[this.settings.tagTextProp]:n,__tagId:a.__tagId},r=this.validateTag(o);this.editTagChangeDetected(g(a,o))||!0!==t.originalIsValid||(r=!0),i.classList.toggle(this.settings.classNames.tagInvalid,!0!==r),a.__isValid=r,i.title=!0===r?a.title||a.value:r,n.length>=this.settings.dropdown.enabled&&(this.state.editing&&(this.state.editing.value=n),this.dropdown.show(n)),this.trigger(\"edit:input\",{tag:i,index:s,data:g({},this.value[s],{newValue:n}),event:e})},onEditTagFocus(t){this.state.editing={scope:t,input:t.querySelector(\"[contenteditable]\")}},onEditTagBlur(t){if(this.state.hasFocus||this.toggleFocusClass(),this.DOM.scope.contains(t)){var e,i,s=this.settings,a=t.closest(\".\"+s.classNames.tag),n=this.input.normalize.call(this,t),o=this.tagData(a),r=o.__originalData,l=this.editTagChangeDetected(o),d=this.validateTag({[s.tagTextProp]:n,__tagId:o.__tagId});if(n)if(l){if(e=this.hasMaxTags(),i=g({},r,{[s.tagTextProp]:this.trim(n),__isValid:d}),s.transformTag.call(this,i,r),!0!==(d=(!e||!0===r.__isValid)&&this.validateTag(i))){if(this.trigger(\"invalid\",{data:i,tag:a,message:d}),s.editTags.keepInvalid)return;s.keepInvalidTags?i.__isValid=d:i=r}else s.keepInvalidTags&&(delete i.title,delete i[\"aria-invalid\"],delete i.class);this.onEditTagDone(a,i)}else this.onEditTagDone(a,r);else this.onEditTagDone(a)}},onEditTagkeydown(t,e){if(!this.state.composing)switch(this.trigger(\"edit:keydown\",{event:t}),t.key){case\"Esc\":case\"Escape\":e.parentNode.replaceChild(e.__tagifyTagData.__originalHTML,e),this.state.editing=!1;case\"Enter\":case\"Tab\":t.preventDefault(),t.target.blur()}},onDoubleClickScope(t){var e,i,s=t.target.closest(\".\"+this.settings.classNames.tag),a=this.tagData(s),n=this.settings;s&&n.userInput&&!1!==a.editable&&(e=s.classList.contains(this.settings.classNames.tagEditing),i=s.hasAttribute(\"readonly\"),\"select\"==n.mode||n.readonly||e||i||!this.settings.editTags||this.editTag(s),this.toggleFocusClass(!0),this.trigger(\"dblclick\",{tag:s,index:this.getNodeIndex(s),data:this.tagData(s)}))},onInputDOMChange(t){t.forEach((t=>{t.addedNodes.forEach((t=>{if(\"<div><br></div>\"==t.outerHTML)t.replaceWith(document.createElement(\"br\"));else if(1==t.nodeType&&t.querySelector(this.settings.classNames.tagSelector)){let e=document.createTextNode(\"\");3==t.childNodes[0].nodeType&&\"BR\"!=t.previousSibling.nodeName&&(e=document.createTextNode(\"\\n\")),t.replaceWith(e,...[...t.childNodes].slice(0,-1)),this.placeCaretAfterNode(e)}else if(v.call(this,t)&&(3!=t.previousSibling?.nodeType||t.previousSibling.textContent||t.previousSibling.remove(),t.previousSibling&&\"BR\"==t.previousSibling.nodeName)){t.previousSibling.replaceWith(\"\\n​\");let e=t.nextSibling,i=\"\";for(;e;)i+=e.textContent,e=e.nextSibling;i.trim()&&this.placeCaretAfterNode(t.previousSibling)}})),t.removedNodes.forEach((t=>{t&&\"BR\"==t.nodeName&&v.call(this,e)&&(this.removeTags(e),this.fixFirefoxLastTagNoCaret())}))}));var e=this.DOM.input.lastChild;e&&\"\"==e.nodeValue&&e.remove(),e&&\"BR\"==e.nodeName||this.DOM.input.appendChild(document.createElement(\"br\"))}}};function M(t,e){if(!t){console.warn(\"Tagify:\",\"input element not found\",t);const e=new Proxy(this,{get:()=>()=>e});return e}if(t.__tagify)return console.warn(\"Tagify: \",\"input element is already Tagified - Same instance is returned.\",t),t.__tagify;var i;g(this,function(t){var e=document.createTextNode(\"\");function i(t,i,s){s&&i.split(/\\s+/g).forEach((i=>e[t+\"EventListener\"].call(e,i,s)))}return{off(t,e){return i(\"remove\",t,e),this},on(t,e){return e&&\"function\"==typeof e&&i(\"add\",t,e),this},trigger(i,s,a){var n;if(a=a||{cloneData:!0},i)if(t.settings.isJQueryPlugin)\"remove\"==i&&(i=\"removeTag\"),jQuery(t.DOM.originalInput).triggerHandler(i,[s]);else{try{var o=\"object\"==typeof s?s:{value:s};if((o=a.cloneData?g({},o):o).tagify=this,s.event&&(o.event=this.cloneEvent(s.event)),s instanceof Object)for(var r in s)s[r]instanceof HTMLElement&&(o[r]=s[r]);n=new CustomEvent(i,{detail:o})}catch(t){console.warn(t)}e.dispatchEvent(n)}}}}(this)),this.isFirefox=\"undefined\"!=typeof InstallTrigger,this.isIE=window.document.documentMode,e=e||{},this.getPersistedData=(i=e.id,t=>{let e,s=\"/\"+t;if(1==localStorage.getItem(b+i+\"/v\",1))try{e=JSON.parse(localStorage[b+i+s])}catch(t){}return e}),this.setPersistedData=(t=>t?(localStorage.setItem(b+t+\"/v\",1),(e,i)=>{let s=\"/\"+i,a=JSON.stringify(e);e&&i&&(localStorage.setItem(b+t+s,a),dispatchEvent(new Event(\"storage\")))}):()=>{})(e.id),this.clearPersistedData=(t=>e=>{const i=b+\"/\"+t+\"/\";if(e)localStorage.removeItem(i+e);else for(let t in localStorage)t.includes(i)&&localStorage.removeItem(t)})(e.id),this.applySettings(t,e),this.state={inputText:\"\",editing:!1,composing:!1,actions:{},mixMode:{},dropdown:{},flaggedTags:{}},this.value=[],this.listeners={},this.DOM={},this.build(t),T.call(this),this.getCSSVars(),this.loadOriginalValues(),this.events.customBinding.call(this),this.events.binding.call(this),t.autofocus&&this.DOM.input.focus(),t.__tagify=this}return M.prototype={_dropdown:w,helpers:{sameStr:s,removeCollectionProp:a,omit:n,isObject:h,parseHTML:r,escapeHTML:d,extend:g,concatWithoutDups:p,getUID:m,isNodeTag:v},customEventsList:[\"change\",\"add\",\"remove\",\"invalid\",\"input\",\"click\",\"keydown\",\"focus\",\"blur\",\"edit:input\",\"edit:beforeUpdate\",\"edit:updated\",\"edit:start\",\"edit:keydown\",\"dropdown:show\",\"dropdown:hide\",\"dropdown:select\",\"dropdown:updated\",\"dropdown:noMatch\",\"dropdown:scroll\"],dataProps:[\"__isValid\",\"__removed\",\"__originalData\",\"__originalHTML\",\"__tagId\"],trim(t){return this.settings.trim&&t&&\"string\"==typeof t?t.trim():t},parseHTML:r,templates:D,parseTemplate(t,e){return t=this.settings.templates[t]||t,this.parseHTML(t.apply(this,e))},set whitelist(t){const e=t&&Array.isArray(t);this.settings.whitelist=e?t:[],this.setPersistedData(e?t:[],\"whitelist\")},get whitelist(){return this.settings.whitelist},generateClassSelectors(t){for(let e in t){let i=e;Object.defineProperty(t,i+\"Selector\",{get(){return\".\"+this[i].split(\" \")[0]}})}},applySettings(t,i){f.templates=this.templates;var s=this.settings=g({},f,i);if(s.disabled=t.hasAttribute(\"disabled\"),s.readonly=s.readonly||t.hasAttribute(\"readonly\"),s.placeholder=d(t.getAttribute(\"placeholder\")||s.placeholder||\"\"),s.required=t.hasAttribute(\"required\"),this.generateClassSelectors(s.classNames),void 0===s.dropdown.includeSelectedTags&&(s.dropdown.includeSelectedTags=s.duplicates),this.isIE&&(s.autoComplete=!1),[\"whitelist\",\"blacklist\"].forEach((e=>{var i=t.getAttribute(\"data-\"+e);i&&(i=i.split(s.delimiters))instanceof Array&&(s[e]=i)})),\"autoComplete\"in i&&!h(i.autoComplete)&&(s.autoComplete=f.autoComplete,s.autoComplete.enabled=i.autoComplete),\"mix\"==s.mode&&(s.autoComplete.rightKey=!0,s.delimiters=i.delimiters||null,s.tagTextProp&&!s.dropdown.searchKeys.includes(s.tagTextProp)&&s.dropdown.searchKeys.push(s.tagTextProp)),t.pattern)try{s.pattern=new RegExp(t.pattern)}catch(t){}if(s.delimiters){s._delimiters=s.delimiters;try{s.delimiters=new RegExp(this.settings.delimiters,\"g\")}catch(t){}}s.disabled&&(s.userInput=!1),this.TEXTS=e(e({},x),s.texts||{}),\"select\"!=s.mode&&s.userInput||(s.dropdown.enabled=0),s.dropdown.appendTarget=i.dropdown&&i.dropdown.appendTarget?i.dropdown.appendTarget:document.body;let a=this.getPersistedData(\"whitelist\");Array.isArray(a)&&(this.whitelist=Array.isArray(s.whitelist)?p(s.whitelist,a):a)},getAttributes(t){var e,i=this.getCustomAttributes(t),s=\"\";for(e in i)s+=\" \"+e+(void 0!==t[e]?`=\"${i[e]}\"`:\"\");return s},getCustomAttributes(t){if(!h(t))return\"\";var e,i={};for(e in t)\"__\"!=e.slice(0,2)&&\"class\"!=e&&t.hasOwnProperty(e)&&void 0!==t[e]&&(i[e]=d(t[e]));return i},setStateSelection(){var t=window.getSelection(),e={anchorOffset:t.anchorOffset,anchorNode:t.anchorNode,range:t.getRangeAt&&t.rangeCount&&t.getRangeAt(0)};return this.state.selection=e,e},getCaretGlobalPosition(){const t=document.getSelection();if(t.rangeCount){const e=t.getRangeAt(0),i=e.startContainer,s=e.startOffset;let a,n;if(s>0)return n=document.createRange(),n.setStart(i,s-1),n.setEnd(i,s),a=n.getBoundingClientRect(),{left:a.right,top:a.top,bottom:a.bottom};if(i.getBoundingClientRect)return i.getBoundingClientRect()}return{left:-9999,top:-9999}},getCSSVars(){var t=getComputedStyle(this.DOM.scope,null);var e;this.CSSVars={tagHideTransition:(t=>{let e=t.value;return\"s\"==t.unit?1e3*e:e})(function(t){if(!t)return{};var e=(t=t.trim().split(\" \")[0]).split(/\\d+/g).filter((t=>t)).pop().trim();return{value:+t.split(e).filter((t=>t))[0].trim(),unit:e}}((e=\"tag-hide-transition\",t.getPropertyValue(\"--\"+e))))}},build(t){var e=this.DOM;this.settings.mixMode.integrated?(e.originalInput=null,e.scope=t,e.input=t):(e.originalInput=t,e.originalInput_tabIndex=t.tabIndex,e.scope=this.parseTemplate(\"wrapper\",[t,this.settings]),e.input=e.scope.querySelector(this.settings.classNames.inputSelector),t.parentNode.insertBefore(e.scope,t),t.tabIndex=-1)},destroy(){this.events.unbindGlobal.call(this),this.DOM.scope.parentNode.removeChild(this.DOM.scope),this.DOM.originalInput.tabIndex=this.DOM.originalInput_tabIndex,delete this.DOM.originalInput.__tagify,this.dropdown.hide(!0),clearTimeout(this.dropdownHide__bindEventsTimeout),clearInterval(this.listeners.main.originalInputValueObserverInterval)},loadOriginalValues(t){var e,i=this.settings;if(this.state.blockChangeEvent=!0,void 0===t){const e=this.getPersistedData(\"value\");t=e&&!this.DOM.originalInput.value?e:i.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value}if(this.removeAllTags(),t)if(\"mix\"==i.mode)this.parseMixTags(this.trim(t)),(e=this.DOM.input.lastChild)&&\"BR\"==e.tagName||this.DOM.input.insertAdjacentHTML(\"beforeend\",\"<br>\");else{try{JSON.parse(t)instanceof Array&&(t=JSON.parse(t))}catch(t){}this.addTags(t,!0).forEach((t=>t&&t.classList.add(i.classNames.tagNoAnimation)))}else this.postUpdate();this.state.lastOriginalValueReported=i.mixMode.integrated?\"\":this.DOM.originalInput.value,this.state.blockChangeEvent=!1},cloneEvent(t){var e={};for(var i in t)\"path\"!=i&&(e[i]=t[i]);return e},loading(t){return this.state.isLoading=t,this.DOM.scope.classList[t?\"add\":\"remove\"](this.settings.classNames.scopeLoading),this},tagLoading(t,e){return t&&t.classList[e?\"add\":\"remove\"](this.settings.classNames.tagLoading),this},toggleClass(t,e){\"string\"==typeof t&&this.DOM.scope.classList.toggle(t,e)},toggleScopeValidation(t){var e=!0===t||void 0===t;!this.settings.required&&t&&t===this.TEXTS.empty&&(e=!0),this.toggleClass(this.settings.classNames.tagInvalid,!e),this.DOM.scope.title=e?\"\":t},toggleFocusClass(t){this.toggleClass(this.settings.classNames.focus,!!t)},triggerChangeEvent:function(){if(!this.settings.mixMode.integrated){var t=this.DOM.originalInput,e=this.state.lastOriginalValueReported!==t.value,i=new CustomEvent(\"change\",{bubbles:!0});e&&(this.state.lastOriginalValueReported=t.value,i.simulated=!0,t._valueTracker&&t._valueTracker.setValue(Math.random()),t.dispatchEvent(i),this.trigger(\"change\",this.state.lastOriginalValueReported),t.value=this.state.lastOriginalValueReported)}},events:O,fixFirefoxLastTagNoCaret(){},placeCaretAfterNode(t){if(t&&t.parentNode){var e=t,i=window.getSelection(),s=i.getRangeAt(0);i.rangeCount&&(s.setStartAfter(e||t),s.collapse(!0),i.removeAllRanges(),i.addRange(s))}},insertAfterTag(t,e){if(e=e||this.settings.mixMode.insertAfterTag,t&&t.parentNode&&e)return e=\"string\"==typeof e?document.createTextNode(e):e,t.parentNode.insertBefore(e,t.nextSibling),e},editTagChangeDetected(t){var e=t.__originalData;for(var i in e)if(!this.dataProps.includes(i)&&t[i]!=e[i])return!0;return!1},getTagTextNode(t){return t.querySelector(this.settings.classNames.tagTextSelector)},setTagTextNode(t,e){this.getTagTextNode(t).innerHTML=d(e)},editTag(t,e){t=t||this.getLastTag(),e=e||{},this.dropdown.hide();var i=this.settings,s=this.getTagTextNode(t),a=this.getNodeIndex(t),n=this.tagData(t),o=this.events.callbacks,r=this,l=!0;if(s){if(!(n instanceof Object&&\"editable\"in n)||n.editable)return n=this.tagData(t,{__originalData:g({},n),__originalHTML:t.cloneNode(!0)}),this.tagData(n.__originalHTML,n.__originalData),s.setAttribute(\"contenteditable\",!0),t.classList.add(i.classNames.tagEditing),s.addEventListener(\"focus\",o.onEditTagFocus.bind(this,t)),s.addEventListener(\"blur\",(function(){setTimeout((()=>o.onEditTagBlur.call(r,r.getTagTextNode(t))))})),s.addEventListener(\"input\",o.onEditTagInput.bind(this,s)),s.addEventListener(\"keydown\",(e=>o.onEditTagkeydown.call(this,e,t))),s.addEventListener(\"compositionstart\",o.onCompositionStart.bind(this)),s.addEventListener(\"compositionend\",o.onCompositionEnd.bind(this)),e.skipValidation||(l=this.editTagToggleValidity(t)),s.originalIsValid=l,this.trigger(\"edit:start\",{tag:t,index:a,data:n,isValid:l}),s.focus(),this.setRangeAtStartEnd(!1,s),this}else console.warn(\"Cannot find element in Tag template: .\",i.classNames.tagTextSelector)},editTagToggleValidity(t,e){var i;if(e=e||this.tagData(t))return(i=!(\"__isValid\"in e)||!0===e.__isValid)||this.removeTagsFromValue(t),this.update(),t.classList.toggle(this.settings.classNames.tagNotAllowed,!i),e.__isValid;console.warn(\"tag has no data: \",t,e)},onEditTagDone(t,e){e=e||{};var i={tag:t=t||this.state.editing.scope,index:this.getNodeIndex(t),previousData:this.tagData(t),data:e};this.trigger(\"edit:beforeUpdate\",i,{cloneData:!1}),this.state.editing=!1,delete e.__originalData,delete e.__originalHTML,t&&e[this.settings.tagTextProp]?(t=this.replaceTag(t,e),this.editTagToggleValidity(t,e),this.settings.a11y.focusableTags?t.focus():this.placeCaretAfterNode(t)):t&&this.removeTags(t),this.trigger(\"edit:updated\",i),this.dropdown.hide(),this.settings.keepInvalidTags&&this.reCheckInvalidTags()},replaceTag(t,e){e&&e.value||(e=t.__tagifyTagData),e.__isValid&&1!=e.__isValid&&g(e,this.getInvalidTagAttrs(e,e.__isValid));var i=this.createTagElem(e);return t.parentNode.replaceChild(i,t),this.updateValueByDOMTags(),i},updateValueByDOMTags(){this.value.length=0,[].forEach.call(this.getTagElms(),(t=>{t.classList.contains(this.settings.classNames.tagNotAllowed.split(\" \")[0])||this.value.push(this.tagData(t))})),this.update()},setRangeAtStartEnd(t,e){t=\"number\"==typeof t?t:!!t,e=(e=e||this.DOM.input).lastChild||e;var i=document.getSelection();try{i.rangeCount>=1&&[\"Start\",\"End\"].forEach((s=>i.getRangeAt(0)[\"set\"+s](e,t||e.length)))}catch(t){}},injectAtCaret(t,e){return!(e=e||this.state.selection?.range)&&t?(this.appendMixTags(t),this):(\"string\"==typeof t&&(t=document.createTextNode(t)),e.deleteContents(),e.insertNode(t),this.setRangeAtStartEnd(!1,t),this.updateValueByDOMTags(),this.update(),this)},input:{set(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:\"\",e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];var i=this.settings.dropdown.closeOnSelect;this.state.inputText=t,e&&(this.DOM.input.innerHTML=d(\"\"+t)),!t&&i&&this.dropdown.hide.bind(this),this.input.autocomplete.suggest.call(this),this.input.validate.call(this)},raw(){return this.DOM.input.textContent},validate(){var t=!this.state.inputText||!0===this.validateTag({value:this.state.inputText});return this.DOM.input.classList.toggle(this.settings.classNames.inputInvalid,!t),t},normalize(t){var e=t||this.DOM.input,i=[];e.childNodes.forEach((t=>3==t.nodeType&&i.push(t.nodeValue))),i=i.join(\"\\n\");try{i=i.replace(/(?:\\r\\n|\\r|\\n)/g,this.settings.delimiters.source.charAt(0))}catch(t){}return i=i.replace(/\\s/g,\" \"),this.trim(i)},autocomplete:{suggest(t){if(this.settings.autoComplete.enabled){\"string\"==typeof(t=t||{})&&(t={value:t});var e=t.value?\"\"+t.value:\"\",i=e.substr(0,this.state.inputText.length).toLowerCase(),s=e.substring(this.state.inputText.length);e&&this.state.inputText&&i==this.state.inputText.toLowerCase()?(this.DOM.input.setAttribute(\"data-suggest\",s),this.state.inputSuggestion=t):(this.DOM.input.removeAttribute(\"data-suggest\"),delete this.state.inputSuggestion)}},set(t){var e=this.DOM.input.getAttribute(\"data-suggest\"),i=t||(e?this.state.inputText+e:null);return!!i&&(\"mix\"==this.settings.mode?this.replaceTextWithNode(document.createTextNode(this.state.tag.prefix+i)):(this.input.set.call(this,i),this.setRangeAtStartEnd()),this.input.autocomplete.suggest.call(this),this.dropdown.hide(),!0)}}},getTagIdx(t){return this.value.findIndex((e=>e.__tagId==(t||{}).__tagId))},getNodeIndex(t){var e=0;if(t)for(;t=t.previousElementSibling;)e++;return e},getTagElms(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var s=\".\"+[...this.settings.classNames.tag.split(\" \"),...e].join(\".\");return[].slice.call(this.DOM.scope.querySelectorAll(s))},getLastTag(){var t=this.DOM.scope.querySelectorAll(`${this.settings.classNames.tagSelector}:not(.${this.settings.classNames.tagHide}):not([readonly])`);return t[t.length-1]},tagData:(t,e,i)=>t?(e&&(t.__tagifyTagData=i?e:g({},t.__tagifyTagData||{},e)),t.__tagifyTagData):(console.warn(\"tag element doesn't exist\",t,e),e),isTagDuplicate(t,e,i){var a=0;if(\"select\"==this.settings.mode)return!1;for(let n of this.value){s(this.trim(\"\"+t),n.value,e)&&i!=n.__tagId&&a++}return a},getTagIndexByValue(t){var e=[];return this.getTagElms().forEach(((i,a)=>{s(this.trim(i.textContent),t,this.settings.dropdown.caseSensitive)&&e.push(a)})),e},getTagElmByValue(t){var e=this.getTagIndexByValue(t)[0];return this.getTagElms()[e]},flashTag(t){t&&(t.classList.add(this.settings.classNames.tagFlash),setTimeout((()=>{t.classList.remove(this.settings.classNames.tagFlash)}),100))},isTagBlacklisted(t){return t=this.trim(t.toLowerCase()),this.settings.blacklist.filter((e=>(\"\"+e).toLowerCase()==t)).length},isTagWhitelisted(t){return!!this.getWhitelistItem(t)},getWhitelistItem(t,e,i){e=e||\"value\";var a,n=this.settings;return(i=i||n.whitelist).some((i=>{var o=\"string\"==typeof i?i:i[e]||i.value;if(s(o,t,n.dropdown.caseSensitive,n.trim))return a=\"string\"==typeof i?{value:i}:i,!0})),a||\"value\"!=e||\"value\"==n.tagTextProp||(a=this.getWhitelistItem(t,n.tagTextProp,i)),a},validateTag(t){var e=this.settings,i=\"value\"in t?\"value\":e.tagTextProp,s=this.trim(t[i]+\"\");return(t[i]+\"\").trim()?e.pattern&&e.pattern instanceof RegExp&&!e.pattern.test(s)?this.TEXTS.pattern:!e.duplicates&&this.isTagDuplicate(s,e.dropdown.caseSensitive,t.__tagId)?this.TEXTS.duplicate:this.isTagBlacklisted(s)||e.enforceWhitelist&&!this.isTagWhitelisted(s)?this.TEXTS.notAllowed:!e.validate||e.validate(t):this.TEXTS.empty},getInvalidTagAttrs(t,e){return{\"aria-invalid\":!0,class:`${t.class||\"\"} ${this.settings.classNames.tagNotAllowed}`.trim(),title:e}},hasMaxTags(){return this.value.length>=this.settings.maxTags&&this.TEXTS.exceed},setReadonly(t,e){var i=this.settings;document.activeElement.blur(),i[e||\"readonly\"]=t,this.DOM.scope[(t?\"set\":\"remove\")+\"Attribute\"](e||\"readonly\",!0),this.setContentEditable(!t)},setContentEditable(t){this.settings.userInput&&(this.DOM.input.contentEditable=t,this.DOM.input.tabIndex=t?0:-1)},setDisabled(t){this.setReadonly(t,\"disabled\")},normalizeTags(t){var e=this.settings,i=e.whitelist,s=e.delimiters,a=e.mode,n=e.tagTextProp;e.enforceWhitelist;var o=[],r=!!i&&i[0]instanceof Object,l=Array.isArray(t),d=l&&t[0].value,h=t=>(t+\"\").split(s).filter((t=>t)).map((t=>({[n]:this.trim(t),value:this.trim(t)})));if(\"number\"==typeof t&&(t=t.toString()),\"string\"==typeof t){if(!t.trim())return[];t=h(t)}else l&&(t=[].concat(...t.map((t=>t.value?t:h(t)))));return r&&!d&&(t.forEach((t=>{var e=o.map((t=>t.value)),i=this.dropdown.filterListItems.call(this,t[n],{exact:!0});this.settings.duplicates||(i=i.filter((t=>!e.includes(t.value))));var s=i.length>1?this.getWhitelistItem(t[n],n,i):i[0];s&&s instanceof Object?o.push(s):\"mix\"!=a&&(null==t.value&&(t.value=t[n]),o.push(t))})),o.length&&(t=o)),t},parseMixTags(t){var e=this.settings,i=e.mixTagsInterpolator,s=e.duplicates,a=e.transformTag,n=e.enforceWhitelist,o=e.maxTags,r=e.tagTextProp,l=[];return t=t.split(i[0]).map(((t,e)=>{var d,h,g,p=t.split(i[1]),c=p[0],u=l.length==o;try{if(c==+c)throw Error;h=JSON.parse(c)}catch(t){h=this.normalizeTags(c)[0]||{value:c}}if(a.call(this,h),u||!(p.length>1)||n&&!this.isTagWhitelisted(h.value)||!s&&this.isTagDuplicate(h.value)){if(t)return e?i[0]+t:t}else h[d=h[r]?r:\"value\"]=this.trim(h[d]),g=this.createTagElem(h),l.push(h),g.classList.add(this.settings.classNames.tagNoAnimation),p[0]=g.outerHTML,this.value.push(h);return p.join(\"\")})).join(\"\"),this.DOM.input.innerHTML=t,this.DOM.input.appendChild(document.createTextNode(\"\")),this.DOM.input.normalize(),this.getTagElms().forEach(((t,e)=>this.tagData(t,l[e]))),this.update({withoutChangeEvent:!0}),t},replaceTextWithNode(t,e){if(this.state.tag||e){e=e||this.state.tag.prefix+this.state.tag.value;var i,s,a=this.state.selection||window.getSelection(),n=a.anchorNode,o=this.state.tag.delimiters?this.state.tag.delimiters.length:0;return n.splitText(a.anchorOffset-o),-1==(i=n.nodeValue.lastIndexOf(e))?!0:(s=n.splitText(i),t&&n.parentNode.replaceChild(t,s),!0)}},selectTag(t,e){var i=this.settings;if(!i.enforceWhitelist||this.isTagWhitelisted(e.value)){this.input.set.call(this,e[i.tagTextProp]||e.value,!0),this.state.actions.selectOption&&setTimeout(this.setRangeAtStartEnd.bind(this));var s=this.getLastTag();return s?this.replaceTag(s,e):this.appendTag(t),this.value[0]=e,this.update(),this.trigger(\"add\",{tag:t,data:e}),[t]}},addEmptyTag(t){var e=g({value:\"\"},t||{}),i=this.createTagElem(e);this.tagData(i,e),this.appendTag(i),this.editTag(i,{skipValidation:!0})},addTags(t,e,i){var s=[],a=this.settings,n=[],o=document.createDocumentFragment();if(i=i||a.skipInvalid,!t||0==t.length)return s;switch(t=this.normalizeTags(t),a.mode){case\"mix\":return this.addMixTags(t);case\"select\":e=!1,this.removeAllTags()}return this.DOM.input.removeAttribute(\"style\"),t.forEach((t=>{var e,r={},l=Object.assign({},t,{value:t.value+\"\"});if(t=Object.assign({},l),a.transformTag.call(this,t),t.__isValid=this.hasMaxTags()||this.validateTag(t),!0!==t.__isValid){if(i)return;if(g(r,this.getInvalidTagAttrs(t,t.__isValid),{__preInvalidData:l}),t.__isValid==this.TEXTS.duplicate&&this.flashTag(this.getTagElmByValue(t.value)),!a.createInvalidTags)return void n.push(t.value)}if(\"readonly\"in t&&(t.readonly?r[\"aria-readonly\"]=!0:delete t.readonly),e=this.createTagElem(t,r),s.push(e),\"select\"==a.mode)return this.selectTag(e,t);o.appendChild(e),t.__isValid&&!0===t.__isValid?(this.value.push(t),this.trigger(\"add\",{tag:e,index:this.value.length-1,data:t})):(this.trigger(\"invalid\",{data:t,index:this.value.length,tag:e,message:t.__isValid}),a.keepInvalidTags||setTimeout((()=>this.removeTags(e,!0)),1e3)),this.dropdown.position()})),this.appendTag(o),this.update(),t.length&&e&&(this.input.set.call(this,a.createInvalidTags?\"\":n.join(a._delimiters)),this.setRangeAtStartEnd()),a.dropdown.enabled&&this.dropdown.refilter(),s},addMixTags(t){if((t=this.normalizeTags(t))[0].prefix||this.state.tag)return this.prefixedTextToTag(t[0]);\"string\"==typeof t&&(t=[{value:t}]),this.state.selection;var e=document.createDocumentFragment();return t.forEach((t=>{var i=this.createTagElem(t);e.appendChild(i),this.insertAfterTag(i)})),this.appendMixTags(e),e},appendMixTags(t){var e=!!this.state.selection;e?this.injectAtCaret(t):(this.DOM.input.focus(),(e=this.setStateSelection()).range.setStart(this.DOM.input,e.range.endOffset),e.range.setEnd(this.DOM.input,e.range.endOffset),this.DOM.input.appendChild(t),this.updateValueByDOMTags(),this.update())},prefixedTextToTag(t){var e,i=this.settings,s=this.state.tag.delimiters;if(i.transformTag.call(this,t),t.prefix=t.prefix||this.state.tag?this.state.tag.prefix:(i.pattern.source||i.pattern)[0],e=this.createTagElem(t),this.replaceTextWithNode(e)||this.DOM.input.appendChild(e),setTimeout((()=>e.classList.add(this.settings.classNames.tagNoAnimation)),300),this.value.push(t),this.update(),!s){var a=this.insertAfterTag(e)||e;this.placeCaretAfterNode(a)}return this.state.tag=null,this.trigger(\"add\",g({},{tag:e},{data:t})),e},appendTag(t){var e=this.DOM,i=e.input;i===e.input?e.scope.insertBefore(t,i):e.scope.appendChild(t)},createTagElem(t,i){t.__tagId=m();var s,a=g({},t,e({value:d(t.value+\"\")},i));return function(t){for(var e,i=document.createNodeIterator(t,NodeFilter.SHOW_TEXT,null,!1);e=i.nextNode();)e.textContent.trim()||e.parentNode.removeChild(e)}(s=this.parseTemplate(\"tag\",[a,this])),this.tagData(s,t),s},reCheckInvalidTags(){var t=this.settings;this.getTagElms(t.classNames.tagNotAllowed).forEach(((e,i)=>{var s=this.tagData(e),a=this.hasMaxTags(),n=this.validateTag(s),o=!0===n&&!a;if(\"select\"==t.mode&&this.toggleScopeValidation(n),o)return s=s.__preInvalidData?s.__preInvalidData:{value:s.value},this.replaceTag(e,s);e.title=a||n}))},removeTags(t,e,i){var s,a=this.settings;if(t=t&&t instanceof HTMLElement?[t]:t instanceof Array?t:t?[t]:[this.getLastTag()],s=t.reduce(((t,e)=>{e&&\"string\"==typeof e&&(e=this.getTagElmByValue(e));var i=this.tagData(e);return e&&i&&!i.readonly&&t.push({node:e,idx:this.getTagIdx(i),data:this.tagData(e,{__removed:!0})}),t}),[]),i=\"number\"==typeof i?i:this.CSSVars.tagHideTransition,\"select\"==a.mode&&(i=0,this.input.set.call(this)),1==s.length&&\"select\"!=a.mode&&s[0].node.classList.contains(a.classNames.tagNotAllowed)&&(e=!0),s.length)return a.hooks.beforeRemoveTag(s,{tagify:this}).then((()=>{function t(t){t.node.parentNode&&(t.node.parentNode.removeChild(t.node),e?a.keepInvalidTags&&this.trigger(\"remove\",{tag:t.node,index:t.idx}):(this.trigger(\"remove\",{tag:t.node,index:t.idx,data:t.data}),this.dropdown.refilter(),this.dropdown.position(),this.DOM.input.normalize(),a.keepInvalidTags&&this.reCheckInvalidTags()))}i&&i>10&&1==s.length?function(e){e.node.style.width=parseFloat(window.getComputedStyle(e.node).width)+\"px\",document.body.clientTop,e.node.classList.add(a.classNames.tagHide),setTimeout(t.bind(this),i,e)}.call(this,s[0]):s.forEach(t.bind(this)),e||(this.removeTagsFromValue(s.map((t=>t.node))),this.update(),\"select\"==a.mode&&this.setContentEditable(!0))})).catch((t=>{}))},removeTagsFromDOM(){[].slice.call(this.getTagElms()).forEach((t=>t.parentNode.removeChild(t)))},removeTagsFromValue(t){(t=Array.isArray(t)?t:[t]).forEach((t=>{var e=this.tagData(t),i=this.getTagIdx(e);i>-1&&this.value.splice(i,1)}))},removeAllTags(t){t=t||{},this.value=[],\"mix\"==this.settings.mode?this.DOM.input.innerHTML=\"\":this.removeTagsFromDOM(),this.dropdown.refilter(),this.dropdown.position(),this.state.dropdown.visible&&setTimeout((()=>{this.DOM.input.focus()})),\"select\"==this.settings.mode&&(this.input.set.call(this),this.setContentEditable(!0)),this.update(t)},postUpdate(){var t=this.settings,e=t.classNames,i=\"mix\"==t.mode?t.mixMode.integrated?this.DOM.input.textContent:this.DOM.originalInput.value.trim():this.value.length+this.input.raw.call(this).length;this.toggleClass(e.hasMaxTags,this.value.length>=t.maxTags),this.toggleClass(e.hasNoTags,!this.value.length),this.toggleClass(e.empty,!i),\"select\"==t.mode&&this.toggleScopeValidation(this.value?.[0]?.__isValid)},setOriginalInputValue(t){var e=this.DOM.originalInput;this.settings.mixMode.integrated||(e.value=t,e.tagifyValue=e.value,this.setPersistedData(t,\"value\"))},update(t){var e=this.getInputValue();this.setOriginalInputValue(e),this.postUpdate(),this.settings.onChangeAfterBlur&&(t||{}).withoutChangeEvent||this.state.blockChangeEvent||this.triggerChangeEvent()},getInputValue(){var t=this.getCleanValue();return\"mix\"==this.settings.mode?this.getMixedTagsAsString(t):t.length?this.settings.originalInputValueFormat?this.settings.originalInputValueFormat(t):JSON.stringify(t):\"\"},getCleanValue(t){return a(t||this.value,this.dataProps)},getMixedTagsAsString(){var t=\"\",e=this,i=this.settings,s=i.originalInputValueFormat||JSON.stringify,a=i.mixTagsInterpolator;return function i(o){o.childNodes.forEach((o=>{if(1==o.nodeType){const r=e.tagData(o);if(\"BR\"==o.tagName&&(t+=\"\\r\\n\"),r&&v.call(e,o)){if(r.__removed)return;t+=a[0]+s(n(r,e.dataProps))+a[1]}else o.getAttribute(\"style\")||[\"B\",\"I\",\"U\"].includes(o.tagName)?t+=o.textContent:\"DIV\"!=o.tagName&&\"P\"!=o.tagName||(t+=\"\\r\\n\",i(o))}else t+=o.textContent}))}(this.DOM.input),t}},M.prototype.removeTag=M.prototype.removeTags,M}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@yaireo/tagify/dist/tagify.min.js\n");

/***/ }),

/***/ "./libs/tagify/tagify.js":
/*!*******************************!*\
  !*** ./libs/tagify/tagify.js ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Tagify\": function() { return /* reexport default from dynamic */ _yaireo_tagify__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _yaireo_tagify__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @yaireo/tagify */ \"./node_modules/@yaireo/tagify/dist/tagify.min.js\");\n/* harmony import */ var _yaireo_tagify__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_yaireo_tagify__WEBPACK_IMPORTED_MODULE_0__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3RhZ2lmeS90YWdpZnkuanMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vVnVleHkvLi9saWJzL3RhZ2lmeS90YWdpZnkuanM/YTQzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVGFnaWZ5IGZyb20gJ0B5YWlyZW8vdGFnaWZ5JztcblxuZXhwb3J0IHsgVGFnaWZ5IH07XG4iXSwibmFtZXMiOlsiVGFnaWZ5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/tagify/tagify.js\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./libs/tagify/tagify.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});