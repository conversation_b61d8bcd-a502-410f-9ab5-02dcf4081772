@use '../../utils' as *;

/*----------------------------------------*/
/*  dashboard chat
/*----------------------------------------*/
.support-chat-box {
  border-radius: 24px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF2EE;
  padding: 30px;

  .td-chat-footer {
    .message-box {
      .td-form-group-message {
        position: relative;

        .file-input {
          position: absolute;
          top: 21px;
          left: 10px;
          z-index: 1;

          .file-clip {
            font-size: 18px;
            color: #303030CC;
          }
        }

        .input-field {
          .form-control {
            height: 60px;
            border-radius: 0px;
            background: transparent;
            color: rgba(48, 48, 48, 0.80);
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            border-radius: 10px;
            border: 1px solid rgba(48, 48, 48, 0.20);
            background: #FFF;
            padding-left: 35px;
            padding-right: 120px;
          }
        }

        .send-button {
          display: inline-flex;
          height: 40px;
          padding: 10px 16px;
          align-items: center;
          gap: 10px;
          border-radius: 12px;
          background: var(--td-secondary);
          color: var(--td-white);
          font-size: 14px;
          font-weight: 600;
          line-height: normal;
          position: absolute;
          top: 10px;
          inset-inline-end: 10px;
          z-index: 1;

          .send-icon {
            font-size: 18px;
            color: var(--td-white);
          }
        }
      }
    }
  }
}

.preview-file-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 10px;

  .preview-file {
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
    border-radius: 5px;
    border: 1px solid rgba(48, 48, 48, 0.20);
    background: #FFF;
    padding: 5px 10px;

    .img-or-file-preview {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(48, 48, 48, 0.20);
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .file-icon {
        font-size: 20px;
        color: white;
      }
    }

    .file-name {
      font-size: 12px;
      color: var(--td-text-primary);
    }

    .remove-item {
      position: absolute;
      top: 0px;
      inset-inline-end: 5px;
      cursor: pointer;
      background: none;
      border: none;
      font-size: 16px;
    }
  }
}


.has-attachment {
  h6 {
    font-size: 16px;
    font-weight: 500;
    color: var(--td-primary);
    margin-bottom: 5px;
  }

  &-box {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon {
      height: 30px;
      width: 30px;
      background-color: var(--td-card-bg-2);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    p {
      font-size: 14px;
      font-weight: 400;
      color: var(--td-card-bg-1);
      margin-bottom: 0 !important;
    }
  }
}

.show-file-name {
  span {
    font-size: 14px;
  }
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 650px;
  overflow-y: auto;
  padding-inline-end: 20px;

  &::-webkit-scrollbar {
    width: rem(5);
  }

  &::-webkit-scrollbar-track {
    background: #d6d6d6;
  }

  &::-webkit-scrollbar-thumb {
    background-image: linear-gradient(125deg, rgba(#080808, 0.4) 0%, rgba(#080808, 0.4) 100%);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .message {
    display: flex;
    flex-direction: column !important;
    align-items: end;
    gap: 10px;

    .avatar-thumb {
      display: flex;
      align-items: center;
      gap: 8px;

      .user-info {
        h4 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: normal;
        }

        p {
          font-size: 14px;
        }
      }

      .img {
        width: 40px;
        height: 40px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }
      }


    }

    .contents {
      padding: 16px 24px;
      border-radius: 14px 0px 14px 14px;
      border: 1px solid rgba(48, 48, 48, 0.16);
      background: rgba(255, 98, 41, 0.10);

      p {
        font-size: rem(14);
        color: var(--td-heading);
        text-align: left;
      }

      .message-time {
        display: block;
        text-align: right;
        letter-spacing: -0.03em;
        color: var(--td-white);
      }

      .has-attachment {
        margin-top: 10px;

        h6 {
          color: var(--td-heading);
          text-align: left;
          font-size: 13px;
          font-weight: 600;
          line-height: normal;
        }

        .attachment-box {
          display: flex;
          align-items: center;
          gap: 8px;
          text-align: right;
          margin-top: 2px;

          @media #{$xs} {
            flex-direction: column;
            align-items: start;
          }

          .icon {
            display: flex;

            .file-icon {
              font-size: 18px;
              color: var(--td-secondary);
            }
          }

          p {
            color: var(--td-heading);
            font-size: 14px;
            font-weight: 400;
            line-height: normal;
            text-wrap: wrap;
          }
        }
      }
    }
  }

  .user-message {
    width: 60%;
    margin-inline-start: auto;
    text-align: right;
    flex-direction: row-reverse;

    @media #{$xxl} {
      width: 100%;
    }

    @media #{$xs,$sm,$md} {
      width: 100%;
    }
  }

  .reply-message {
    width: 60%;
    margin-inline-end: auto;
    text-align: right;

    @media #{$xxl} {
      width: 100%;
    }

    @media #{$xs,$sm,$md} {
      width: 100%;
    }

    .avatar-thumb {
      width: 100%;

      .avatar-thumb {
        order: 1;
      }

      .user-info {
        order: 2;

        h4 {
          text-align: left;
        }

        p {
          text-align: left;
        }
      }
    }

    .contents {
      border-radius: 0px 14px 14px 14px;
      border: 1px solid #E7E7E7;
      background: #FFF;
      padding: 16px 24px;

      p {
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 400;
        line-height: lh(22, 14);
      }

      .message-time {
        color: var(--td-card-bg-2);
      }
    }
  }

  .message-time {
    font-size: 12px;
    color: #aaa;
  }
}