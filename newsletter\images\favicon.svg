<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_242_7704)">
<rect width="48" height="48" rx="10.8" fill="#FF6229"/>
<rect width="48" height="48" fill="url(#paint0_linear_242_7704)"/>
<g filter="url(#filter0_d_242_7704)">
<path d="M10.5 13.875C10.5 12.011 12.011 10.5 13.875 10.5H20.625C22.489 10.5 24 12.011 24 13.875V20.5029C24.0002 20.5435 24.0002 20.5842 24.0002 20.625C24.0002 22.4863 25.5069 23.9957 27.3672 24C27.3698 24 27.3724 24 27.375 24H34.125C35.989 24 37.5 25.511 37.5 27.375V34.125C37.5 35.989 35.989 37.5 34.125 37.5H27.375C25.511 37.5 24 35.989 24 34.125V27.375C24 27.3656 24 27.3563 24.0001 27.3469C23.985 25.4959 22.4798 24 20.6252 24C20.6165 24 20.6078 24 20.5991 24H13.875C12.011 24 10.5 22.489 10.5 20.625V13.875Z" fill="url(#paint1_linear_242_7704)"/>
</g>
</g>
<rect x="0.9" y="0.9" width="46.2" height="46.2" rx="9.9" stroke="url(#paint2_linear_242_7704)" stroke-width="1.8"/>
<defs>
<filter id="filter0_d_242_7704" x="7.8" y="7.1248" width="32.4" height="37.8004" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1.35" operator="erode" in="SourceAlpha" result="effect1_dropShadow_242_7704"/>
<feOffset dy="2.025"/>
<feGaussianBlur stdDeviation="2.025"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0 0.141176 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_242_7704"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_242_7704" result="shape"/>
</filter>
<linearGradient id="paint0_linear_242_7704" x1="24" y1="5.96047e-07" x2="26" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint1_linear_242_7704" x1="24" y1="10.5" x2="24" y2="37.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_242_7704" x1="24" y1="0" x2="24" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.12"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_242_7704">
<rect width="48" height="48" rx="10.8" fill="white"/>
</clipPath>
</defs>
</svg>
