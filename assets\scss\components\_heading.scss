@use "../utils" as *;

/*----------------------------------------*/
/*  2.9 Heading
/*----------------------------------------*/
.websiteTitle {
  color: var(--td-heading);
  text-align: center;
  font-size: 60px;
  font-weight: 800;
  line-height: normal;

  @media #{$xl} {
    font-size: 55px;
  }

  @media #{$lg} {
    font-size: 45px;
  }

  @media #{$md} {
    font-size: 35px;
  }

  @media #{$xs} {
    font-size: 26px;
  }

  @media #{$sm} {
    font-size: 30px;
  }
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  // @media #{$xs} {
  //   flex-direction: column;
  //   align-items: start;
  //   gap: 10px;
  // }

  .left {
    .title-text {
      display: flex;
      align-items: center;
      gap: 10px;

      h2 {
        color: var(--td-heading);
        font-size: 30px;
        font-weight: 700;
        line-height: lh(42, 30);

        @media #{$xs} {
          font-size: 20px;
        }
      }

      .flash-timer {
        display: flex;
        padding: 10px 15px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 60px;
        background: #e93a2d;
        margin-left: 50px;

        @media #{$xs} {
          margin-left: 10px;
          padding: 5px 8px;
        }

        p {
          color: var(--td-white);
          font-size: 14px;
          font-weight: 600;
          line-height: lh(20, 14);
        }
      }
    }
  }

  .right {
    .swiper-arrows {
      display: flex;
      align-items: center;
      gap: 8px;

      .swiper-btn {
        color: var(--td-secondary);
        width: 35px;
        height: 35px;
        background-color: var(--td-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: #fff;

        @include rtl {
          transform: rotate(180deg);
        }

        &.swiper-button-disabled {
          background-color: #30303099;
        }

        .arrow-icon {
          font-size: 20px;
        }
      }
    }
  }
}

.middle-section-title {
  display: flex;
  justify-content: center;
  align-items: center;

  .middle {
    width: 415px;

    h2 {
      color: var(--td-heading);
      text-align: center;
      font-size: 30px;
      font-style: normal;
      font-weight: 700;
      line-height: lh(42, 30);
      margin-bottom: 12px;
    }

    p {
      color: var(--td-text-primary);
      text-align: center;
      font-size: 16px;
      font-weight: 400;
      line-height: lh(22, 14);
    }
  }
}

.common-page-header {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 58px 0;

  @media #{$lg} {
    padding: 50px 0;
  }

  @media #{$md} {
    padding: 45px 0;
  }

  @media #{$xs} {
    padding: 30px 0;
  }

  @media #{$sm} {
    padding: 40px 0;
  }

  .content {
    max-width: 600px;

    h2 {
      color: var(--td-heading);
      text-align: center;
      font-size: 30px;
      font-weight: 700;
      line-height: lh(42, 30);

      @media #{$lg} {
        font-size: 28px;
      }

      @media #{$md} {
        font-size: 26px;
      }

      @media #{$xs} {
        font-size: 22px;
      }

      @media #{$sm} {
        font-size: 24px;
      }
    }
  }
}

.left-filter {
  @media #{$xs} {
    width: 100%;
  }
}

.all-games-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;

  @media #{$md,$xs} {
    flex-direction: column;
    align-items: start;
    gap: 20px;
  }

  .price-range {
    display: flex;
    align-items: center;
    gap: 30px;
    background: #ffeae3;
    border-radius: 10px;
    padding: 10px 16px;

    @media #{$md,$xs} {
      flex-direction: column;
      align-items: start;
      gap: 10px;
    }

    @media #{$xs} {
      width: 100%;
    }
    @media #{$sm} {
      width: 100%;
    }

    .title {
      h6 {
        color: var(--td-heading);
        font-size: 16px;
        font-weight: 700;
        line-height: lh(20, 14);
      }
    }

    .all-range {
      display: flex;
      align-items: center;
      gap: 10px;

      @media #{$xs} {
        width: 100%;
      }
      @media #{$sm} {
        width: 100%;
      }

      @media #{$xs} {
        flex-direction: column;
        align-items: start;
        gap: 10px;
      }
      @media #{$sm} {
        flex-direction: row;
        align-items: center;
        gap: 10px;
      }

      .price-range-box {
        display: flex;
        align-items: center;
        gap: 12px;

        @media #{$xs} {
          width: 100%;
        }
        @media #{$sm} {
          width: 100%;
        }

        p {
          font-family: var(--td-heading-font);
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 700;
          line-height: lh(20, 14);
        }

        .td-form-group {
          @media #{$xs} {
            width: 100%;
          }
          @media #{$sm} {
            width: 100%;
          }
          .input-field {
            .form-control {
              font-family: var(--td-heading-font);
              height: 40px;
              border-radius: 0px;
              background: transparent;
              color: var(--td-heading);
              font-size: 16px;
              font-weight: 400;
              line-height: 100%;
              border-radius: 8px;
              border: 1px solid rgba(48, 48, 48, 0.16);
              width: 130px;

              @media #{$xs} {
                width: 100%;
              }
              @media #{$sm} {
                width: 130px;
              }
            }
          }
        }
      }
      .price-range-box-button {
        @media #{$xs} {
          width: 100%;
        }
        @media #{$sm} {
          width: 100%;
        }

        .primary-button {
          @media #{$xs} {
            width: 100%;
          }
          @media #{$sm} {
            width: 100%;
          }
        }
      }
      .price-range-box-saperate {
        span {
          display: block;
          height: 2px;
          width: 12px;
          background: rgba(48, 48, 48, 0.31);
        }
      }
    }
  }

  .filters {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    z-index: 2;
  }
}
