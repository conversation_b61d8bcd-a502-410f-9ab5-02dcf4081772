@use '../utils' as *;

/*----------------------------------------*/
/*  Heading
/*----------------------------------------*/
.websiteTitle {
  color: var(--td-white);
  text-align: center;
  font-size: 60px;
  font-weight: 800;
  line-height: normal;

  @media #{$xl} {
    font-size: 55px;
  }

  @media #{$lg} {
    font-size: 45px;
  }

  @media #{$md} {
    font-size: 35px;
  }

  @media #{$xs} {
    font-size: 26px;
  }

  @media #{$sm} {
    font-size: 30px;
  }
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media #{$xs} {
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }

  .left {
    .title-text {
      display: flex;
      align-items: center;
      gap: 10px;

      h2 {
        color: var(--td-heading);
        font-size: 24px;
        font-weight: 700;
        line-height: lh(32, 24);

        @media #{$xs} {
          font-size: 20px;
        }
      }
    }
  }

  .right {
    .swiper-arrows {
      display: flex;
      align-items: center;
      gap: 8px;

      .swiper-btn {
        color: var(--td-secondary);

        &.swiper-button-disabled {
          color: rgba(48, 48, 48, 0.40);
        }

        .arrow-icon {
          font-size: 24px;
        }
      }
    }
  }
}

.middle-section-title {
  display: flex;
  justify-content: center;
  align-items: center;

  .middle {
    width: 415px;

    h2 {
      color: var(--td-heading);
      text-align: center;
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: lh(32, 24);
      margin-bottom: 12px;
    }

    p {
      color: var(--td-text-primary);
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      line-height: lh(22, 14);
    }
  }
}