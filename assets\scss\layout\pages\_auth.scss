@use '../../utils' as *;

/*----------------------------------------*/
/*  Auth
/*----------------------------------------*/
.auth-area {
  position: relative;

  .auth-area-content {
    display: flex;
    align-items: center;
    height: 100vh;
    position: relative;
    z-index: 2;

    @media #{$lg,$md,$xs} {
      flex-direction: column;
    }

    .left {
      width: 608px;

      @media #{$xl} {
        width: 500px;
      }

      @media #{$lg,$md,$xs} {
        width: 100%;
        display: none;
      }

      .auth-img {
        width: 100%;
        height: 100vh;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .right {
      width: calc(100% - 608px);
      height: 100%;

      @media #{$xl} {
        width: calc(100% - 500px);
      }

      @media #{$lg,$md,$xs} {
        width: 100%;
      }

      &.sign-up-right {
        height: unset;
      }

      .auth-content-box {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        padding: 0 100px;

        @media #{$md,$xs} {
          height: 100%;
        }

        @media #{$xs} {
          padding: 0 30px;
        }

        .auth-forms {
          .auth-checking {
            display: flex;
            align-items: center;
            justify-content: space-between;

            @media #{$xs} {
              flex-direction: column;
              align-items: start;
              justify-content: start;
              gap: 10px;
            }

            @media #{$sm} {
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              gap: 4px;
            }

            .forgot-password {

              .forgot-btn {
                color: var(--td-heading);
                text-align: right;
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
              }

            }
          }

          .auth-action {
            margin-top: 20px;

            @media #{$xs} {
              margin-top: 5px;
            }

            @media #{$sm} {
              margin-top: 20px;
            }
          }

          .switch-page {
            margin-top: 20px;

            @media #{$xs} {
              margin-top: 5px;
            }

            @media #{$sm} {
              margin-top: 20px;
            }

            p {
              color: var(--td-heading);
              text-align: center;
              font-size: 14px;
              font-weight: 400;
              line-height: normal;

              a {
                color: var(--td-secondary);
              }
            }
          }
        }

        .auth-content {
          max-width: 645px;
          padding: 50px;
          border-radius: 20px;
          background: #FFF;
          box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.10);

          @media #{$md,$xs} {
            margin: 30px 0;
          }

          @media #{$xs} {
            padding: 16px;
          }

          .auth-header {
            margin-bottom: 60px;

            @media #{$xl,$lg,$md} {
              margin-bottom: 40px;
            }

            @media #{$xs} {
              margin-bottom: 20px;
            }

            @media #{$sm} {
              margin-bottom: 30px;
            }

            &-2 {
              margin-bottom: 40px;
            }

            h3 {
              color: var(--td-heading);
              text-align: center;
              font-size: 24px;
              font-weight: 600;
              line-height: normal;
              margin-bottom: 10px;
            }

            p {
              color: rgba(48, 48, 48, 0.80);
              text-align: center;
              font-size: 14px;
              font-weight: 400;
              line-height: lh(22, 14);
            }
          }
        }
      }
    }
  }

  .auth-element {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    z-index: 1;
    height: 100vh;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}


#twoStepsForm {
  display: flex;
  justify-content: center;
  align-items: center;

  .numeral-mask-wrapper {
    display: flex;
    align-items: center;

    input {
      border: 1px solid #E1E1E1;
      border-radius: 10px;
      padding: 0px 0;
      color: #333;
      width: 50px;
      height: 50px;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      line-height: normal;

      @media #{$xs} {
        width: 40px;
        height: 40px;
        font-size: 16px;
        font-weight: 500;
      }

      &.form-control {
        background-color: transparent;
        margin-inline-end: 15px;

        @media #{$xs} {
          margin-inline-end: 5px;
        }
      }

      &:focus {
        border-color: var(--td-secondary);
        color: #333;
        box-shadow: none;

        &::placeholder {
          opacity: 0;
        }
      }

      &:disabled {
        background-color: transparent;
      }
    }

    &-2 {
      input {
        border-radius: 50%;
      }
    }
  }
}