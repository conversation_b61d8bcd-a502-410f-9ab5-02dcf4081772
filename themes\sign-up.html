<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Sign Up || Easy, Secure and Best Way to Earn Money From Your Own Place</title>
  <meta name="description" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Place favicon.ico in the root directory -->
  <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
  <!-- CSS here -->
  <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="../assets/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/flag-icon.css">
  <link rel="stylesheet" href="../assets/css/swiper.min.css">
  <link rel="stylesheet" href="../assets/css/magnific-popup.css">
  <link rel="stylesheet" href="../assets/css/nice-select.css">
  <link rel="stylesheet" href="../assets/css/select2.min.css">
  <link rel="stylesheet" href="../assets/css/flatpickr.min.css">
  <link rel="stylesheet" href="../assets/css/flat-picker-color-select.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
</head>

<body>
  <!-- Body main wrapper start -->
  <main>
    <!-- auth area start -->
    <div class="auth-area">
      <div class="auth-area-content">
        <div class="left order-2 order-xl-1">
          <div class="auth-img">
            <img src="../assets/images/auth/auth-img.png" alt="">
          </div>
        </div>
        <div class="right order-1 order-xl-2">
          <div class="auth-content-box">
            <div class="auth-content">
              <div class="logo-container">
                <a href="index.html" class="logo">
                  <img src="../assets/images/logo/logo.svg" alt="">
                </a>
              </div>
              <div class="auth-content-inside">
                <div class="auth-header">
                  <h3>Registration</h3>
                </div>
                <div class="auth-tab">
                  <div class="auth-tab-area" id="pills-tab" role="tablist">
                    <button class="auth-tab-btn active" id="nav-home-tab" data-bs-toggle="tab"
                      data-bs-target="#nav-home" type="button" role="tab" aria-controls="nav-home" aria-selected="true">
                      Buyer
                    </button>
                    <button class="auth-tab-btn" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="#nav-profile"
                      type="button" role="tab" aria-controls="nav-profile" aria-selected="false">
                      Seller
                    </button>
                  </div>
                </div>
                <div class="auth-tab-content tab-content" id="nav-tabContent">
                  <div class="tab-pane fade show active vendor" id="nav-home" role="tabpanel"
                    aria-labelledby="nav-home-tab">
                    <div class="auth-forms">
                      <form>
                        <div class="row gy-4">
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">First Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">First Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">First Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">First Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Last Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Email <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Username <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Country <span>*</span></label>
                              <div class="auth-nice-select auth-nice-select-2">
                                <select class="nice-select-active">
                                  <option selected>Male</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                </select>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Gender <span>*</span></label>
                              <div class="auth-nice-select auth-nice-select-2">
                                <select class="nice-select-active">
                                  <option selected>Male</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                </select>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Referral Code <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group has-right-icon">
                              <label class="input-label">Password <span>*</span></label>
                              <div class="input-field input-field-icon">
                                <input type="password" class="form-control" required>
                                <span class="input-icon"><i class="fa-regular fa-eye-slash"></i></span>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group has-right-icon">
                              <label class="input-label">Confirm Password <span>*</span></label>
                              <div class="input-field input-field-icon">
                                <input type="password" class="form-control" required>
                                <span class="input-icon"><i class="fa-regular fa-eye-slash"></i></span>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="auth-checking">
                              <div class="auth-checkbox">
                                <div class="animate-custom">
                                  <input class="inp-cbx" id="auth_remind" type="checkbox">
                                  <label class="cbx" for="auth_remind">
                                    <span>
                                      <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                      </svg>
                                    </span>
                                    <span>I agree with <a href="terms-and-condition.html">Terms & Conditions</a></span>
                                  </label>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="auth-action">
                              <button class="primary-button xl-btn w-100">
                                Register
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="social-logins">
                      <div class="or-divider">
                        <span class="text">or</span>
                      </div>
                      <div class="buttons">
                        <div class="row g-2">
                          <div class="col-sm-6">
                            <a href="#" class="social-btn">
                              <span class="icon">
                                <iconify-icon icon="ri:facebook-fill" class="social-icon facebook"></iconify-icon>
                              </span>
                              <span class="text">With Facebook</span>
                            </a>
                          </div>
                          <div class="col-sm-6">
                            <a href="#" class="social-btn">
                              <span class="icon">
                                <iconify-icon icon="flat-color-icons:google" class="social-icon google"></iconify-icon>
                              </span>
                              <span class="text">With Google</span>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="switch-page">
                      <p>Already have an account? <a href="sign-in.html">Sign In</a></p>
                    </div>
                  </div>
                  <div class="tab-pane fade customer" id="nav-profile" role="tabpanel"
                    aria-labelledby="nav-profile-tab">
                    <div class="auth-forms">
                      <form>
                        <div class="row gy-4">
                          <div class="col-12">
                            <div class="content-separation">
                              <p>Basic information</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">First Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Last Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Email <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Username <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Country <span>*</span></label>
                              <div class="auth-nice-select auth-nice-select-2">
                                <select class="nice-select-active">
                                  <option selected>Male</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                </select>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Gender <span>*</span></label>
                              <div class="auth-nice-select auth-nice-select-2">
                                <select class="nice-select-active">
                                  <option selected>Male</option>
                                  <option value="1">Female</option>
                                  <option value="2">Others</option>
                                </select>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Referral Code <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group has-right-icon">
                              <label class="input-label">Password <span>*</span></label>
                              <div class="input-field input-field-icon">
                                <input type="password" class="form-control" required>
                                <span class="input-icon"><i class="fa-regular fa-eye-slash"></i></span>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group has-right-icon">
                              <label class="input-label">Confirm Password <span>*</span></label>
                              <div class="input-field input-field-icon">
                                <input type="password" class="form-control" required>
                                <span class="input-icon"><i class="fa-regular fa-eye-slash"></i></span>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="td-form-group">
                              <label class="input-label">Date of Birth <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" placeholder="Date Posted" id="flatpickr-date">
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="content-separation">
                              <p>Verification information</p>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="td-form-group">
                              <label class="input-label">Name <span>*</span></label>
                              <div class="input-field">
                                <input type="text" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="td-form-group">
                              <label class="input-label">Massage <span>*</span></label>
                              <div class="input-field">
                                <textarea></textarea>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="common-upload-image-system">
                              <div class="title">
                                <div class="left">
                                  <p>NID Photo<sup>*</sup></p>
                                </div>
                              </div>
                              <div class="upload-container">
                                <div for="fileInput1" class="upload-thumb">
                                  <div class="upload-thumb-inner">
                                    <input type="file" class="file-upload-input" id="fileInput1" multiple hidden>
                                    <div class="upload-thumb-img">
                                      <!-- Preview images will appear here -->
                                    </div>
                                    <div class="upload-thumb-content">
                                      <h4><a href="#" class="attach-file">Attach File</a> Or Drag & Drop</h4>
                                      <p>JPEG/PNG/PDF/Docs file</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="auth-checking">
                              <div class="auth-checkbox">
                                <div class="animate-custom">
                                  <input class="inp-cbx" id="auth_remind2" type="checkbox">
                                  <label class="cbx" for="auth_remind2">
                                    <span>
                                      <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                      </svg>
                                    </span>
                                    <span>I agree with <a href="terms-and-condition.html">Terms & Conditions</a></span>
                                  </label>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-12">
                            <div class="auth-action">
                              <button class="primary-button xl-btn w-100">
                                Register
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="switch-page">
                      <p>Already have an account? <a href="sign-in.html">Sign In</a></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="auth-element">
        <img src="../assets/images/auth/auth-shape.svg" alt="">
      </div>
    </div>
    <!-- auth area end -->

  </main>
  <!-- Body main wrapper end -->

  <!-- JS here -->
  <script src="../assets/js/jquery-3.7.1.min.js"></script>
  <script src="../assets/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/js/jquery.nice-select.min.js"></script>
  <script src="../assets/js/magnific-popup.min.js"></script>
  <script src="../assets/js/swiper.min.js"></script>
  <script src="../assets/js/jarallax.min.js"></script>
  <script src="../assets/js/iconify.min.js"></script>
  <script src="../assets/js/moment.min.js"></script>
  <script src="../assets/js/select2.js"></script>
  <script src="../assets/js/flatpickr.js"></script>
  <script src="../assets/js/flatpicker-activation.js"></script>
  <script src="../assets/js/cookie.js"></script>
  <script src="../assets/js/main.js"></script>
  <script>
    $(document).ready(function () {
      // Initialize all upload thumbs
      $('.upload-thumb').each(function () {
        initUploadThumb($(this));
      });

      function initUploadThumb($thumb) {
        const $input = $thumb.find('.file-upload-input');
        const $thumbImg = $thumb.find('.upload-thumb-img');
        const $thumbContent = $thumb.find('.upload-thumb-content');
        const $attachFile = $thumb.find('.attach-file');

        // Click handler for attach file link
        $attachFile.on('click', function (e) {
          e.preventDefault();
          $input.click();
        });

        // Click handler for the whole thumb area
        $thumb.on('click', function (e) {
          // Don't trigger if click originated from delete button or its children
          if ($(e.target).closest('.delete-btn').length) {
            return;
          }

          if ($(e.target).is('.upload-thumb') ||
            $(e.target).is('.upload-thumb-content') ||
            $(e.target).is('.upload-thumb-content *:not(.attach-file)')) {
            $input.click();
          }
        });

        // File input change handler
        $input.on('change', function (e) {
          const files = e.target.files;
          if (files.length > 0) {
            processFiles(files, $thumbImg, $thumbContent, $input);
          }
        });

        // Drag and drop handlers
        $thumb.on('dragover', function (e) {
          e.preventDefault();
          e.stopPropagation();
          $(this).css('border-color', 'var(--td-secondary)');
        });

        $thumb.on('dragleave', function (e) {
          e.preventDefault();
          e.stopPropagation();
          $(this).css('border-color', 'rgba(48, 48, 48, 0.30)');
        });

        $thumb.on('drop', function (e) {
          e.preventDefault();
          e.stopPropagation();
          $(this).css('border-color', 'rgba(48, 48, 48, 0.30)');

          const files = e.originalEvent.dataTransfer.files;
          if (files.length > 0) {
            $input[0].files = files;
            $input.trigger('change');
          }
        });
      }

      // Process uploaded files
      function processFiles(files, $uploadThumbImg, $uploadThumbContent, $input) {
        // Clear previous files if you want to replace them
        $uploadThumbImg.empty();

        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const imageBox = $('<div class="image-box"></div>');

          // Create delete button
          const deleteBtn = $('<span class="delete-btn">×</span>')
            .css({
              'position': 'absolute',
              'top': '5px',
              'right': '5px',
              'width': '20px',
              'height': '20px',
              'background': 'rgba(0,0,0,0.5)',
              'color': 'white',
              'border-radius': '50%',
              'display': 'flex',
              'align-items': 'center',
              'justify-content': 'center',
              'cursor': 'pointer',
              'font-size': '14px',
              'z-index': '10'
            })
            .on('click', function (e) {
              e.preventDefault();
              e.stopPropagation();
              $(this).parent().remove();
              checkEmptyState($uploadThumbImg, $uploadThumbContent, $input);
            });

          imageBox.append(deleteBtn);

          if (file.type.match('image.*')) {
            const reader = new FileReader();
            reader.onload = function (e) {
              const img = $('<img>').attr('src', e.target.result);
              imageBox.append(img);
              $uploadThumbImg.append(imageBox);
              updateUploadState($uploadThumbImg, $uploadThumbContent);
            };
            reader.readAsDataURL(file);
          } else {
            const fileInfo = $('<div></div>')
              .text(file.name)
              .css({
                'width': '100%',
                'height': '100%',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'padding': '10px',
                'word-break': 'break-all'
              });
            imageBox.append(fileInfo);
            $uploadThumbImg.append(imageBox);
            updateUploadState($uploadThumbImg, $uploadThumbContent);
          }
        }
      }

      function updateUploadState($uploadThumbImg, $uploadThumbContent) {
        $uploadThumbImg.addClass('has-img');
        $uploadThumbContent.addClass('has-img');
      }

      function checkEmptyState($uploadThumbImg, $uploadThumbContent, $input) {
        if ($uploadThumbImg.children().length === 0) {
          $uploadThumbImg.removeClass('has-img');
          $uploadThumbContent.removeClass('has-img');
          $input.val('');
        }
      }
    });
  </script>
</body>

</html>