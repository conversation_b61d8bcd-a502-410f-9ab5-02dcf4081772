@use '../../utils' as *;

/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.header {
  height: 80px;
  display: flex;
  align-items: center;
  background-color: var(--td-white);
  justify-content: center;
  width: 100%;
  // position: fixed;
  // top: 0;
  // left: 0;
  // right: 0;
  // z-index: 99;

  // position: fixed;
  // padding: 24px 0;
  // top: 0;
  // left: 0;
  // right: 0;
  // z-index: 99;

  // &.has-start-scroll {
  //     background-color: #174AFF;
  //     transition: all 0.3s ease-in-out;
  // }

  // &.has-bordered {
  //     border-bottom: 1px solid var(--td-card-bg-1);
  // }

  // @media #{$xl} {
  //     padding: 26px 0;
  // }

  // @media #{$lg} {
  //     padding: 22px 0;
  // }

  // @media #{$md} {
  //     padding: 20px 0;
  // }

  // @media #{$xs} {
  //     padding: 15px 0;
  // }

  // @media #{$sm} {
  //     padding: 20px 0;
  // }

  &-full {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .left {
      display: flex;
      align-items: center;
      gap: 32px;

      .logo {
        img {
          height: 35px;

          @media #{$xs} {
            height: 22px;
          }

          @media #{$sm} {
            height: 30px;
          }
        }
      }

      .navigation-menu {
        ul {
          list-style-type: none;
          display: flex;
          justify-content: center;
          gap: 20px;

          li {
            a {
              font-size: 14px;
              font-weight: 600;
              line-height: lh(26, 14);
              transition: all 0.3s ease-in-out;

              &:hover {
                color: var(--td-white);
              }
            }
          }
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 8px;

      .navigation-menu {
        margin-inline-end: 30px;

        @media #{$xl,$lg,$md,$xs} {
          margin-inline-end: 10px;
        }

        @media #{$lg,$md,$xs} {
          display: none;
        }

        ul {
          list-style-type: none;
          display: flex;
          justify-content: center;
          gap: 20px;

          @media #{$xl} {
            gap: 16px;
          }

          @media #{$lg,$md,$xs} {
            gap: 16px;
          }

          li {
            a {
              color: var(--td-heading);
              font-size: 14px;
              font-weight: 600;
              line-height: normal;
              position: relative;
              display: inline-block;
              transition: all 0.3s ease-in-out;

              &:after {
                content: "";
                position: absolute;
                inset-inline-start: 0;
                bottom: 0px;
                width: 0;
                height: 1px;
                background-color: var(--td-secondary);
                transition: width 0.3s ease-in-out;
              }

              &:hover,
              &.active {
                color: var(--td-secondary);
              }

              &:hover::after,
              &.active::after {
                width: 100%;
              }
            }
          }

        }
      }

      .auth-language-cta {
        display: flex;
        align-items: center;

        .auth-language-cta-inside {
          display: flex;
          align-items: center;
          background: var(--td-card-bg-2);
          border-radius: 14px;
        }

        .language-button {
          @media #{$lg,$md,$xs} {
            display: none;
          }

          .nice-select {
            color: var(--td-heading);
            font-size: 14px;
            font-weight: 600;
            border: solid 1px transparent;
            border-radius: 0px;
            background-color: transparent;
            height: 42px;
            line-height: 40px;
            padding-left: 18px;
            padding-right: 30px;

            &::after {
              border-bottom: 1px solid #303030;
              border-right: 1px solid #303030;
              height: 6px;
              width: 6px;
              right: 16px;
              content: '';
              display: block;
              margin-top: -4px;
              pointer-events: none;
              position: absolute;
              top: 50%;
              -webkit-transform-origin: 66% 66%;
              -ms-transform-origin: 66% 66%;
              transform-origin: 66% 66%;
              -webkit-transform: rotate(45deg);
              -ms-transform: rotate(45deg);
              transform: rotate(45deg);
              -webkit-transition: all 0.15s ease-in-out;
              transition: all 0.15s ease-in-out;
            }

            .list {
              border-radius: 10px;
              border: 1px solid rgba(48, 48, 48, 0.16);
              background: #FFF;
              padding: 10px;
              left: auto;
              right: 0;
            }
          }

          .nice-select .option:hover,
          .nice-select .option.focus,
          .nice-select .option.selected.focus {
            border-radius: 6px;
            background-color: #FFEFEA;
            color: var(--td-secondary);
            font-weight: 500;
          }
        }

        .auth-cta {
          display: flex;
          align-items: center;
          padding: 6px;
          gap: 24px;
          border-radius: 14px;

          @include rtl {
            border-radius: 14px 0px 0px 14px;
          }

          @media #{$xl,$lg,$md,$xs} {
            gap: 16px;
          }

          @media #{$lg,$md} {
            border-radius: 14px;

            @include rtl {
              border-radius: 14px;
            }
          }

          @media #{$xs} {
            padding: 0;
            border-radius: 0;
            background: transparent;
          }

          .start-selling {
            position: relative;

            .left-bar {
              display: inline-block;
              position: absolute;
              width: 1px;
              height: 22px;
              background: linear-gradient(360deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.70) 51%, rgba(255, 255, 255, 0.00) 100%);
              top: 50%;
              transform: translateY(-50%);
              inset-inline-start: 0;

              @media #{$lg,$md,$xs} {
                display: none;
              }
            }

            .right-bar {
              display: inline-block;
              position: absolute;
              width: 1px;
              height: 22px;
              background: linear-gradient(360deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.70) 51%, rgba(255, 255, 255, 0.00) 100%);
              top: 50%;
              transform: translateY(-50%);
              inset-inline-end: 0;
            }
          }

          .auth-btn {
            position: relative;

            @media #{$xl,$lg,$md,$xs} {
              padding: 0px 16px 0 16px;
            }

            @media #{$lg,$md,$xs} {
              display: none;
            }

            // &::after {
            //     position: absolute;
            //     content: '';
            //     width: 1px;
            //     height: 22px;
            //     background: linear-gradient(360deg, rgba(255, 255, 255, 0.00) 0%, rgba(255, 255, 255, 0.70) 51%, rgba(255, 255, 255, 0.00) 100%);
            //     top: 50%;
            //     transform: translateY(-50%);
            //     inset-inline-start: 0;
            // }
          }

          &-2 {
            padding: 13px 6px 13px 6px;

            @media #{$lg,$md,$xs} {
              display: none;
            }
          }
        }
      }
    }
  }
}

.full-page-overlay {
  position: absolute;
  top: 80px;
  inset-inline-start: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(8, 8, 8, 0.6);
  z-index: 3;
  visibility: hidden;
  opacity: 0;

  &.open {
    visibility: visible;
    opacity: 1;
  }

  &-dashboard {
    height: 100vh;
    width: 100vw;
  }
}


.has-container-fluid {
  padding-left: 100px;
  padding-right: 100px;
  width: 100%;

  @media #{$xl} {
    padding-left: 90px;
    padding-right: 90px;
  }

  @media #{$lg} {
    padding-left: 80px;
    padding-right: 80px;
  }

  @media #{$md} {
    padding-left: 50px;
    padding-right: 50px;
  }

  @media #{$xs} {
    padding-left: 20px;
    padding-right: 20px;
  }

  @media #{$sm} {
    padding-left: 30px;
    padding-right: 30px;
  }
}

.has-enable-disable-search-button {
  display: none;

  @media #{$xxl,$xl,$lg,$md,$xs} {
    display: block;
  }
}

.offcanvas-nav {
  margin-bottom: 20px;

  ul {
    list-style-type: none;

    li {
      margin-bottom: 6px;

      a {
        color: var(--td-white);
        transition: all 0.3s ease-in-out;

        &:hover {
          color: var(--td-white);
        }
      }
    }
  }
}

.language-switch-mobile{
  .nice-select {
    width: 100%;
    border-radius: 12px;
}

.nice-select .list {
    width: 100%;
}
}