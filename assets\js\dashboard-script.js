// Menu Nav
$(document).ready(function () {
  const applyCloseIcon = () => {
    const widthwindow = $(window).width(); // Get current window width
    if (widthwindow <= 1199) {
      $(".app-page-header").addClass("close_icon");
      $(".app-sidebar-wrapper").addClass("close_icon");
      $(".app-sidebar").removeClass("nav-folded side-nav-hover"); // Ensure hover-related classes are removed
    } else {
      $(".app-page-header").removeClass("close_icon");
      $(".app-sidebar-wrapper").removeClass("close_icon");
    }
  };

  // Apply initial state based on window size
  applyCloseIcon();

  // Update on window resize
  $(window).on("resize", applyCloseIcon);

  // Toggle classes on button click
  $(".toggle-sidebar").on("click", function () {
    const widthwindow = $(window).width(); // Check window width on click
    if (widthwindow > 1199) {
      $(".app-sidebar").toggleClass("nav-folded");
    } else {
      $(".app-sidebar").removeClass("nav-folded side-nav-hover"); // Disable hover for small screens
    }
    $(".app-page-header").toggleClass("close_icon");
    $(".app-sidebar-wrapper").toggleClass("close_icon");
    $(this).toggleClass("active");
  });

  // Add hover effect for nav-folded sidebar only for large screens
  $(document).on("mouseenter", ".app-sidebar", function () {
    const widthwindow = $(window).width(); // Get current window width dynamically
    if (widthwindow > 1199 && $(this).hasClass("nav-folded")) {
      $(this).addClass("side-nav-hover");
    }
  });

  $(document).on("mouseleave", ".app-sidebar", function () {
    const widthwindow = $(window).width(); // Get current window width dynamically
    if (widthwindow > 1199 && $(this).hasClass("nav-folded")) {
      $(this).removeClass("side-nav-hover");
    }
  });
});

// Initialize sidebar
$("#sidebar__active").on("click", function () {
  if (window.innerWidth > 0 && window.innerWidth <= 1199) {
    $(".app-sidebar").toggleClass("close_sidebar");
  } else {
    $(".app-sidebar").toggleClass("collapsed");
  }
  $(".app__offcanvas-overlay").toggleClass("overlay-open");
});

$(".app__offcanvas-overlay").on("click", function () {
  $(".app-sidebar").removeClass("collapsed");
  $(".app-sidebar").removeClass("close_sidebar");
  $(".app__offcanvas-overlay").removeClass("overlay-open");
});


$(document).ready(function () {
  $(".has-dropdown").on("click", function (e) {
    e.preventDefault();

    const $dropdown = $(this);
    const $submenu = $dropdown.next(".has-submenu-slide-content");
    const $parentSlide = $dropdown.closest(".has-submenu-slide");

    $(".has-submenu-slide-content").not($submenu).slideUp(300, function () {
      $(this).removeClass("open");
      $(this).prev(".has-dropdown").removeClass("open");
      $(this).closest(".has-submenu-slide").removeClass("open");
    });

    $dropdown.toggleClass("open");
    $parentSlide.toggleClass("open");
    $submenu.stop(true, true).slideToggle(300, function () {
      $(this).toggleClass("open", $(this).is(":visible"));
    });
  });
});


