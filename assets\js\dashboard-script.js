// Menu Nav
$(document).ready(function () {
  const applyCloseIcon = () => {
    const widthwindow = $(window).width(); // Get current window width
    if (widthwindow <= 1199) {
      $(".app-page-header").addClass("close_icon");
      $(".app-sidebar-wrapper").addClass("close_icon");
      $(".app-sidebar").removeClass("nav-folded side-nav-hover"); // Ensure hover-related classes are removed
    } else {
      $(".app-page-header").removeClass("close_icon");
      $(".app-sidebar-wrapper").removeClass("close_icon");
    }
  };

  // Apply initial state based on window size
  applyCloseIcon();

  // Update on window resize
  $(window).on("resize", applyCloseIcon);

  // Toggle classes on button click
  $(".toggle-sidebar").on("click", function () {
    const widthwindow = $(window).width(); // Check window width on click
    if (widthwindow > 1199) {
      $(".app-sidebar").toggleClass("nav-folded");
    } else {
      $(".app-sidebar").removeClass("nav-folded side-nav-hover"); // Disable hover for small screens
    }
    $(".app-page-header").toggleClass("close_icon");
    $(".app-sidebar-wrapper").toggleClass("close_icon");
    $(this).toggleClass("active");
  });

  // Add hover effect for nav-folded sidebar only for large screens
  $(document).on("mouseenter", ".app-sidebar", function () {
    const widthwindow = $(window).width(); // Get current window width dynamically
    if (widthwindow > 1199 && $(this).hasClass("nav-folded")) {
      $(this).addClass("side-nav-hover");
    }
  });

  $(document).on("mouseleave", ".app-sidebar", function () {
    const widthwindow = $(window).width(); // Get current window width dynamically
    if (widthwindow > 1199 && $(this).hasClass("nav-folded")) {
      $(this).removeClass("side-nav-hover");
    }
  });
});

// Initialize sidebar
$("#sidebar__active").on("click", function () {
  if (window.innerWidth > 0 && window.innerWidth <= 1199) {
    $(".app-sidebar").toggleClass("close_sidebar");
  } else {
    $(".app-sidebar").toggleClass("collapsed");
  }
  $(".app__offcanvas-overlay").toggleClass("overlay-open");
});

$(".app__offcanvas-overlay").on("click", function () {
  $(".app-sidebar").removeClass("collapsed");
  $(".app-sidebar").removeClass("close_sidebar");
  $(".app__offcanvas-overlay").removeClass("overlay-open");
});

// Initialize dynamic search results
$(document).ready(function () {
  // Initialize dynamic search results
  const $searchInput = $("#searchInput");
  const $searchInput2 = $("#searchInput2");
  const $searchResults = $("#searchResults");
  const $resultsList = $("#resultsList");
  const $resultsCount = $("#resultsCount");
  const $clearBtn = $("#clearBtn");

  const items = [
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M1.33398 4.0026C1.33398 2.74552 1.33398 2.11698 1.72451 1.72646C2.11503 1.33594 2.74357 1.33594 4.00065 1.33594C5.25773 1.33594 5.88627 1.33594 6.27679 1.72646C6.66732 2.11698 6.66732 2.74552 6.66732 4.0026V5.33594C6.66732 6.59302 6.66732 7.22154 6.27679 7.61207C5.88627 8.0026 5.25773 8.0026 4.00065 8.0026C2.74357 8.0026 2.11503 8.0026 1.72451 7.61207C1.33398 7.22154 1.33398 6.59302 1.33398 5.33594V4.0026Z" stroke="#9BA2AE"/>
    <path d="M1.33398 12.6641C1.33398 12.0428 1.33398 11.7322 1.43548 11.4871C1.5708 11.1605 1.83037 10.9009 2.15707 10.7655C2.4021 10.6641 2.71273 10.6641 3.33398 10.6641H4.66732C5.28857 10.6641 5.5992 10.6641 5.84423 10.7655C6.17093 10.9009 6.4305 11.1605 6.56582 11.4871C6.66732 11.7322 6.66732 12.0428 6.66732 12.6641C6.66732 13.2853 6.66732 13.5959 6.56582 13.841C6.4305 14.1677 6.17093 14.4273 5.84423 14.5626C5.5992 14.6641 5.28857 14.6641 4.66732 14.6641H3.33398C2.71273 14.6641 2.4021 14.6641 2.15707 14.5626C1.83037 14.4273 1.5708 14.1677 1.43548 13.841C1.33398 13.5959 1.33398 13.2853 1.33398 12.6641Z" stroke="#9BA2AE"/>
    <path d="M9.33398 10.6667C9.33398 9.4096 9.33398 8.78107 9.72452 8.39053C10.1151 8 10.7436 8 12.0007 8C13.2577 8 13.8862 8 14.2768 8.39053C14.6673 8.78107 14.6673 9.4096 14.6673 10.6667V12C14.6673 13.2571 14.6673 13.8856 14.2768 14.2761C13.8862 14.6667 13.2577 14.6667 12.0007 14.6667C10.7436 14.6667 10.1151 14.6667 9.72452 14.2761C9.33398 13.8856 9.33398 13.2571 9.33398 12V10.6667Z" stroke="#9BA2AE"/>
    <path d="M9.33398 3.33594C9.33398 2.71468 9.33398 2.40406 9.43545 2.15902C9.57078 1.83232 9.83038 1.57276 10.1571 1.43743C10.4021 1.33594 10.7127 1.33594 11.334 1.33594H12.6673C13.2886 1.33594 13.5992 1.33594 13.8442 1.43743C14.1709 1.57276 14.4305 1.83232 14.5658 2.15902C14.6673 2.40406 14.6673 2.71468 14.6673 3.33594C14.6673 3.95719 14.6673 4.26782 14.5658 4.51285C14.4305 4.83955 14.1709 5.09912 13.8442 5.23444C13.5992 5.33594 13.2886 5.33594 12.6673 5.33594H11.334C10.7127 5.33594 10.4021 5.33594 10.1571 5.23444C9.83038 5.09912 9.57078 4.83955 9.43545 4.51285C9.33398 4.26782 9.33398 3.95719 9.33398 3.33594Z" stroke="#9BA2AE"/>
  </svg>`,
      name: "Dashboard",
      link: "dashborad.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <g clip-path="url(#clip0_2647_24213)">
      <path d="M9.38117 2.5929C9.87797 2.05466 10.1264 1.78554 10.3903 1.62856C11.0272 1.24978 11.8114 1.238 12.459 1.59748C12.7273 1.74647 12.9833 2.00802 13.4954 2.53111C14.0074 3.0542 14.2635 3.31575 14.4093 3.58989C14.7612 4.25135 14.7497 5.05246 14.3789 5.70307C14.2252 5.97271 13.9618 6.22646 13.4349 6.73394L7.16577 12.7721C6.16729 13.7339 5.66803 14.2147 5.04407 14.4584C4.42011 14.7021 3.73417 14.6842 2.36227 14.6483L2.17562 14.6435C1.75797 14.6325 1.54915 14.6271 1.42776 14.4893C1.30637 14.3515 1.32294 14.1388 1.35609 13.7134L1.37409 13.4824C1.46737 12.2849 1.51401 11.6863 1.74784 11.1481C1.98166 10.6099 2.38499 10.1729 3.19165 9.29894L9.38117 2.5929Z" stroke="#9BA2AE" stroke-linejoin="round"/>
      <path d="M8.66797 2.66406L13.3346 7.33073" stroke="#9BA2AE" stroke-linejoin="round"/>
      <path d="M9.33203 14.6641H14.6654" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
      <clipPath id="clip0_2647_24213">
        <rect width="16" height="16" fill="white"/>
      </clipPath>
    </defs>
  </svg>
            `,
      name: "Add New Job",
      link: "add-new-job.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M7.33203 4H13.9987" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M7.33203 8H13.9987" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M7.33203 12H13.9987" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M2 10H3C3.18586 10 3.27879 10 3.35607 10.0154C3.67343 10.0785 3.9215 10.3266 3.98463 10.6439C4 10.7212 4 10.8141 4 11C4 11.1859 4 11.2788 3.98463 11.3561C3.9215 11.6734 3.67343 11.9215 3.35607 11.9846C3.27879 12 3.18586 12 3 12C2.81414 12 2.72121 12 2.64393 12.0154C2.32657 12.0785 2.0785 12.3266 2.01537 12.6439C2 12.7212 2 12.8141 2 13V13.6C2 13.7885 2 13.8829 2.05858 13.9414C2.11716 14 2.21144 14 2.4 14H4" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 2H2.8C2.91046 2 3 2.08954 3 2.2V6M3 6H2M3 6H4" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Job List",
      link: "job-list.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <g clip-path="url(#clip0_2647_24231)">
      <path d="M11.3503 2.02499L10.7139 1.85543C8.91397 1.37584 8.01397 1.13606 7.30497 1.5431C6.59594 1.95014 6.35479 2.84505 5.87249 4.63488L5.19041 7.16607C4.7081 8.95587 4.46696 9.8508 4.8763 10.5558C5.28565 11.2609 6.18564 11.5006 7.98564 11.9802L8.62204 12.1498C10.422 12.6293 11.322 12.8691 12.031 12.4621C12.74 12.0551 12.9812 11.1601 13.4634 9.37034L14.1455 6.83914C14.6278 5.0493 14.869 4.15439 14.4596 3.44938C14.0503 2.74436 13.1503 2.50457 11.3503 2.02499Z" stroke="#9BA2AE"/>
      <path d="M11.2363 4.95924C11.2363 5.50196 10.7938 5.94193 10.248 5.94193C9.70223 5.94193 9.25977 5.50196 9.25977 4.95924C9.25977 4.41652 9.70223 3.97656 10.248 3.97656C10.7938 3.97656 11.2363 4.41652 11.2363 4.95924Z" stroke="#9BA2AE"/>
      <path d="M7.9987 13.9616L7.36383 14.1345C5.56805 14.6235 4.67018 14.868 3.96283 14.4529C3.25548 14.0379 3.01489 13.1255 2.53372 11.3005L1.85325 8.71965C1.37207 6.89472 1.13149 5.98226 1.53988 5.26341C1.89315 4.64159 2.66537 4.66423 3.66537 4.66415" stroke="#9BA2AE" stroke-linecap="round"/>
    </g>
    <defs>
      <clipPath id="clip0_2647_24231">
        <rect width="16" height="16" fill="white"/>
      </clipPath>
    </defs>
  </svg>`,
      name: "Applied Candidate",
      link: "applied-candidate.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M7.33398 4H14.0007" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M7.33398 8H14.0007" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M7.33398 12H14.0007" stroke="#9BA2AE" stroke-linecap="round"/>
    <path d="M2 4.92597C2 4.92597 2.66667 5.3605 3 5.9974C3 5.9974 4 3.4974 5.33333 2.66406" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2 12.2619C2 12.2619 2.66667 12.6965 3 13.3333C3 13.3333 4 10.8333 5.33333 10" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Candidate Short Listed ",
      link: "candidate-short-list.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M11.3327 1.33594V3.33594M4.66602 1.33594V3.33594" stroke="#9BA2AE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M8.66667 2.33594H7.33333C4.81917 2.33594 3.5621 2.33594 2.78105 3.11698C2 3.89804 2 5.15511 2 7.66927V9.33594C2 11.8501 2 13.1072 2.78105 13.8882C3.5621 14.6693 4.81917 14.6693 7.33333 14.6693H8.66667C11.1808 14.6693 12.4379 14.6693 13.2189 13.8882C14 13.1072 14 11.8501 14 9.33594V7.66927C14 5.15511 14 3.89804 13.2189 3.11698C12.4379 2.33594 11.1808 2.33594 8.66667 2.33594Z" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2.33398 5.66406H13.6673" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 10.3359C6 10.3359 7 10.6693 7.33333 11.6693C7.33333 11.6693 8.78433 9.0026 10.6667 8.33594" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Meeting Schedule",
      link: "meeting-schedule.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
    <mask id="path-1-inside-1_2647_22662" fill="white">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z"/>
    </mask>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z" fill="#9BA2AE"/>
    <path d="M3.85679 3.34701L-7.53435 21.3846L7.91724 31.1426L19.9317 17.3722L3.85679 3.34701ZM6.52079 0.293681L-9.55386 -13.7319L-9.55417 -13.7315L6.52079 0.293681ZM7.16679 0V21.3333V0ZM7.81279 0.293681L23.8877 -13.7315L23.8874 -13.7319L7.81279 0.293681ZM10.4768 3.34701L-5.59817 17.3722L6.41633 31.1426L21.8679 21.3846L10.4768 3.34701ZM13.0188 1.74168L1.66676 -16.3205L1.64719 -16.3082L1.62765 -16.2959L13.0188 1.74168ZM14.3228 2.60235L35.3881 5.97368L35.3908 5.95674L35.3934 5.9398L14.3228 2.60235ZM13.1148 10.1503L-7.95047 6.77902L-7.95192 6.78809L13.1148 10.1503ZM11.9161 11.173V32.5063H11.919L11.9161 11.173ZM1.21879 10.1503L22.2858 6.79011L22.284 6.77901L1.21879 10.1503ZM0.0107882 2.60235L21.076 -0.768988L21.0757 -0.771248L0.0107882 2.60235ZM1.31479 1.74168L12.7059 -16.2959L12.6978 -16.301L1.31479 1.74168ZM1.04812 2.75635L12.4369 -15.2827L-27.4762 -40.4815L-20.0172 6.12749L1.04812 2.75635ZM2.20612 9.99235L23.2791 6.6694L23.2753 6.6453L23.2714 6.6212L2.20612 9.99235ZM2.41745 10.173L2.412 31.5063H2.41745V10.173ZM11.9161 10.173V31.5063L11.9216 31.5063L11.9161 10.173ZM12.1275 9.99235L-8.93784 6.6212L-8.94169 6.6453L-8.94549 6.6694L12.1275 9.99235ZM13.2855 2.75635L34.3507 6.12749L41.8097 -40.4815L1.89665 -15.2827L13.2855 2.75635ZM10.9075 4.25768L-0.481351 -13.7813L-0.483089 -13.7802L10.9075 4.25768ZM9.80346 4.09701L-6.27302 18.1205L-6.26446 18.1303L-6.25589 18.1401L9.80346 4.09701ZM7.16679 1.07435L23.2433 -12.9491L7.16679 -31.3792L-8.90969 -12.9491L7.16679 1.07435ZM4.53012 4.09701L20.5913 18.138L20.599 18.1292L20.6066 18.1205L4.53012 4.09701ZM3.42612 4.25768L14.8294 -13.7722L14.8221 -13.7768L14.8149 -13.7813L3.42612 4.25768ZM19.9317 17.3722L22.5957 14.3189L-9.55417 -13.7315L-12.2182 -10.6782L19.9317 17.3722ZM22.5954 14.3193C20.6735 16.522 18.3021 18.2877 15.6409 19.4975L-2.01692 -19.3438C-4.90103 -18.0326 -7.47096 -16.1191 -9.55386 -13.7319L22.5954 14.3193ZM15.6409 19.4975C12.9796 20.7074 10.0902 21.3333 7.16679 21.3333V-21.3333C3.99862 -21.3333 0.867187 -20.6549 -2.01692 -19.3438L15.6409 19.4975ZM7.16679 21.3333C4.24342 21.3333 1.35395 20.7074 -1.3073 19.4975L16.3505 -19.3438C13.4664 -20.6549 10.335 -21.3333 7.16679 -21.3333V21.3333ZM-1.3073 19.4975C-3.96855 18.2877 -6.3399 16.522 -8.26186 14.3193L23.8874 -13.7319C21.8045 -16.1191 19.2346 -18.0326 16.3505 -19.3438L-1.3073 19.4975ZM-8.26217 14.3189L-5.59817 17.3722L26.5517 -10.6782L23.8877 -13.7315L-8.26217 14.3189ZM21.8679 21.3846L24.4099 19.7792L1.62765 -16.2959L-0.914349 -14.6905L21.8679 21.3846ZM24.3708 19.8039C21.0413 21.8965 17.1796 22.986 13.2473 22.9422L13.7225 -19.7218C9.46071 -19.7693 5.27535 -18.5885 1.66676 -16.3205L24.3708 19.8039ZM13.2473 22.9422C9.315 22.8984 5.47852 21.7231 2.19643 19.5569L25.6995 -16.0528C22.1424 -18.4006 17.9844 -19.6744 13.7225 -19.7218L13.2473 22.9422ZM2.19643 19.5569C-1.0857 17.3906 -3.67452 14.325 -5.26063 10.7266L33.7816 -6.48246C32.0625 -10.3825 29.2567 -13.705 25.6995 -16.0528L2.19643 19.5569ZM-5.26063 10.7266C-6.84678 7.12808 -7.36308 3.14897 -6.74787 -0.735107L35.3934 5.9398C36.0602 1.73021 35.5006 -2.58238 33.7816 -6.48246L-5.26063 10.7266ZM-6.74247 -0.768984L-7.95047 6.77902L34.18 13.5217L35.3881 5.97368L-6.74247 -0.768984ZM-7.95192 6.78809C-7.19763 2.06195 -4.78328 -2.24047 -1.14244 -5.34673L26.5503 27.1119C30.6305 23.6307 33.3362 18.8091 34.1815 13.5126L-7.95192 6.78809ZM-1.14244 -5.34673C2.49846 -8.45304 7.12742 -10.1597 11.9133 -10.1603L11.919 32.5063C17.2824 32.5056 22.47 30.593 26.5503 27.1119L-1.14244 -5.34673ZM11.9161 -10.1603H2.41745V32.5063H11.9161V-10.1603ZM2.41745 -10.1603C12.2988 -10.1603 20.7271 -2.98201 22.2858 6.79011L-19.8482 13.5106C-18.1016 24.4614 -8.65593 32.5063 2.41745 32.5063V-10.1603ZM22.284 6.77901L21.076 -0.768988L-21.0545 5.97368L-19.8465 13.5217L22.284 6.77901ZM21.0757 -0.771248C21.6985 3.11782 21.1865 7.10401 19.601 10.7094L-19.4559 -6.46612C-21.1742 -2.55879 -21.7291 1.7612 -21.0541 5.97594L21.0757 -0.771248ZM19.601 10.7094C18.0155 14.3148 15.4238 17.3865 12.1366 19.5561L-11.3665 -16.0536C-14.9289 -13.7023 -17.7377 -10.3734 -19.4559 -6.46612L19.601 10.7094ZM12.1366 19.5561C8.84944 21.7257 5.00627 22.9012 1.06784 22.9417L0.629201 -19.7227C-3.63904 -19.6788 -7.80404 -18.4049 -11.3665 -16.0536L12.1366 19.5561ZM1.06784 22.9417C-2.87058 22.9822 -6.73711 21.8859 -10.0682 19.7844L12.6978 -16.301C9.08774 -18.5786 4.89743 -19.7666 0.629201 -19.7227L1.06784 22.9417ZM-10.0764 19.7792L-7.53435 21.3846L15.2479 -14.6905L12.7059 -16.2959L-10.0764 19.7792ZM-20.0172 6.12749L-18.8592 13.3635L23.2714 6.6212L22.1134 -0.614798L-20.0172 6.12749ZM-18.8668 13.3153C-18.0675 18.3846 -15.485 23.0023 -11.584 26.3372L16.1409 -6.09384C19.9644 -2.82516 22.4956 1.70081 23.2791 6.6694L-18.8668 13.3153ZM-11.584 26.3372C-7.68289 29.6722 -2.71973 31.505 2.412 31.5063L2.42291 -11.1603C7.4527 -11.159 12.3173 -9.36264 16.1409 -6.09384L-11.584 26.3372ZM2.41745 31.5063H11.9161V-11.1603H2.41745V31.5063ZM11.9216 31.5063C17.0534 31.505 22.0165 29.6722 25.9176 26.3372L-1.80735 -6.09384C2.01625 -9.36259 6.88081 -11.159 11.9107 -11.1603L11.9216 31.5063ZM25.9176 26.3372C29.8185 23.0023 32.401 18.3847 33.2004 13.3153L-8.94549 6.6694C-8.16199 1.70073 -5.6308 -2.82521 -1.80735 -6.09384L25.9176 26.3372ZM33.1927 13.3635L34.3507 6.12749L-7.77984 -0.614797L-8.93784 6.6212L33.1927 13.3635ZM1.89665 -15.2827L-0.481351 -13.7813L22.2963 22.2967L24.6743 20.7954L1.89665 -15.2827ZM-0.483089 -13.7802C3.64446 -16.3867 8.5674 -17.4327 13.3981 -16.7297L7.25347 25.4922C12.489 26.2542 17.8245 25.1205 22.298 22.2956L-0.483089 -13.7802ZM13.3981 -16.7297C18.2288 -16.0267 22.6494 -13.6209 25.8628 -9.94609L-6.25589 18.1401C-2.77316 22.1229 2.01792 24.7303 7.25347 25.4922L13.3981 -16.7297ZM25.8799 -9.92647L23.2433 -12.9491L-8.90969 15.0978L-6.27302 18.1205L25.8799 -9.92647ZM-8.90969 -12.9491L-11.5464 -9.92647L20.6066 18.1205L23.2433 15.0978L-8.90969 -12.9491ZM-11.5311 -9.94393C-4.90251 -17.5263 6.28177 -19.1782 14.8294 -13.7722L-7.97712 22.2876C1.28514 28.1456 13.4068 26.3563 20.5913 18.138L-11.5311 -9.94393ZM14.8149 -13.7813L12.4369 -15.2827L-10.3407 20.7954L-7.96268 22.2967L14.8149 -13.7813Z" fill="#9BA2AE" mask="url(#path-1-inside-1_2647_22662)"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5246 7.67188C12.6572 7.67187 12.7844 7.72455 12.8781 7.81832C12.9719 7.91209 13.0246 8.03927 13.0246 8.17188C13.0246 8.30448 12.9719 8.43166 12.8781 8.52543C12.7844 8.6192 12.6572 8.67188 12.5246 8.67188H1.80859C1.67599 8.67188 1.54881 8.6192 1.45504 8.52543C1.36127 8.43166 1.30859 8.30448 1.30859 8.17188C1.30859 8.03927 1.36127 7.91209 1.45504 7.81832C1.54881 7.72455 1.67599 7.67188 1.80859 7.67188H12.5246Z" fill="#9BA2AE"/>
  </svg>`,
      name: "Membership",
      link: "membership.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M4.38505 10.3237C3.44187 10.8853 0.968909 12.032 2.47511 13.467C3.21088 14.168 4.03033 14.6693 5.06058 14.6693H10.9394C11.9697 14.6693 12.7891 14.168 13.5249 13.467C15.0311 12.032 12.5581 10.8853 11.6149 10.3237C9.4032 9.00669 6.5968 9.00669 4.38505 10.3237Z" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M11 4.33594C11 5.99279 9.65687 7.33594 8 7.33594C6.34315 7.33594 5 5.99279 5 4.33594C5 2.67908 6.34315 1.33594 8 1.33594C9.65687 1.33594 11 2.67908 11 4.33594Z" stroke="#9BA2AE"/>
  </svg>`,
      name: "Company Profile",
      link: "company-profile.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M9.44576 13.9244C12.2344 13.7391 14.4558 11.4862 14.6386 8.658C14.6743 8.10453 14.6743 7.53133 14.6386 6.97786C14.4558 4.14965 12.2344 1.89682 9.44576 1.71144C8.49436 1.6482 7.5011 1.64834 6.55163 1.71144C3.76296 1.89682 1.54164 4.14965 1.35886 6.97786C1.32309 7.53133 1.32309 8.10453 1.35886 8.658C1.42543 9.68806 1.88098 10.6418 2.4173 11.4471C2.7287 12.0109 2.52319 12.7146 2.19884 13.3293C1.96497 13.7725 1.84804 13.9941 1.94192 14.1541C2.03582 14.3142 2.24554 14.3193 2.66498 14.3295C3.49448 14.3497 4.05382 14.1145 4.49782 13.7871C4.74963 13.6015 4.87554 13.5086 4.96232 13.4979C5.0491 13.4873 5.21988 13.5576 5.56137 13.6983C5.8683 13.8247 6.22467 13.9027 6.55163 13.9244C7.5011 13.9875 8.49436 13.9877 9.44576 13.9244Z" stroke="#9BA2AE" stroke-linejoin="round"/>
    <path d="M5.66797 9.66927H10.3346M5.66797 6.33594H8.0013" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Message",
      link: "message.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M3.43891 7.66327C3.38993 8.59394 3.44624 9.5846 2.61475 10.2082C2.22776 10.4985 2 10.9539 2 11.4377C2 12.1031 2.5212 12.6693 3.2 12.6693H12.8C13.4788 12.6693 14 12.1031 14 11.4377C14 10.9539 13.7723 10.4985 13.3853 10.2082C12.5537 9.5846 12.6101 8.59394 12.5611 7.66327C12.4334 5.23742 10.4292 3.33594 8 3.33594C5.57078 3.33594 3.56659 5.23742 3.43891 7.66327Z" stroke="#9BA2AE" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M7 2.08594C7 2.63822 7.44773 3.33594 8 3.33594C8.55227 3.33594 9 2.63822 9 2.08594C9 1.53365 8.55227 1.33594 8 1.33594C7.44773 1.33594 7 1.53365 7 2.08594Z" stroke="#9BA2AE" stroke-width="1.25"/>
    <path d="M10 12.6641C10 13.7687 9.1046 14.6641 8 14.6641C6.8954 14.6641 6 13.7687 6 12.6641" stroke="#9BA2AE" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Notification",
      link: "notification.html"
    },
  ];
  const items2 = [
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M1.33398 4.0026C1.33398 2.74552 1.33398 2.11698 1.72451 1.72646C2.11503 1.33594 2.74357 1.33594 4.00065 1.33594C5.25773 1.33594 5.88627 1.33594 6.27679 1.72646C6.66732 2.11698 6.66732 2.74552 6.66732 4.0026V5.33594C6.66732 6.59302 6.66732 7.22154 6.27679 7.61207C5.88627 8.0026 5.25773 8.0026 4.00065 8.0026C2.74357 8.0026 2.11503 8.0026 1.72451 7.61207C1.33398 7.22154 1.33398 6.59302 1.33398 5.33594V4.0026Z" stroke="#9BA2AE"/>
    <path d="M1.33398 12.6641C1.33398 12.0428 1.33398 11.7322 1.43548 11.4871C1.5708 11.1605 1.83037 10.9009 2.15707 10.7655C2.4021 10.6641 2.71273 10.6641 3.33398 10.6641H4.66732C5.28857 10.6641 5.5992 10.6641 5.84423 10.7655C6.17093 10.9009 6.4305 11.1605 6.56582 11.4871C6.66732 11.7322 6.66732 12.0428 6.66732 12.6641C6.66732 13.2853 6.66732 13.5959 6.56582 13.841C6.4305 14.1677 6.17093 14.4273 5.84423 14.5626C5.5992 14.6641 5.28857 14.6641 4.66732 14.6641H3.33398C2.71273 14.6641 2.4021 14.6641 2.15707 14.5626C1.83037 14.4273 1.5708 14.1677 1.43548 13.841C1.33398 13.5959 1.33398 13.2853 1.33398 12.6641Z" stroke="#9BA2AE"/>
    <path d="M9.33398 10.6667C9.33398 9.4096 9.33398 8.78107 9.72452 8.39053C10.1151 8 10.7436 8 12.0007 8C13.2577 8 13.8862 8 14.2768 8.39053C14.6673 8.78107 14.6673 9.4096 14.6673 10.6667V12C14.6673 13.2571 14.6673 13.8856 14.2768 14.2761C13.8862 14.6667 13.2577 14.6667 12.0007 14.6667C10.7436 14.6667 10.1151 14.6667 9.72452 14.2761C9.33398 13.8856 9.33398 13.2571 9.33398 12V10.6667Z" stroke="#9BA2AE"/>
    <path d="M9.33398 3.33594C9.33398 2.71468 9.33398 2.40406 9.43545 2.15902C9.57078 1.83232 9.83038 1.57276 10.1571 1.43743C10.4021 1.33594 10.7127 1.33594 11.334 1.33594H12.6673C13.2886 1.33594 13.5992 1.33594 13.8442 1.43743C14.1709 1.57276 14.4305 1.83232 14.5658 2.15902C14.6673 2.40406 14.6673 2.71468 14.6673 3.33594C14.6673 3.95719 14.6673 4.26782 14.5658 4.51285C14.4305 4.83955 14.1709 5.09912 13.8442 5.23444C13.5992 5.33594 13.2886 5.33594 12.6673 5.33594H11.334C10.7127 5.33594 10.4021 5.33594 10.1571 5.23444C9.83038 5.09912 9.57078 4.83955 9.43545 4.51285C9.33398 4.26782 9.33398 3.95719 9.33398 3.33594Z" stroke="#9BA2AE"/>
  </svg>`,
      name: "Dashboard",
      link: "job-seekers-dashborad.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <g clip-path="url(#clip0_2647_24213)">
      <path d="M9.38117 2.5929C9.87797 2.05466 10.1264 1.78554 10.3903 1.62856C11.0272 1.24978 11.8114 1.238 12.459 1.59748C12.7273 1.74647 12.9833 2.00802 13.4954 2.53111C14.0074 3.0542 14.2635 3.31575 14.4093 3.58989C14.7612 4.25135 14.7497 5.05246 14.3789 5.70307C14.2252 5.97271 13.9618 6.22646 13.4349 6.73394L7.16577 12.7721C6.16729 13.7339 5.66803 14.2147 5.04407 14.4584C4.42011 14.7021 3.73417 14.6842 2.36227 14.6483L2.17562 14.6435C1.75797 14.6325 1.54915 14.6271 1.42776 14.4893C1.30637 14.3515 1.32294 14.1388 1.35609 13.7134L1.37409 13.4824C1.46737 12.2849 1.51401 11.6863 1.74784 11.1481C1.98166 10.6099 2.38499 10.1729 3.19165 9.29894L9.38117 2.5929Z" stroke="#9BA2AE" stroke-linejoin="round"/>
      <path d="M8.66797 2.66406L13.3346 7.33073" stroke="#9BA2AE" stroke-linejoin="round"/>
      <path d="M9.33203 14.6641H14.6654" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
      <clipPath id="clip0_2647_24213">
        <rect width="16" height="16" fill="white"/>
      </clipPath>
    </defs>
  </svg>
            `,
      name: "Applied Job",
      link: "job-seekers-applied-job.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M5.52315 2.53191L5.13199 3.90096L4.81148 3.80939L5.20264 2.44034L4.73832 2.30768L5.20264 2.44034C5.30486 2.08258 5.63185 1.83594 6.00391 1.83594H8.99819C9.37021 1.83594 9.69721 2.08259 9.79942 2.44033L9.79942 2.44034L10.1906 3.80939L9.87012 3.90096L9.47895 2.53191L9.37533 2.16927H8.99819H6.00391H5.62676L5.52315 2.53191Z" stroke="#9BA2AE"/>
  <mask id="path-2-inside-1_2885_32176" fill="white">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 11.1641C9.6716 11.1641 9 11.8357 9 12.6641C9 13.4925 9.6716 14.1641 10.5 14.1641C10.7761 14.1641 11 14.3879 11 14.6641C11 14.9402 10.7761 15.1641 10.5 15.1641C9.11927 15.1641 8 14.0448 8 12.6641C8 11.2833 9.11927 10.1641 10.5 10.1641C10.7761 10.1641 11 10.3879 11 10.6641C11 10.9402 10.7761 11.1641 10.5 11.1641ZM12 10.6641C12 10.3879 12.2239 10.1641 12.5 10.1641C13.8807 10.1641 15 11.2833 15 12.6641C15 14.0448 13.8807 15.1641 12.5 15.1641C12.2239 15.1641 12 14.9402 12 14.6641C12 14.3879 12.2239 14.1641 12.5 14.1641C13.3284 14.1641 14 13.4925 14 12.6641C14 11.8357 13.3284 11.1641 12.5 11.1641C12.2239 11.1641 12 10.9402 12 10.6641ZM10 12.6641C10 12.3879 10.2239 12.1641 10.5 12.1641H12.5C12.7761 12.1641 13 12.3879 13 12.6641C13 12.9402 12.7761 13.1641 12.5 13.1641H10.5C10.2239 13.1641 10 12.9402 10 12.6641Z"/>
  </mask>
  <path d="M10.5 9.66406C8.84317 9.66406 7.5 11.0072 7.5 12.6641H10.5V9.66406ZM7.5 12.6641C7.5 14.3209 8.84317 15.6641 10.5 15.6641V12.6641H7.5ZM10.5 15.6641C9.94771 15.6641 9.5 15.2164 9.5 14.6641H12.5C12.5 13.5595 11.6046 12.6641 10.5 12.6641V15.6641ZM9.5 14.6641C9.5 14.1118 9.94771 13.6641 10.5 13.6641V16.6641C11.6046 16.6641 12.5 15.7686 12.5 14.6641H9.5ZM10.5 13.6641C9.94769 13.6641 9.5 13.2164 9.5 12.6641H6.5C6.5 14.8732 8.29084 16.6641 10.5 16.6641V13.6641ZM9.5 12.6641C9.5 12.1118 9.94769 11.6641 10.5 11.6641V8.66406C8.29084 8.66406 6.5 10.4549 6.5 12.6641H9.5ZM10.5 11.6641C9.94771 11.6641 9.5 11.2164 9.5 10.6641H12.5C12.5 9.5595 11.6046 8.66406 10.5 8.66406V11.6641ZM9.5 10.6641C9.5 10.1118 9.94771 9.66406 10.5 9.66406V12.6641C11.6046 12.6641 12.5 11.7686 12.5 10.6641H9.5ZM13.5 10.6641C13.5 11.2164 13.0523 11.6641 12.5 11.6641V8.66406C11.3954 8.66406 10.5 9.5595 10.5 10.6641H13.5ZM12.5 11.6641C13.0523 11.6641 13.5 12.1118 13.5 12.6641H16.5C16.5 10.4549 14.7092 8.66406 12.5 8.66406V11.6641ZM13.5 12.6641C13.5 13.2164 13.0523 13.6641 12.5 13.6641V16.6641C14.7092 16.6641 16.5 14.8732 16.5 12.6641H13.5ZM12.5 13.6641C13.0523 13.6641 13.5 14.1118 13.5 14.6641H10.5C10.5 15.7686 11.3954 16.6641 12.5 16.6641V13.6641ZM13.5 14.6641C13.5 15.2164 13.0523 15.6641 12.5 15.6641V12.6641C11.3954 12.6641 10.5 13.5595 10.5 14.6641H13.5ZM12.5 15.6641C14.1568 15.6641 15.5 14.3209 15.5 12.6641H12.5V15.6641ZM15.5 12.6641C15.5 11.0072 14.1568 9.66406 12.5 9.66406V12.6641H15.5ZM12.5 9.66406C13.0523 9.66406 13.5 10.1118 13.5 10.6641H10.5C10.5 11.7686 11.3954 12.6641 12.5 12.6641V9.66406ZM11.5 12.6641C11.5 13.2164 11.0523 13.6641 10.5 13.6641V10.6641C9.39544 10.6641 8.5 11.5595 8.5 12.6641H11.5ZM10.5 13.6641H12.5V10.6641H10.5V13.6641ZM12.5 13.6641C11.9477 13.6641 11.5 13.2164 11.5 12.6641H14.5C14.5 11.5595 13.6046 10.6641 12.5 10.6641V13.6641ZM11.5 12.6641C11.5 12.1118 11.9477 11.6641 12.5 11.6641V14.6641C13.6046 14.6641 14.5 13.7686 14.5 12.6641H11.5ZM12.5 11.6641H10.5V14.6641H12.5V11.6641ZM10.5 11.6641C11.0523 11.6641 11.5 12.1118 11.5 12.6641H8.5C8.5 13.7686 9.39544 14.6641 10.5 14.6641V11.6641Z" fill="#9BA2AE" mask="url(#path-2-inside-1_2885_32176)"/>
  <path d="M1.16602 5.66927C1.16602 4.93289 1.76297 4.33594 2.49935 4.33594H12.4994C13.2357 4.33594 13.8327 4.93289 13.8327 5.66927V8.89704C13.4154 8.74953 12.9665 8.66927 12.4994 8.66927H10.4994C8.29021 8.66927 6.49935 10.4601 6.49935 12.6693C6.49935 13.3977 6.69432 14.0809 7.0346 14.6693H2.49935C1.76297 14.6693 1.16602 14.0723 1.16602 13.3359V5.66927Z" stroke="#9BA2AE"/>
</svg>`,
      name: "Saved Job",
      link: "job-seekers-saved-job.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M11.3327 1.33594V3.33594M4.66602 1.33594V3.33594" stroke="#9BA2AE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M8.66667 2.33594H7.33333C4.81917 2.33594 3.5621 2.33594 2.78105 3.11698C2 3.89804 2 5.15511 2 7.66927V9.33594C2 11.8501 2 13.1072 2.78105 13.8882C3.5621 14.6693 4.81917 14.6693 7.33333 14.6693H8.66667C11.1808 14.6693 12.4379 14.6693 13.2189 13.8882C14 13.1072 14 11.8501 14 9.33594V7.66927C14 5.15511 14 3.89804 13.2189 3.11698C12.4379 2.33594 11.1808 2.33594 8.66667 2.33594Z" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2.33398 5.66406H13.6673" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M6 10.3359C6 10.3359 7 10.6693 7.33333 11.6693C7.33333 11.6693 8.78433 9.0026 10.6667 8.33594" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
      name: "Meeting Schedule",
      link: "job-seekers-meeting-schedule.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
  <mask id="path-1-inside-1_2885_32200" fill="white">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z"/>
  </mask>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z" fill="#9BA2AE"/>
  <path d="M3.85679 3.34701L-7.53435 21.3846L7.91724 31.1426L19.9317 17.3722L3.85679 3.34701ZM6.52079 0.293681L-9.55386 -13.7319L-9.55417 -13.7315L6.52079 0.293681ZM7.16679 0V21.3333V0ZM7.81279 0.293681L23.8877 -13.7315L23.8874 -13.7319L7.81279 0.293681ZM10.4768 3.34701L-5.59817 17.3722L6.41633 31.1426L21.8679 21.3846L10.4768 3.34701ZM13.0188 1.74168L1.66676 -16.3205L1.64719 -16.3082L1.62765 -16.2959L13.0188 1.74168ZM14.3228 2.60235L35.3881 5.97368L35.3908 5.95674L35.3934 5.9398L14.3228 2.60235ZM13.1148 10.1503L-7.95047 6.77902L-7.95192 6.78809L13.1148 10.1503ZM11.9161 11.173V32.5063H11.919L11.9161 11.173ZM1.21879 10.1503L22.2858 6.79011L22.284 6.77901L1.21879 10.1503ZM0.0107882 2.60235L21.076 -0.768988L21.0757 -0.771248L0.0107882 2.60235ZM1.31479 1.74168L12.7059 -16.2959L12.6978 -16.301L1.31479 1.74168ZM1.04812 2.75635L12.4369 -15.2827L-27.4762 -40.4815L-20.0172 6.12749L1.04812 2.75635ZM2.20612 9.99235L23.2791 6.6694L23.2753 6.6453L23.2714 6.6212L2.20612 9.99235ZM2.41745 10.173L2.412 31.5063H2.41745V10.173ZM11.9161 10.173V31.5063L11.9216 31.5063L11.9161 10.173ZM12.1275 9.99235L-8.93784 6.6212L-8.94169 6.6453L-8.94549 6.6694L12.1275 9.99235ZM13.2855 2.75635L34.3507 6.12749L41.8097 -40.4815L1.89665 -15.2827L13.2855 2.75635ZM10.9075 4.25768L-0.481351 -13.7813L-0.483089 -13.7802L10.9075 4.25768ZM9.80346 4.09701L-6.27302 18.1205L-6.26446 18.1303L-6.25589 18.1401L9.80346 4.09701ZM7.16679 1.07435L23.2433 -12.9491L7.16679 -31.3792L-8.90969 -12.9491L7.16679 1.07435ZM4.53012 4.09701L20.5913 18.138L20.599 18.1292L20.6066 18.1205L4.53012 4.09701ZM3.42612 4.25768L14.8294 -13.7722L14.8221 -13.7768L14.8149 -13.7813L3.42612 4.25768ZM19.9317 17.3722L22.5957 14.3189L-9.55417 -13.7315L-12.2182 -10.6782L19.9317 17.3722ZM22.5954 14.3193C20.6735 16.522 18.3021 18.2877 15.6409 19.4975L-2.01692 -19.3438C-4.90103 -18.0326 -7.47096 -16.1191 -9.55386 -13.7319L22.5954 14.3193ZM15.6409 19.4975C12.9796 20.7074 10.0902 21.3333 7.16679 21.3333V-21.3333C3.99862 -21.3333 0.867187 -20.6549 -2.01692 -19.3438L15.6409 19.4975ZM7.16679 21.3333C4.24342 21.3333 1.35395 20.7074 -1.3073 19.4975L16.3505 -19.3438C13.4664 -20.6549 10.335 -21.3333 7.16679 -21.3333V21.3333ZM-1.3073 19.4975C-3.96855 18.2877 -6.3399 16.522 -8.26186 14.3193L23.8874 -13.7319C21.8045 -16.1191 19.2346 -18.0326 16.3505 -19.3438L-1.3073 19.4975ZM-8.26217 14.3189L-5.59817 17.3722L26.5517 -10.6782L23.8877 -13.7315L-8.26217 14.3189ZM21.8679 21.3846L24.4099 19.7792L1.62765 -16.2959L-0.914349 -14.6905L21.8679 21.3846ZM24.3708 19.8039C21.0413 21.8965 17.1796 22.986 13.2473 22.9422L13.7225 -19.7218C9.46071 -19.7693 5.27535 -18.5885 1.66676 -16.3205L24.3708 19.8039ZM13.2473 22.9422C9.315 22.8984 5.47852 21.7231 2.19643 19.5569L25.6995 -16.0528C22.1424 -18.4006 17.9844 -19.6744 13.7225 -19.7218L13.2473 22.9422ZM2.19643 19.5569C-1.0857 17.3906 -3.67452 14.325 -5.26063 10.7266L33.7816 -6.48246C32.0625 -10.3825 29.2567 -13.705 25.6995 -16.0528L2.19643 19.5569ZM-5.26063 10.7266C-6.84678 7.12808 -7.36308 3.14897 -6.74787 -0.735107L35.3934 5.9398C36.0602 1.73021 35.5006 -2.58238 33.7816 -6.48246L-5.26063 10.7266ZM-6.74247 -0.768984L-7.95047 6.77902L34.18 13.5217L35.3881 5.97368L-6.74247 -0.768984ZM-7.95192 6.78809C-7.19763 2.06195 -4.78328 -2.24047 -1.14244 -5.34673L26.5503 27.1119C30.6305 23.6307 33.3362 18.8091 34.1815 13.5126L-7.95192 6.78809ZM-1.14244 -5.34673C2.49846 -8.45304 7.12742 -10.1597 11.9133 -10.1603L11.919 32.5063C17.2824 32.5056 22.47 30.593 26.5503 27.1119L-1.14244 -5.34673ZM11.9161 -10.1603H2.41745V32.5063H11.9161V-10.1603ZM2.41745 -10.1603C12.2988 -10.1603 20.7271 -2.98201 22.2858 6.79011L-19.8482 13.5106C-18.1016 24.4614 -8.65593 32.5063 2.41745 32.5063V-10.1603ZM22.284 6.77901L21.076 -0.768988L-21.0545 5.97368L-19.8465 13.5217L22.284 6.77901ZM21.0757 -0.771248C21.6985 3.11782 21.1865 7.10401 19.601 10.7094L-19.4559 -6.46612C-21.1742 -2.55879 -21.7291 1.7612 -21.0541 5.97594L21.0757 -0.771248ZM19.601 10.7094C18.0155 14.3148 15.4238 17.3865 12.1366 19.5561L-11.3665 -16.0536C-14.9289 -13.7023 -17.7377 -10.3734 -19.4559 -6.46612L19.601 10.7094ZM12.1366 19.5561C8.84944 21.7257 5.00627 22.9012 1.06784 22.9417L0.629201 -19.7227C-3.63904 -19.6788 -7.80404 -18.4049 -11.3665 -16.0536L12.1366 19.5561ZM1.06784 22.9417C-2.87058 22.9822 -6.73711 21.8859 -10.0682 19.7844L12.6978 -16.301C9.08774 -18.5786 4.89743 -19.7666 0.629201 -19.7227L1.06784 22.9417ZM-10.0764 19.7792L-7.53435 21.3846L15.2479 -14.6905L12.7059 -16.2959L-10.0764 19.7792ZM-20.0172 6.12749L-18.8592 13.3635L23.2714 6.6212L22.1134 -0.614798L-20.0172 6.12749ZM-18.8668 13.3153C-18.0675 18.3846 -15.485 23.0023 -11.584 26.3372L16.1409 -6.09384C19.9644 -2.82516 22.4956 1.70081 23.2791 6.6694L-18.8668 13.3153ZM-11.584 26.3372C-7.68289 29.6722 -2.71973 31.505 2.412 31.5063L2.42291 -11.1603C7.4527 -11.159 12.3173 -9.36264 16.1409 -6.09384L-11.584 26.3372ZM2.41745 31.5063H11.9161V-11.1603H2.41745V31.5063ZM11.9216 31.5063C17.0534 31.505 22.0165 29.6722 25.9176 26.3372L-1.80735 -6.09384C2.01625 -9.36259 6.88081 -11.159 11.9107 -11.1603L11.9216 31.5063ZM25.9176 26.3372C29.8185 23.0023 32.401 18.3847 33.2004 13.3153L-8.94549 6.6694C-8.16199 1.70073 -5.6308 -2.82521 -1.80735 -6.09384L25.9176 26.3372ZM33.1927 13.3635L34.3507 6.12749L-7.77984 -0.614797L-8.93784 6.6212L33.1927 13.3635ZM1.89665 -15.2827L-0.481351 -13.7813L22.2963 22.2967L24.6743 20.7954L1.89665 -15.2827ZM-0.483089 -13.7802C3.64446 -16.3867 8.5674 -17.4327 13.3981 -16.7297L7.25347 25.4922C12.489 26.2542 17.8245 25.1205 22.298 22.2956L-0.483089 -13.7802ZM13.3981 -16.7297C18.2288 -16.0267 22.6494 -13.6209 25.8628 -9.94609L-6.25589 18.1401C-2.77316 22.1229 2.01792 24.7303 7.25347 25.4922L13.3981 -16.7297ZM25.8799 -9.92647L23.2433 -12.9491L-8.90969 15.0978L-6.27302 18.1205L25.8799 -9.92647ZM-8.90969 -12.9491L-11.5464 -9.92647L20.6066 18.1205L23.2433 15.0978L-8.90969 -12.9491ZM-11.5311 -9.94393C-4.90251 -17.5263 6.28177 -19.1782 14.8294 -13.7722L-7.97712 22.2876C1.28514 28.1456 13.4068 26.3563 20.5913 18.138L-11.5311 -9.94393ZM14.8149 -13.7813L12.4369 -15.2827L-10.3407 20.7954L-7.96268 22.2967L14.8149 -13.7813Z" fill="#9BA2AE" mask="url(#path-1-inside-1_2885_32200)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5246 7.67188C12.6572 7.67187 12.7844 7.72455 12.8781 7.81832C12.9719 7.91209 13.0246 8.03927 13.0246 8.17188C13.0246 8.30448 12.9719 8.43166 12.8781 8.52543C12.7844 8.6192 12.6572 8.67188 12.5246 8.67188H1.80859C1.67599 8.67188 1.54881 8.6192 1.45504 8.52543C1.36127 8.43166 1.30859 8.30448 1.30859 8.17188C1.30859 8.03927 1.36127 7.91209 1.45504 7.81832C1.54881 7.72455 1.67599 7.67188 1.80859 7.67188H12.5246Z" fill="#9BA2AE"/>
</svg>`,
      name: "Membership",
      link: "job-seekers-membership.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path d="M11.3327 1.33594V3.33594M4.66602 1.33594V3.33594" stroke="#9BA2AE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M8.66667 2.33594H7.33333C4.81917 2.33594 3.5621 2.33594 2.78105 3.11698C2 3.89804 2 5.15511 2 7.66927V9.33594C2 11.8501 2 13.1072 2.78105 13.8882C3.5621 14.6693 4.81917 14.6693 7.33333 14.6693H8.66667C11.1808 14.6693 12.4379 14.6693 13.2189 13.8882C14 13.1072 14 11.8501 14 9.33594V7.66927C14 5.15511 14 3.89804 13.2189 3.11698C12.4379 2.33594 11.1808 2.33594 8.66667 2.33594Z" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M2.33398 5.66406H13.6673" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 10.3359C6 10.3359 7 10.6693 7.33333 11.6693C7.33333 11.6693 8.78433 9.0026 10.6667 8.33594" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`,
      name: "Meeting Schedule",
      link: "meeting-schedule.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
    <mask id="path-1-inside-1_2647_22662" fill="white">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z"/>
    </mask>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M3.85679 3.34701L6.52079 0.293681C6.60126 0.201451 6.70055 0.127522 6.81198 0.076866C6.9234 0.0262096 7.04439 0 7.16679 0C7.28919 0 7.41017 0.0262096 7.5216 0.076866C7.63303 0.127522 7.73232 0.201451 7.81279 0.293681L10.4768 3.34701L13.0188 1.74168C13.1583 1.65399 13.3201 1.60834 13.4849 1.61017C13.6497 1.61201 13.8105 1.66126 13.948 1.75203C14.0855 1.84281 14.194 1.97127 14.2605 2.12206C14.3269 2.27285 14.3486 2.43959 14.3228 2.60235L13.1148 10.1503C13.0693 10.4355 12.9236 10.6951 12.7039 10.8826C12.4842 11.07 12.2049 11.173 11.9161 11.173H2.41745C1.82145 11.173 1.31279 10.7397 1.21879 10.1503L0.0107882 2.60235C-0.0152904 2.43951 0.00614797 2.27261 0.0725332 2.12165C0.138918 1.97069 0.247433 1.84208 0.385068 1.75124C0.522703 1.6604 0.683618 1.61118 0.84852 1.60948C1.01342 1.60779 1.17531 1.65369 1.31479 1.74168L3.85679 3.34701ZM1.04812 2.75635L2.20612 9.99235C2.21406 10.0427 2.23971 10.0886 2.27845 10.1217C2.31719 10.1548 2.36649 10.173 2.41745 10.173H11.9161C11.9671 10.173 12.0164 10.1548 12.0551 10.1217C12.0939 10.0886 12.1195 10.0427 12.1275 9.99235L13.2855 2.75635L10.9075 4.25768C10.7345 4.3669 10.5282 4.41073 10.3258 4.38127C10.1234 4.35181 9.93811 4.251 9.80346 4.09701L7.16679 1.07435L4.53012 4.09701C4.25212 4.41501 3.78345 4.48368 3.42612 4.25768L1.04812 2.75635Z" fill="#9BA2AE"/>
    <path d="M3.85679 3.34701L-7.53435 21.3846L7.91724 31.1426L19.9317 17.3722L3.85679 3.34701ZM6.52079 0.293681L-9.55386 -13.7319L-9.55417 -13.7315L6.52079 0.293681ZM7.16679 0V21.3333V0ZM7.81279 0.293681L23.8877 -13.7315L23.8874 -13.7319L7.81279 0.293681ZM10.4768 3.34701L-5.59817 17.3722L6.41633 31.1426L21.8679 21.3846L10.4768 3.34701ZM13.0188 1.74168L1.66676 -16.3205L1.64719 -16.3082L1.62765 -16.2959L13.0188 1.74168ZM14.3228 2.60235L35.3881 5.97368L35.3908 5.95674L35.3934 5.9398L14.3228 2.60235ZM13.1148 10.1503L-7.95047 6.77902L-7.95192 6.78809L13.1148 10.1503ZM11.9161 11.173V32.5063H11.919L11.9161 11.173ZM1.21879 10.1503L22.2858 6.79011L22.284 6.77901L1.21879 10.1503ZM0.0107882 2.60235L21.076 -0.768988L21.0757 -0.771248L0.0107882 2.60235ZM1.31479 1.74168L12.7059 -16.2959L12.6978 -16.301L1.31479 1.74168ZM1.04812 2.75635L12.4369 -15.2827L-27.4762 -40.4815L-20.0172 6.12749L1.04812 2.75635ZM2.20612 9.99235L23.2791 6.6694L23.2753 6.6453L23.2714 6.6212L2.20612 9.99235ZM2.41745 10.173L2.412 31.5063H2.41745V10.173ZM11.9161 10.173V31.5063L11.9216 31.5063L11.9161 10.173ZM12.1275 9.99235L-8.93784 6.6212L-8.94169 6.6453L-8.94549 6.6694L12.1275 9.99235ZM13.2855 2.75635L34.3507 6.12749L41.8097 -40.4815L1.89665 -15.2827L13.2855 2.75635ZM10.9075 4.25768L-0.481351 -13.7813L-0.483089 -13.7802L10.9075 4.25768ZM9.80346 4.09701L-6.27302 18.1205L-6.26446 18.1303L-6.25589 18.1401L9.80346 4.09701ZM7.16679 1.07435L23.2433 -12.9491L7.16679 -31.3792L-8.90969 -12.9491L7.16679 1.07435ZM4.53012 4.09701L20.5913 18.138L20.599 18.1292L20.6066 18.1205L4.53012 4.09701ZM3.42612 4.25768L14.8294 -13.7722L14.8221 -13.7768L14.8149 -13.7813L3.42612 4.25768ZM19.9317 17.3722L22.5957 14.3189L-9.55417 -13.7315L-12.2182 -10.6782L19.9317 17.3722ZM22.5954 14.3193C20.6735 16.522 18.3021 18.2877 15.6409 19.4975L-2.01692 -19.3438C-4.90103 -18.0326 -7.47096 -16.1191 -9.55386 -13.7319L22.5954 14.3193ZM15.6409 19.4975C12.9796 20.7074 10.0902 21.3333 7.16679 21.3333V-21.3333C3.99862 -21.3333 0.867187 -20.6549 -2.01692 -19.3438L15.6409 19.4975ZM7.16679 21.3333C4.24342 21.3333 1.35395 20.7074 -1.3073 19.4975L16.3505 -19.3438C13.4664 -20.6549 10.335 -21.3333 7.16679 -21.3333V21.3333ZM-1.3073 19.4975C-3.96855 18.2877 -6.3399 16.522 -8.26186 14.3193L23.8874 -13.7319C21.8045 -16.1191 19.2346 -18.0326 16.3505 -19.3438L-1.3073 19.4975ZM-8.26217 14.3189L-5.59817 17.3722L26.5517 -10.6782L23.8877 -13.7315L-8.26217 14.3189ZM21.8679 21.3846L24.4099 19.7792L1.62765 -16.2959L-0.914349 -14.6905L21.8679 21.3846ZM24.3708 19.8039C21.0413 21.8965 17.1796 22.986 13.2473 22.9422L13.7225 -19.7218C9.46071 -19.7693 5.27535 -18.5885 1.66676 -16.3205L24.3708 19.8039ZM13.2473 22.9422C9.315 22.8984 5.47852 21.7231 2.19643 19.5569L25.6995 -16.0528C22.1424 -18.4006 17.9844 -19.6744 13.7225 -19.7218L13.2473 22.9422ZM2.19643 19.5569C-1.0857 17.3906 -3.67452 14.325 -5.26063 10.7266L33.7816 -6.48246C32.0625 -10.3825 29.2567 -13.705 25.6995 -16.0528L2.19643 19.5569ZM-5.26063 10.7266C-6.84678 7.12808 -7.36308 3.14897 -6.74787 -0.735107L35.3934 5.9398C36.0602 1.73021 35.5006 -2.58238 33.7816 -6.48246L-5.26063 10.7266ZM-6.74247 -0.768984L-7.95047 6.77902L34.18 13.5217L35.3881 5.97368L-6.74247 -0.768984ZM-7.95192 6.78809C-7.19763 2.06195 -4.78328 -2.24047 -1.14244 -5.34673L26.5503 27.1119C30.6305 23.6307 33.3362 18.8091 34.1815 13.5126L-7.95192 6.78809ZM-1.14244 -5.34673C2.49846 -8.45304 7.12742 -10.1597 11.9133 -10.1603L11.919 32.5063C17.2824 32.5056 22.47 30.593 26.5503 27.1119L-1.14244 -5.34673ZM11.9161 -10.1603H2.41745V32.5063H11.9161V-10.1603ZM2.41745 -10.1603C12.2988 -10.1603 20.7271 -2.98201 22.2858 6.79011L-19.8482 13.5106C-18.1016 24.4614 -8.65593 32.5063 2.41745 32.5063V-10.1603ZM22.284 6.77901L21.076 -0.768988L-21.0545 5.97368L-19.8465 13.5217L22.284 6.77901ZM21.0757 -0.771248C21.6985 3.11782 21.1865 7.10401 19.601 10.7094L-19.4559 -6.46612C-21.1742 -2.55879 -21.7291 1.7612 -21.0541 5.97594L21.0757 -0.771248ZM19.601 10.7094C18.0155 14.3148 15.4238 17.3865 12.1366 19.5561L-11.3665 -16.0536C-14.9289 -13.7023 -17.7377 -10.3734 -19.4559 -6.46612L19.601 10.7094ZM12.1366 19.5561C8.84944 21.7257 5.00627 22.9012 1.06784 22.9417L0.629201 -19.7227C-3.63904 -19.6788 -7.80404 -18.4049 -11.3665 -16.0536L12.1366 19.5561ZM1.06784 22.9417C-2.87058 22.9822 -6.73711 21.8859 -10.0682 19.7844L12.6978 -16.301C9.08774 -18.5786 4.89743 -19.7666 0.629201 -19.7227L1.06784 22.9417ZM-10.0764 19.7792L-7.53435 21.3846L15.2479 -14.6905L12.7059 -16.2959L-10.0764 19.7792ZM-20.0172 6.12749L-18.8592 13.3635L23.2714 6.6212L22.1134 -0.614798L-20.0172 6.12749ZM-18.8668 13.3153C-18.0675 18.3846 -15.485 23.0023 -11.584 26.3372L16.1409 -6.09384C19.9644 -2.82516 22.4956 1.70081 23.2791 6.6694L-18.8668 13.3153ZM-11.584 26.3372C-7.68289 29.6722 -2.71973 31.505 2.412 31.5063L2.42291 -11.1603C7.4527 -11.159 12.3173 -9.36264 16.1409 -6.09384L-11.584 26.3372ZM2.41745 31.5063H11.9161V-11.1603H2.41745V31.5063ZM11.9216 31.5063C17.0534 31.505 22.0165 29.6722 25.9176 26.3372L-1.80735 -6.09384C2.01625 -9.36259 6.88081 -11.159 11.9107 -11.1603L11.9216 31.5063ZM25.9176 26.3372C29.8185 23.0023 32.401 18.3847 33.2004 13.3153L-8.94549 6.6694C-8.16199 1.70073 -5.6308 -2.82521 -1.80735 -6.09384L25.9176 26.3372ZM33.1927 13.3635L34.3507 6.12749L-7.77984 -0.614797L-8.93784 6.6212L33.1927 13.3635ZM1.89665 -15.2827L-0.481351 -13.7813L22.2963 22.2967L24.6743 20.7954L1.89665 -15.2827ZM-0.483089 -13.7802C3.64446 -16.3867 8.5674 -17.4327 13.3981 -16.7297L7.25347 25.4922C12.489 26.2542 17.8245 25.1205 22.298 22.2956L-0.483089 -13.7802ZM13.3981 -16.7297C18.2288 -16.0267 22.6494 -13.6209 25.8628 -9.94609L-6.25589 18.1401C-2.77316 22.1229 2.01792 24.7303 7.25347 25.4922L13.3981 -16.7297ZM25.8799 -9.92647L23.2433 -12.9491L-8.90969 15.0978L-6.27302 18.1205L25.8799 -9.92647ZM-8.90969 -12.9491L-11.5464 -9.92647L20.6066 18.1205L23.2433 15.0978L-8.90969 -12.9491ZM-11.5311 -9.94393C-4.90251 -17.5263 6.28177 -19.1782 14.8294 -13.7722L-7.97712 22.2876C1.28514 28.1456 13.4068 26.3563 20.5913 18.138L-11.5311 -9.94393ZM14.8149 -13.7813L12.4369 -15.2827L-10.3407 20.7954L-7.96268 22.2967L14.8149 -13.7813Z" fill="#9BA2AE" mask="url(#path-1-inside-1_2647_22662)"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5246 7.67188C12.6572 7.67187 12.7844 7.72455 12.8781 7.81832C12.9719 7.91209 13.0246 8.03927 13.0246 8.17188C13.0246 8.30448 12.9719 8.43166 12.8781 8.52543C12.7844 8.6192 12.6572 8.67188 12.5246 8.67188H1.80859C1.67599 8.67188 1.54881 8.6192 1.45504 8.52543C1.36127 8.43166 1.30859 8.30448 1.30859 8.17188C1.30859 8.03927 1.36127 7.91209 1.45504 7.81832C1.54881 7.72455 1.67599 7.67188 1.80859 7.67188H12.5246Z" fill="#9BA2AE"/>
  </svg>`,
      name: "Membership",
      link: "membership.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M10 14.6693V12.3359L11.9173 12.5756C12.3152 12.6253 12.6667 12.3151 12.6667 11.9141V9.66927L14 8.66927L12.6667 6.66927C12.6667 3.72375 10.2789 1.33594 7.33333 1.33594C4.38781 1.33594 2 3.72375 2 6.66927C2 9.03274 3.53738 11.0371 5.66667 11.737M4 10.8329V14.6693" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M9.33203 6.33073L10.332 7.33073L9.33203 8.33073M5.33203 6.33073L4.33203 7.33073L5.33203 8.33073M7.66536 5.66406L6.9987 8.9974" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
      name: "Skills Development Center",
      link: "job-seekers-skills-development-center.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <g clip-path="url(#clip0_2885_32214)">
    <path d="M9.33203 12.0026C9.33203 12.0026 9.9987 12.0026 10.6654 13.3359C10.6654 13.3359 12.783 10.0026 14.6654 9.33594" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M8.66537 14.6693H4.39261C3.36236 14.6693 2.54291 14.1679 1.80714 13.467C0.300942 12.032 2.7739 10.8853 3.71708 10.3237C5.83597 9.062 8.50063 9.009 10.6654 10.1647" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M11 4.33594C11 5.99279 9.65687 7.33594 8 7.33594C6.34315 7.33594 5 5.99279 5 4.33594C5 2.67908 6.34315 1.33594 8 1.33594C9.65687 1.33594 11 2.67908 11 4.33594Z" stroke="#9BA2AE"/>
  </g>
  <defs>
    <clipPath id="clip0_2885_32214">
      <rect width="16" height="16" fill="white"/>
    </clipPath>
  </defs>
</svg>`,
      name: "Who Got Hired",
      link: "job-seekers-who-got-hired .html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <g clip-path="url(#clip0_2885_27864)">
    <path d="M9.33203 12.0026C9.33203 12.0026 9.9987 12.0026 10.6654 13.3359C10.6654 13.3359 12.783 10.0026 14.6654 9.33594" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M8.66537 14.6693H4.39261C3.36236 14.6693 2.54291 14.1679 1.80714 13.467C0.300942 12.032 2.7739 10.8853 3.71708 10.3237C5.83597 9.062 8.50063 9.009 10.6654 10.1647" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M11 4.33594C11 5.99279 9.65687 7.33594 8 7.33594C6.34315 7.33594 5 5.99279 5 4.33594C5 2.67908 6.34315 1.33594 8 1.33594C9.65687 1.33594 11 2.67908 11 4.33594Z" stroke="#9BA2AE"/>
  </g>
  <defs>
    <clipPath id="clip0_2885_27864">
      <rect width="16" height="16" fill="white"/>
    </clipPath>
  </defs>
</svg>`,
      name: "View Profile",
      link: "job-seekers-profile-view.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M10 1.66406V2.66406C10 3.60687 10 4.07828 10.2929 4.37117C10.5858 4.66406 11.0572 4.66406 12 4.66406H13" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2.66797 10.6693V5.33594C2.66797 3.45032 2.66797 2.50751 3.25376 1.92172C3.83954 1.33594 4.78235 1.33594 6.66797 1.33594H9.44904C9.7215 1.33594 9.85777 1.33594 9.9803 1.38668C10.1028 1.43743 10.1992 1.53378 10.3918 1.72646L12.9441 4.27874C13.1368 4.47143 13.2332 4.56778 13.2839 4.69029C13.3346 4.8128 13.3346 4.94906 13.3346 5.22156V10.6693C13.3346 12.5549 13.3346 13.4977 12.7488 14.0835C12.163 14.6693 11.2202 14.6693 9.33464 14.6693H6.66797C4.78235 14.6693 3.83954 14.6693 3.25376 14.0835C2.66797 13.4977 2.66797 12.5549 2.66797 10.6693Z" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M5.33203 7.33594H10.6654M5.33203 9.33594H10.6654M5.33203 11.3359H8.11256" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
      name: "View CV",
      link: "job-seekers-view-cv.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M9.44576 13.9244C12.2344 13.7391 14.4558 11.4862 14.6386 8.658C14.6743 8.10453 14.6743 7.53133 14.6386 6.97786C14.4558 4.14965 12.2344 1.89682 9.44576 1.71144C8.49436 1.6482 7.5011 1.64834 6.55163 1.71144C3.76296 1.89682 1.54164 4.14965 1.35886 6.97786C1.32309 7.53133 1.32309 8.10453 1.35886 8.658C1.42543 9.68806 1.88098 10.6418 2.4173 11.4471C2.7287 12.0109 2.52319 12.7146 2.19884 13.3293C1.96497 13.7725 1.84804 13.9941 1.94192 14.1541C2.03582 14.3142 2.24554 14.3193 2.66498 14.3295C3.49448 14.3497 4.05382 14.1145 4.49782 13.7871C4.74963 13.6015 4.87554 13.5086 4.96232 13.4979C5.0491 13.4873 5.21988 13.5576 5.56137 13.6983C5.8683 13.8247 6.22467 13.9027 6.55163 13.9244C7.5011 13.9875 8.49436 13.9877 9.44576 13.9244Z" stroke="#9BA2AE" stroke-linejoin="round"/>
  <path d="M5.66797 9.66927H10.3346M5.66797 6.33594H8.0013" stroke="#9BA2AE" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
      name: "Message",
      link: "job-seekers-message.html"
    },
    {
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M3.43891 7.66327C3.38993 8.59394 3.44624 9.5846 2.61475 10.2082C2.22776 10.4985 2 10.9539 2 11.4377C2 12.1031 2.5212 12.6693 3.2 12.6693H12.8C13.4788 12.6693 14 12.1031 14 11.4377C14 10.9539 13.7723 10.4985 13.3853 10.2082C12.5537 9.5846 12.6101 8.59394 12.5611 7.66327C12.4334 5.23742 10.4292 3.33594 8 3.33594C5.57078 3.33594 3.56659 5.23742 3.43891 7.66327Z" stroke="#9BA2AE" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M7 2.08594C7 2.63822 7.44773 3.33594 8 3.33594C8.55227 3.33594 9 2.63822 9 2.08594C9 1.53365 8.55227 1.33594 8 1.33594C7.44773 1.33594 7 1.53365 7 2.08594Z" stroke="#9BA2AE" stroke-width="1.25"/>
  <path d="M10 12.6641C10 13.7687 9.1046 14.6641 8 14.6641C6.8954 14.6641 6 13.7687 6 12.6641" stroke="#9BA2AE" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
      name: "Notification",
      link: "job-seekers-notification.html"
    },
  ];

  // Function to filter and display search results
  function displayResults() {
    // Determine which input is active
    const $activeInput = $(document.activeElement).is($searchInput) ? $searchInput : $searchInput2;
    const query = $activeInput.val().toLowerCase();

    // Select the appropriate items array based on the active input
    const itemArray = $activeInput.is($searchInput) ? items : items2;

    // Filter items based on the query
    const filteredItems = itemArray.filter((item) =>
      item.name.toLowerCase().includes(query)
    );

    // Clear previous results
    $resultsList.empty();
    $resultsCount.text(filteredItems.length);

    if (filteredItems.length > 0) {
      $searchResults.show();

      // Append filtered items to the results list
      filteredItems.forEach((item) => {
        const $li = $("<li>");
        const $a = $("<a>")
          .attr("href", item.link)
          .html(`<span>${item.icon}</span> ${item.name}`);
        $li.append($a);
        $resultsList.append($li);
      });
    } else {
      $searchResults.hide();
    }
  }

  // Event listeners to trigger search on input for both searchInput and searchInput2
  $searchInput.on("input", displayResults);
  $searchInput2.on("input", displayResults);

  // Close search results when clicking outside
  $(document).on("click", function (e) {
    if (
      !$searchInput.is(e.target) &&
      !$searchInput2.is(e.target) &&
      !$searchResults.has(e.target).length &&
      !$searchResults.is(e.target)
    ) {
      $searchResults.hide();
    }
  });

  // Clear button logic for both inputs
  [$searchInput, $searchInput2].forEach(($input) => {
    $input.on("input", () => {
      $clearBtn.css("display", $input.val() ? "block" : "none");
    });
  });

  $clearBtn.on("click", () => {
    // Clear the active input
    const $activeInput = $(document.activeElement).is($searchInput) ? $searchInput : $searchInput2;
    $activeInput.val("");
    $clearBtn.css("display", "none");
    $activeInput.focus(); // Refocus the input
    displayResults(); // Update results after clearing
  });
});



//has sub menu
$(document).ready(function() {
  $('.has-sub > .sidebar-menu-item').on('click', function(e) {
    e.preventDefault();
    const parentLi = $(this).closest('.has-sub');
    
    parentLi.toggleClass('open active');
    
    const submenu = parentLi.find('.has-sub-inside-menu');
    if (parentLi.hasClass('open')) {
      submenu.slideDown(300);
    } else {
      submenu.slideUp(300);
    }

    $('.has-sub').not(parentLi).removeClass('open active').find('.has-sub-inside-menu').slideUp(300);
  });
});