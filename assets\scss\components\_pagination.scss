@use '../utils' as *;

/*----------------------------------------*/
/*  pagination
/*----------------------------------------*/
.common-pagination {
  display: flex;
  justify-content: center;
  align-items: center;

  ul {
    display: flex;
    align-items: center;
    gap: 10px;
    list-style-type: none;

    li {
      a {
        font-family: var(--td-heading-font);
        display: flex;
        width: 40px;
        height: 40px;
        padding: 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 12px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #F1F1F1;
        color: rgba(48, 48, 48, 0.60);
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        line-height: normal;

        @media #{$xs} {
          width: 30px;
          height: 30px;
          border-radius: 8px;
          font-size: 14px;
          gap: 6px;
        }

        &:hover,
        &.active {
          border: 1px solid var(--td-secondary);
          background: var(--td-secondary);
          color: var(--td-white);
        }

        &.navigation {
          &.disabled {
            cursor: not-allowed;

            .arrow {
              opacity: 0.2;
              cursor: not-allowed;
            }
          }

          &:hover,
          &.active {
            border-color: var(--td-secondary);
            background: var(--td-secondary);

            .arrow {
              color: var(--td-white);
              opacity: 1;
            }
          }
        }
      }
    }
  }
}