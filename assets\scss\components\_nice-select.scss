@use '../utils' as *;

/*----------------------------------------*/
/*  nice-select
/*----------------------------------------*/
.common-filter {
  .nice-select {
    height: 40px;
    line-height: 38px;
    color: rgba(48, 48, 48, 0.80);
    font-size: 13px;
    font-weight: 600;
    border-radius: 12px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    backdrop-filter: blur(8px);
    padding-left: 12px;
  }

  .nice-select:after {
    border-bottom: 1px solid rgba(48, 48, 48, 0.80);
    border-right: 1px solid rgba(48, 48, 48, 0.80);
    height: 6px;
    width: 6px;
  }

  .nice-select .list {
    border-radius: 10px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #FFF;
    padding: 10px;
    left: auto;
    right: 0;
    z-index: 50;
    box-shadow: none;
  }

  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    border-radius: 6px;
    background-color: #FFEFEA;
    color: var(--td-secondary);
    font-weight: 500;
  }

  &.common-filter-height-fixed {
    .nice-select .list {
      height: 250px;
      overflow-y: auto;
    }
  }
}

.auth-nice-select {
  .nice-select {
    height: 44px;
    line-height: 43px;
    border-radius: 8px;
    border: 1px solid rgba(48, 48, 48, 0.20);
    width: 100%;
  }

  .nice-select:after {
    border-bottom: 1px solid #30303099;
    border-right: 1px solid #30303099;
    height: 7px;
    width: 7px;
  }

  .nice-select .list {
    border-radius: 10px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #FFF;
    padding: 10px;
    left: auto;
    right: 0;
    z-index: 50;
    box-shadow: none;
    width: 100%;
  }

  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    border-radius: 6px;
    background-color: #FFEFEA;
    color: var(--td-secondary);
    font-weight: 500;
  }
}


.common-image-select2 {
  .select2-container .select2-selection--single {
    height: 52px;
  }

  .select2-container--default .select2-selection--single {
    border-radius: 12px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: #fff;
    color: rgba(48, 48, 48, 0.80);
  }

  .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--td-white);
    line-height: 49px;
    font-size: 14px;
    padding-inline-end: 35px;
    padding-inline-start: 14px;
  }

  .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 49px;
    position: absolute;
    top: 1px;
    inset-inline-end: 10px;
    width: 20px;
  }

  .select2-dropdown {
    background: #fff;
    border: 1px solid rgba(48, 48, 48, 0.16);
    border-radius: 0 0 12px 12px;
    overflow: hidden;
  }

  .select2-results__options{
    background: #fff;
  }

  .select2-results__option {
    background: #fff;
    
    &:hover {
      background-color: #FFEFEA;
      color: var(--td-secondary);
    }
  }

  .select2-results__option {
    padding: 10px 15px;
    user-select: none;
    -webkit-user-select: none;
    font-size: 14px;
  }

  .select2-selection__rendered span{
    color: rgba(48, 48, 48, 0.80);
  }

  .select2-selection__rendered span .img-flag {
      width: unset !important;
      height: 16px !important;
    }
  .select2-results__option span img{
    width: unset!important;
    height: 16px!important;
  }
}