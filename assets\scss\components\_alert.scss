@use "../utils" as *;

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/

.td-alert-box {
    background: #010c1a;
    padding: 16px 20px;
    z-index: 1;
    position: relative;
    transition: .3s;
    @include flexbox();
    column-gap: rem(12);
    align-items: center;
    border: rem(1) solid transparent;
    width: 351px;

    @media #{$xxs} {
        padding: rem(10) rem(12) rem(10);
        width: 300px;
    }

    .alert-content {
        @include flexbox();
        align-items: center;
        column-gap: rem(16);
        flex-grow: 1;
        overflow: hidden;

        @media #{$xs} {
            column-gap: rem(12);
        }

        .alert-title {
            font-size: 18px;
            font-weight: 500;
            font-family: var(--td-body-font);

            @media #{$xs} {
                font-size: rem(14);
            }
        }

        .alert-message {
            font-size: 14px;
            position: relative;
            margin-top: 2px;
        }
    }

    .alert-icon {
        flex: 0 0 auto;

        svg {
            width: rem(36);
            height: rem(36);
        }
    }

    .close-btn {
        padding: 5px;
        position: absolute;
        right: -8px;
        top: -8px;
        display: flex;
        width: 30px;
        height: 30px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 24px;
        background: #FFDDE0;
    }

    &.hidden {
        opacity: 0;
        transform: translateY(-50%, rem(20));
        pointer-events: none;
    }

    &.has-success {
        border-left: 4px solid #0C9;
        background: #fff;
        border-radius: 16px;
        border-top: 1px solid rgba(48, 48, 48, 0.16);
        border-right: 1px solid rgba(48, 48, 48, 0.16);
        border-bottom: 1px solid rgba(48, 48, 48, 0.16);
    }

    &.has-warning {
        border-left: 4px solid #F2C94C;
        background: #fff;
        border-radius: 16px;
        border-radius: 16px;
        border-top: 1px solid rgba(48, 48, 48, 0.16);
        border-right: 1px solid rgba(48, 48, 48, 0.16);
        border-bottom: 1px solid rgba(48, 48, 48, 0.16);
    }

    &.has-info {
        border-left: 4px solid #5458F7;
        background: #fff;
        border-radius: 16px;
        border-radius: 16px;
        border-top: 1px solid rgba(48, 48, 48, 0.16);
        border-right: 1px solid rgba(48, 48, 48, 0.16);
        border-bottom: 1px solid rgba(48, 48, 48, 0.16);
        position: relative;
        overflow: hidden;

        &::before{
            content: '';
            left: 0;
            top: 0;
            width: 1px;
            height: 100%;
            position: absolute;
            background: #5458F7;
        }
    }

    &.has-danger {
        border-left: 4px solid #E93A2D;
        background: #fff;
        border-radius: 16px;
        border-radius: 16px;
        border-top: 1px solid rgba(48, 48, 48, 0.16);
        border-right: 1px solid rgba(48, 48, 48, 0.16);
        border-bottom: 1px solid rgba(48, 48, 48, 0.16);
    }
}

.alert-show-status {
    position: fixed;
    top: rem(16);
    right: rem(16);
    z-index: 999;
}