@use '../../utils' as *;

/*----------------------------------------*/
/*  blog details
/*----------------------------------------*/
.blog-details-area {
  .blog-details-content {
    hr {
      margin: 20px 0;
      border-color: rgba(48, 48, 48, 0.10);
    }

    .share-box {
      .share {

        h6 {
          color: var(--td-heading);
          font-size: 20px;
          font-weight: 700;
          line-height: normal;
          margin-bottom: 10px;
        }

        .share-icon {
          display: flex;
          align-items: center;
          gap: 10px;

          a {
            display: flex;
            width: 30px;
            height: 30px;
            justify-content: center;
            align-items: center;
            gap: 2px;
            border-radius: 8px;
            border: 1px solid rgba(48, 48, 48, 0.16);

            .social-icon {
              font-size: 18px;
              color: var(--td-heading);
            }

            &:hover {
              border-color: var(--td-secondary);
              background: var(--td-secondary);

              .social-icon {
                color: var(--td-white);
              }
            }
          }
        }
      }
    }

    .trending-topic{
      .blog-horizontal-card{
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.text-editor-content {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    margin-top: 32px;
    border-radius: 16px;

    &:first-child {
      margin-top: 0px;
    }
  }

  h1 {
    color: var(--td-heading);
    font-size: 26px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  h2 {
    color: var(--td-heading);
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  h3 {
    color: var(--td-heading);
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  h5 {
    color: var(--td-heading);
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  h6 {
    color: var(--td-heading);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: lh(32, 20);
    margin-top: 32px;
  }

  p {
    color: var(--td-text-primary);
    font-size: 16px;
    font-weight: 400;
    line-height: lh(22, 16);
    margin-top: 10px;
  }

  ul {
    list-style-type: disc;
    padding-left: 20px;
    margin-top: 16px;

    li {
      color: var(--td-heading);
      font-size: 16px;
      font-weight: 500;
      line-height: lh(22, 16);
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}