// Variables
// Common Color
$white: #ffffff;
$black: #000000;
$placeholder: #000000;
$selection: #000000;

// Theme Color
$primary: #13a158;
$secondary: #031a0e;

// Heading Color
$heading: #151415;

// Text Color
$text-primary: rgba(8, 8, 8, 0.7);

// Border Color
$border-primary: #d7d7d7;
$td-secondary: #ff6229;

// Others Color
$yellow: #f79e1c;
$warning: #e9b722;
$success: #0bc355;
$danger: #e94e5b;
$green: #80ed99;

// Responsive Variables
$x3l: "only screen and (min-width: 1600px) and (max-width: 4000px)";
$xxl: "only screen and (min-width: 1400px) and (max-width: 1599px)";
$xl: "only screen and (min-width: 1200px) and (max-width: 1399px)";
$lg: "only screen and (min-width: 992px) and (max-width: 1199px)";
$md: "only screen and (min-width: 768px) and (max-width: 991px)";
$sm: "only screen and (min-width: 576px) and (max-width: 767px)";
$xs: "(max-width: 767px)";
$xxs: "(max-width: 480px)";
