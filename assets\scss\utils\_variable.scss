// Variables
// Common Color
$white: #ffffff;
$black: #000000;
$placeholder: #000000;
$selection: #000000;

// Theme Color
$primary: #13A158;
$secondary: #031A0E;

// Heading Color
$heading: #151415;

// Text Color 
$text-primary: rgba(8, 8, 8, 0.70);

// Border Color
$border-primary: #D7D7D7;

// Others Color
$yellow: #F79E1C;
$warning: #E9B722;
$success: #0BC355;
$danger: #E94E5B;
$green: #80ED99;

// Responsive Variables
$x3l: 'only screen and (min-width: 1600px) and (max-width: 4000px)';
$xxl: 'only screen and (min-width: 1400px) and (max-width: 1599px)';
$xl: "only screen and (min-width: 1200px) and (max-width: 1399px)";
$lg: "only screen and (min-width: 992px) and (max-width: 1199px)";
$md: "only screen and (min-width: 768px) and (max-width: 991px)";
$sm: "only screen and (min-width: 576px) and (max-width: 767px)";
$xs: "(max-width: 767px)";
$xxs: "(max-width: 480px)";