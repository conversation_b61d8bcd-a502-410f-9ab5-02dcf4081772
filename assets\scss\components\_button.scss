@use '../utils' as *;

/*----------------------------------------*/
/*  buttons
/*----------------------------------------*/
.primary-button {
  display: inline-flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-secondary);
  color: var(--td-white);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;

  &:hover {
    background: #DA4F1D;
    color: var(--td-white);
  }

  &.xl-btn {
    height: 52px;
    padding: 10px 32px;

    @media #{$xs} {
      height: 40px;
      padding: 10px 24px;
      font-size: 12px;
    }
  }
}

.secondary-button {
  display: flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-white);
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;

  &:hover {
    background: var(--td-white);
    color: var(--td-heading);
  }

  &.xl-btn {
    height: 52px;
    padding: 10px 32px;

    @media #{$xs} {
      height: 40px;
      padding: 10px 24px;
      font-size: 12px;
    }
  }
}

.view-all {
  color: var(--td-heading);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: lh(15, 14);
  transition: all 0.3s ease-in-out;

  &:hover {
    color: var(--td-secondary);
  }
}