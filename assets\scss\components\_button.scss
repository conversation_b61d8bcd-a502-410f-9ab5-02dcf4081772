@use '../utils' as *;

/*----------------------------------------*/
/*  buttons
/*----------------------------------------*/
.primary-button {
  display: inline-flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-secondary);
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  transition: all 0.3s ease-in-out;

  &:hover {
    background: #DA4F1D;
    color: var(--td-white);
  }

  &.xl-btn {
    height: 52px;
    padding: 10px 32px;
    font-size: 16px;

    @media #{$xs} {
      height: 40px;
      padding: 10px 24px;
      font-size: 12px;
    }
  }

  &.sm-btn {
    height: 32px;
    padding: 12px 18px;
    font-size: 14px;
  }

  &.md-btn{
    height: 36;
    padding: 10px 20px;
    font-size: 14px;
  }

  &.reject-btn{
    background-color: #DC3545;
    color: var(--td-white);
  }

  &.border-btn {
    border-radius: 12px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background: transparent;
    color: var(--td-secondary);

    &:hover {
      background: var(--td-secondary);
      color: var(--td-white);
    }
  }
  &.border-btn-secondary {
    border-radius: 12px;
    border: 1px solid var(--td-secondary);
    background: transparent;
    color: var(--td-secondary);

    &:hover {
      background: var(--td-secondary);
      color: var(--td-white);
    }
  }

  &.border-btn-2{
    border-radius: 12px;
    border: 2px solid var(--td-secondary);
    background: transparent;
    color: var(--td-secondary);

    &:hover {
      background: var(--td-secondary);
      color: var(--td-white);
    }
  }
}

.secondary-button {
  display: flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-white);
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;

  &:hover {
    background: var(--td-white);
    color: var(--td-heading);
  }

  &.xl-btn {
    height: 52px;
    padding: 10px 32px;
    font-size: 16px;

    @media #{$xs} {
      height: 40px;
      padding: 10px 24px;
      font-size: 12px;
    }
  }
}

.view-all {
  color: var(--td-heading);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: lh(15, 14);
  transition: all 0.3s ease-in-out;

  &:hover {
    color: var(--td-secondary);
  }
}

.tag-btn {
  display: inline-flex;
  height: 27px;
  padding: 6px 8px;
  align-items: center;
  gap: 2px;
  border-radius: 5px;
  background: rgba(255, 98, 41, 0.10);
  color: rgba(48, 48, 48, 0.80);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;

  span {
    display: flex;
    font-size: 14px;
    color: var(--td-secondary);
  }
}

.border-button {
  display: inline-flex;
  height: 30px;
  padding: 12px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid var(--td-secondary);
  background: rgba(255, 98, 41, 0.04);
  color: rgba(48, 48, 48, 0.80);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;

  span {
    display: inline-flex;
    align-items: center;

    .chat-icon {
      font-size: 16px;
      color: #6F5BFE;
    }
  }

  &:hover {
    background: var(--td-secondary);
    color: var(--td-white);
  }

  &.xl-btn {
    height: 32px;
    border-radius: 11px;
    border: 1px solid rgba(48, 48, 48, 0.16);
    background-color: transparent;
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--td-secondary);
      border: 1px solid var(--td-secondary);
    }
  }
}