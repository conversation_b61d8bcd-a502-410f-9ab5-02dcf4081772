@use '../../utils' as *;

/*----------------------------------------*/
/*  faq
/*----------------------------------------*/
.faq-area {
  .faq-main-content {

    .right {
      margin-left: 135px;

      @media #{$xl,$lg} {
        margin-left: 35px;
      }

      @media #{$md,$xs} {
        margin-left: 0px;
      }

      .faq-img {
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.faq {
  .faq-content {
    .left {
      h2 {
        width: 90%;
        margin-bottom: 16px;
        color: var(--td-white);
      }

      p {
        width: 80%;

        @media #{$xs} {
          width: 100%;
        }
      }

      .img-box {
        height: 350px;
        margin-top: 24px;

        @media #{$xs} {
          height: 240px;
        }

        @media #{$sm} {
          height: 460px;
        }

        img {
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.common-faq-box {
  .accordion {
    .accordion-item {
      margin-bottom: 16px;
      border: none;

      @media #{$xs} {
        margin-bottom: 10px;
      }

      &:first-of-type {
        border-top-left-radius: unset;
        border-top-right-radius: unset;
        border-top-left-radius: 13px;
        border-top-right-radius: 13px;
      }

      &:last-of-type {
        border-bottom-left-radius: unset;
        border-bottom-right-radius: unset;
        border-bottom-left-radius: 13px;
        border-bottom-right-radius: 13px;
      }

      .accordion-button {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--td-heading) !important;
        font-size: 16px;
        font-weight: 500;
        line-height: 120%;
        padding: 20px 40px 20px 20px;

        @media #{$xs} {
          padding: 20px 40px 20px 20px;
          font-size: 18px;
        }

        &:focus {
          box-shadow: unset;
        }

        &:not(.collapsed) {
          color: var(--td-heading) !important;
          background-color: transparent;
          box-shadow: unset;
          padding: 20px 20px 20px 20px;
          border-radius: 12px 12px 0 0;
          border: 1px solid rgba(48, 48, 48, 0.16);
          border-bottom: none;

          @media #{$xs} {
            padding: 20px 20px 20px 20px;
          }

        }

        &.collapsed {
          color: inherit;
          border-bottom-right-radius: unset;
          border-bottom-left-radius: unset;
          border-radius: 12px;
        }
      }

      .accordion-body {
        padding: unset;
        padding: 0px 50px 20px 20px;
        color: var(--td-text-primary);
        background-color: transparent;
        font-size: 14px;
        font-weight: 400;
        line-height: 150%;
        border-radius: 0 0 12px 12px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        border-top: none;

        span {
          color: var(--td-theme-bg-color);
        }
      }
    }
  }

  &-2 {
    .accordion {
      .accordion-item {
        background-color: transparent;
        border-radius: 13px;
      }
    }
  }
}

.common-faq-box {
  .accordion {
    .accordion-item {
      .accordion-button {
        position: relative;

        &::after {
          display: none !important;
        }

        &::before {
          content: "";
          background-image: url("../../assets/images/faq/accordion-arrow-plus.svg") !important;
          background-size: 20px 20px;
          background-repeat: no-repeat;
          display: inline-block;
          width: 20px;
          height: 20px;
          position: absolute;
          inset-inline-end: 20px;
          top: 50%;
          transform: translateY(-50%);
          transition: transform 0.3s ease-in-out;
        }

        &:not(.collapsed)::before {
          content: "";
          background-image: url("../../assets/images/faq/accordion-arrow-cross.svg") !important;
          background-size: 20px 20px;
          background-repeat: no-repeat;
          display: inline-block;
          width: 20px;
          height: 20px;
          position: absolute;
          inset-inline-end: 20px;
          top: 50%;
          transform: translateY(-50%);
          transition: transform 0.3s ease-in-out;
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }

  &-2 {
    .accordion {
      .accordion-item {
        .accordion-button {
          background-color: transparent;
          border: 1px solid rgba(48, 48, 48, 0.16);
        }
      }
    }
  }
}