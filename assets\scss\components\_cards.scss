@use "../utils" as *;

/*----------------------------------------*/
/*  2.2 Cards
/*----------------------------------------*/
.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 10px;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  transition: all 0.3s ease-in-out;

  @media #{$xs} {
    padding: 16px 10px;
  }

  @media #{$sm} {
    padding: 20px 10px;
  }

  &:hover {
    background: #ffefea;
  }

  .icon {
    width: 66px;
    // height: 66px;
    flex-shrink: 0;
    margin-bottom: 24px;

    @media #{$md} {
      width: 55px;
      height: 55px;
    }

    @media #{$xs} {
      width: 45px;
      height: 45px;
      margin-bottom: 16px;
    }

    @media #{$sm} {
      width: 50px;
      height: 50px;
      margin-bottom: 20px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .text {
    h5 {
      color: var(--td-heading);
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 3px;
    }

    p {
      color: var(--td-secondary);
      text-align: center;
      font-size: 13px;
      font-weight: 600;
      line-height: normal;
    }
  }
}

.games-card {
  padding: 4px;
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;
  transition: all 0.3s ease-in-out;
  background: #fff;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -75%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      130deg,
      rgba(255, 255, 255, 0) 40%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0) 60%
    );
    transform: translateX(-100%) rotate(0deg);
    opacity: 0;
    transition:
      transform 1s ease,
      opacity 0.3s ease;
    pointer-events: none;
    z-index: 5;
  }

  .game-image {
    display: block;
    width: 100%;
    height: 180px;
    border-radius: 8px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      object-fit: cover;
      overflow: hidden;
      transition: transform 0.3s ease-in-out;
    }
  }

  .game-content {
    padding: 12px 16px;

    .category-and-trending {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      h6 {
        color: var(--td-white);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;
        transition: all 0.3s ease-in-out;
        background: var(--td-secondary);
        padding: 4px 8px;
        border-radius: 8px;

        &:hover {
          color: var(--td-white);
        }
      }

      .is-trending {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--td-heading);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;

        img {
          width: 11px;
          height: 11px;
        }
      }
    }

    .title-and-price {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30px;

      .title {
        display: block;
        width: 70%;

        h3 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: lh(22, 16);
          margin-bottom: 3px;
          transition: all 0.3s ease-in-out;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          display: -webkit-box;

          &:hover {
            color: var(--td-secondary);
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 13px;
          font-weight: 600;
          line-height: lh(22, 13);
        }
      }

      .price {
        width: 30%;

        .has-discount {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 600;
          line-height: lh(20, 14);
          text-align: right;
          text-decoration: line-through;
        }

        h6 {
          color: var(--td-heading);
          font-size: 18px;
          font-weight: 600;
          line-height: lh(22, 18);
          text-align: right;

          @include rtl {
            text-align: left;
          }
        }
      }
    }

    .game-content-full {
      .title {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: lh(22, 16);
        margin-bottom: 3px;
        transition: all 0.3s ease-in-out;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        display: -webkit-box;

        &:hover {
          color: var(--td-secondary);
        }
      }

      .author {
        color: var(--td-text-primary);
        font-size: 13px;
        font-weight: 400;
        line-height: lh(22, 13);
        margin-top: 3px;

        a {
          color: var(--td-text-primary);
          font-weight: 700;
          transition: all 0.3s ease-in-out;

          &:hover {
            color: var(--td-secondary);
          }
        }
      }

      .star {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-top: 8px;

        .star-icon {
          color: #ffc119;
          font-size: 18px;
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 600;
          line-height: lh(22, 13);
        }
      }

      .pricing {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        .left {
          display: flex;
          align-items: center;
          gap: 4px;

          .has-discount {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 600;
            line-height: lh(20, 14);
            text-decoration: line-through;
          }

          h6 {
            color: var(--td-secondary);
            font-size: 18px;
            font-weight: 600;
            line-height: lh(22, 18);
          }
        }

        .right {
          p {
            color: var(--td-text-primary);
            font-size: 13px;
            font-weight: 600;
            line-height: lh(22, 13);
          }
        }
      }
    }
  }

  .has-trending {
    position: absolute;
    top: -2px;
    inset-inline-start: -2px;
    z-index: 10;

    @include rtl {
      inset-inline-start: auto;
      inset-inline-end: 0px;
    }
  }

  &-2 {
    position: relative;

    .game-content {
      .game-content-full {
        .title {
          font-size: 20px;
          font-weight: 600;
        }

        .author {
          font-size: 15px;
        }

        .pricing {
          .left {
            display: flex;
            align-items: center;
            gap: 4px;

            .has-discount {
              font-size: 16px;
            }

            h6 {
              font-size: 20px;
            }
          }

          .right {
            p {
              color: var(--td-text-primary);
              font-size: 13px;
              font-weight: 600;
              line-height: lh(22, 13);
            }
          }
        }
      }
    }

    .game-image {
      height: 220px;
    }

    .delete-button {
      position: absolute;
      top: 10px;
      inset-inline-end: 10px;
      z-index: 10;

      .delete-wishlist {
        display: flex;
        width: 40px;
        height: 40px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 9px;
        background: #fff;

        .delete-icon {
          font-size: 20px;
          color: var(--td-secondary);
        }
      }
    }
  }

  &:hover {
    border: 1px solid rgba(48, 48, 48, 0.16);
    box-shadow: 4px 4px 40px 0px rgba(62, 54, 131, 0.1);

    &::before {
      transform: translateX(100%) rotate(0deg);
      opacity: 1;
    }

    .game-image {
      img {
        transform: scale(1.1);
      }
    }
  }
}

.seller-card {
  border-radius: 10px;
  background: #fff;
  padding: 25px 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
  border: 1px solid #d5d5d5;

  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -75%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      130deg,
      rgba(255, 255, 255, 0) 40%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0) 60%
    );
    transform: translateX(-100%);
    opacity: 0;
    transition:
      transform 0.7s ease,
      opacity 0.3s ease;
    pointer-events: none;
    z-index: 3;
  }

  .has-popular {
    position: absolute;
    top: 8px;
    inset-inline-end: 8px;
    z-index: 10;

    img {
      width: 35px;
      height: 35px;
    }
  }

  .seller-image {
    width: 70px;
    height: 70px;
    overflow: hidden;
    border-radius: 8px;
    border-radius: 50%;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      overflow: hidden;
    }
  }

  .seller-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    .top {
      margin-top: 16px;
      display: flex;
      flex-direction: column;
      align-items: center;

      h5 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: lh(20, 16);
        margin-bottom: 4px;
        text-align: center;
      }

      p {
        color: var(--td-text-primary);
        font-size: 14px;
        font-weight: 500;
        line-height: lh(20, 14);
        text-align: center;

        span {
          color: var(--td-secondary);
        }
      }

      .conutry {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-top: 10px;

        .country-image {
          img {
            width: 22px;
            height: 15px;
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(20, 14);
        }
      }
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-top: 12px;

      .left {
        margin-right: 5px;
        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(16, 14);
        }
      }

      .right {
        .success-rate {
          display: flex;
          width: 45px;
          height: 20px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 24px;
          background: #198754;
          color: #fff;
          font-size: 12px;
          font-weight: 600;
          line-height: normal;

          &.success {
            color: #198754;
            background: rgba(25, 135, 84, 0.16);
          }

          &.error {
            background: rgba(220, 53, 69, 0.16);
            color: #dc3545;
          }

          &.warning {
            color: #ff8d29;
            background: rgba(255, 141, 41, 0.16);
          }

          &-2 {
            width: fit-content;
            padding: 4px 8px;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 20px;
      width: 100%;

      .primary-button {
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 600;
        line-height: normal;
        border-radius: 8px;
        height: 40px;
        background-color: rgba(var(--td-secondary-rgb), 0.1);
        color: var(--td-secondary);
        transition: all 0.3s ease-in-out;

        &:hover {
          background-color: var(--td-secondary);
          color: var(--td-white);
        }
      }

      .fav-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        border-radius: 8px;
        border: 1px solid var(--td-border-1);
        background: var(--td-white);
        color: var(--td-heading);
        font-size: 14px;
        line-height: normal;
        transition: all 0.3s ease-in-out;

        .fav-icon {
          font-size: 18px;
        }

        &:hover,
        &.active {
          background: var(--td-secondary);
          color: var(--td-white);
        }
      }
    }
  }

  &-2 {
    border: 1px solid rgba(48, 48, 48, 0.16);
  }

  &-3 {
    .seller-image {
      height: 210px;

      @media #{$xs} {
        width: 100%;
        height: 200px;
      }

      @media #{$sm} {
        width: 100%;
        height: 170px;
      }
    }
  }

  &:hover {
    .seller-image {
      img {
        transform: scale(1.1);
      }
    }
  }

  &:hover::before {
    transform: translateX(100%);
    opacity: 1;
  }
}

.blog-card {
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .full-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .blog-image {
      width: 100%;
      height: 180px;
      overflow: hidden;
      border-radius: 8px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: cover;
        overflow: hidden;
      }

      &:hover {
        img {
          transform: scale(1.1);
        }
      }
    }

    .blog-content {
      border-top: none;
      flex: 1;
      padding: 10px 0;
      padding-top: 16px;
      position: relative;

      .category {
        display: block;
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        margin-bottom: 10px;
      }

      h3 {
        color: var(--td-heading);
        font-size: 20px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 10px;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        display: -webkit-box;

        @media #{$xs} {
          font-size: 18px;
        }
        @media #{$sm} {
          font-size: 20px;
        }

        &:hover {
          color: var(--td-secondary);
        }
      }

      .des {
        color: rgba(77, 77, 77, 0.8);
        font-size: 14px;
        font-weight: 400;
        line-height: lh(22, 16);

        @media #{$xs} {
          font-size: 14px;
        }
        @media #{$sm} {
          font-size: 14px;
        }
      }

      .date-text {
        margin-top: 16px;
        color: var(--td-heading);
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
      }
    }
  }
}

.blog-card:hover {
  .blog-content h3 {
    color: var(--td-secondary);
  }
}

.blog-horizontal-card {
  padding: 8px;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.16);

  .blog-img {
    display: block;
    width: 100%;
    height: 102px;

    @media #{$xs} {
      height: 202px;
    }

    @media #{$sm} {
      height: 102px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }
  }

  .blog-content {
    .date {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 10px;

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;

        .date-icon {
          font-size: 18px;
          color: var(--td-text-primary);
        }

        p {
          color: rgba(48, 48, 48, 0.8);
          font-size: rem(14);
          font-weight: 500;
          line-height: normal;
        }
      }
    }

    h3 {
      color: var(--td-heading);
      font-size: 18px;
      font-weight: 600;
      line-height: normal;
      transition: all 0.3s ease-in-out;

      &:hover {
        color: var(--td-secondary);
      }
    }
  }
}

.product-gallery {
  .main-slider {
    .slick-slide {
      width: 100%;
      height: 380px;
      border-radius: 10px;
      border: 1px solid rgba(48, 48, 48, 0.16);
      padding: 10px;

      @media #{$xs} {
        height: 280px;
      }

      @media #{$sm} {
        height: 380px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
      }
    }
  }

  .nav-slider {
    margin-top: 16px;

    .slick-slide {
      width: 100px;
      margin-inline-end: 10px;
      height: 68px;
      cursor: pointer;

      &.slick-current {
        border: 1px solid rgba(255, 98, 41, 0.6);
        border-radius: 6px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
    }
  }
}

.package-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  border-radius: 19px;
  border: 1px solid rgba(48, 48, 48, 0.16);

  @media #{$xs} {
    grid-template-columns: repeat(1, 1fr);
  }

  .package-card {
    border-inline-end: 1px solid rgba(48, 48, 48, 0.16);
    padding: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    @media #{$lg} {
      padding: 30px;
    }

    @media #{$md,$xs} {
      padding: 50px 20px;
    }

    @media #{$xs} {
      border-inline-end: none;
      border-bottom: 1px solid rgba(48, 48, 48, 0.16);
    }

    &:last-child {
      border-inline-end: none;

      @media #{$xs} {
        border-bottom: none;
      }
    }

    &.has-recommanded {
      background: rgba(255, 98, 41, 0.0901960784);
      position: relative;

      .has-recommnded-box {
        position: absolute;
        top: 10px;
        right: 10px;
        background: var(--td-secondary);
        padding: 4px 8px;
        border-radius: 8px;

        p {
          font-size: 14px;
          font-weight: 500;
          line-height: normal;
          color: #fff;
        }
      }
    }

    .package-icon {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      margin-bottom: 20px;

      @media #{$md,$xs} {
        width: 45px;
        height: 45px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    h6 {
      color: var(--td-heading);
      text-align: center;
      font-size: 20px;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 30px;

      @media #{$md,$xs} {
        font-size: 18px;
        margin-bottom: 20px;
      }
    }

    .price {
      display: flex;
      align-items: end;
      gap: 5px;

      h2 {
        color: var(--td-heading);
        text-align: center;
        font-size: 40px;
        font-weight: 700;
        line-height: normal;

        @media #{$md,$xs} {
          font-size: 26px;
        }

        sup {
          color: var(--td-text-primary);
          text-align: center;
          font-size: 18px;
          font-weight: 600;
          line-height: normal;
          top: -1.2em;

          @media #{$md,$xs} {
            top: -0.5em;
          }
        }
      }

      p {
        color: rgba(48, 48, 48, 0.5);
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        transform: translateY(-10px);
      }
    }

    ul {
      margin-top: 50px;

      @media #{$md,$xs} {
        margin-top: 30px;
      }

      li {
        display: flex;
        align-items: start;
        gap: 10px;
        margin-bottom: 19px;

        span {
          display: inline-flex;

          .double-check-mark {
            font-size: 20px;
            color: var(--td-secondary);

            @media #{$md,$xs} {
              font-size: 16px;
            }
          }
        }
      }
    }

    .action-btn {
      margin-top: 40px;

      @media #{$md,$xs} {
        margin-top: 30px;
      }
    }
  }
}

.info-card {
  padding: 45px 30px;
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;

  &.one {
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 98, 41, 0.05) 50.71%,
      rgba(255, 255, 255, 0.2) 100%
    );
  }

  &.two {
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(26, 155, 122, 0.04) 50.71%,
      rgba(255, 255, 255, 0.2) 100%
    );
  }

  &.three {
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 169, 0, 0.04) 50.71%,
      rgba(255, 255, 255, 0.2) 100%
    );
  }

  .info-card-icon {
    display: flex;
    margin-bottom: 10px;

    .info-email-icon {
      font-size: 40px;
      color: var(--td-secondary);
    }

    .info-phone-icon {
      font-size: 40px;
      color: #1a9b7a;
    }

    .info-location-icon {
      font-size: 40px;
      color: #ffa900;
    }
  }

  p {
    color: var(--td-heading);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
  }
}
