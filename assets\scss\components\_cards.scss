@use '../utils' as *;

/*----------------------------------------*/
/*  Cards
/*----------------------------------------*/
.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 10px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  transition: all 0.3s ease-in-out;

  @media #{$xs} {
    padding: 16px 10px;
  }

  @media #{$sm} {
    padding: 20px 10px;
  }

  &:hover {
    background: #FFEFEA;
  }

  .icon {
    width: 66px;
    height: 66px;
    flex-shrink: 0;
    margin-bottom: 24px;

    @media #{$md} {
      width: 55px;
      height: 55px;
    }

    @media #{$xs} {
      width: 45px;
      height: 45px;
      margin-bottom: 16px;
    }

    @media #{$sm} {
      width: 50px;
      height: 50px;
      margin-bottom: 20px;
    }

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .text {
    h5 {
      color: var(--td-heading);
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 3px;
    }

    p {
      color: var(--td-secondary);
      text-align: center;
      font-size: 13px;
      font-weight: 600;
      line-height: normal;
    }
  }
}

.games-card {
  padding: 4px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;
  transition: all 0.3s ease-in-out;
  background: #FFF;

  &:hover {
    border: 1px solid rgba(48, 48, 48, 0.16);
    box-shadow: 4px 4px 40px 0px rgba(62, 54, 131, 0.10);
  }

  .game-image {
    display: block;
    width: 100%;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      border-radius: 12px;
      object-fit: cover;
      overflow: hidden;
    }
  }

  .game-content {
    padding: 12px;

    .category-and-trending {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      h6 {
        color: var(--td-heading);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;
      }

      .is-trending {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--td-heading);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;

        img {
          width: 11px;
          height: 11px;
        }
      }
    }

    .title-and-price {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30px;

      .title {
        display: block;

        h3 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: lh(22, 16);
          margin-bottom: 3px;
          transition: all 0.3s ease-in-out;

          &:hover {
            color: var(--td-secondary);
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 13px;
          font-weight: 600;
          line-height: lh(22, 13);
        }
      }

      .price {
        width: 30%;

        h6 {
          color: var(--td-heading);
          font-size: 18px;
          font-weight: 600;
          line-height: lh(22, 18);
          text-align: right;
        }
      }

    }
  }

  &-2 {
    position: relative;

    .game-image {
      height: 220px;
    }

    .delete-button {
      position: absolute;
      top: 10px;
      inset-inline-end: 10px;
      z-index: 10;

      .delete-wishlist {
        display: flex;
        width: 40px;
        height: 40px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 9px;
        background: #FFF;

        .delete-icon {
          font-size: 20px;
          color: var(--td-secondary);
        }
      }
    }
  }
}

.seller-card {
  border-radius: 14px;
  background: #FFF;
  padding: 10px;

  .seller-image {
    width: 100%;
    height: 123px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      object-fit: cover;
    }
  }

  .seller-content {
    .top {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-top: 16px;

      .left {
        h5 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: lh(20, 16);
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(20, 14);
        }
      }

      .right {
        p {
          color: var(--td-secondary);
          font-size: 14px;
          font-weight: 500;
          line-height: (20, 14);
        }
      }
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-top: 10px;

      .left {
        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(16, 14);
        }
      }

      .right {
        .success-rate {
          display: flex;
          width: 45px;
          height: 20px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 24px;
          background: #198754;
          color: #FFF;
          font-size: 12px;
          font-weight: 600;
          line-height: normal;

          &.success {
            background: #198754;
          }

          &.error {
            background: #DC3545;
          }

          &.warning {
            background: #FF8D29;
          }
        }
      }
    }
  }

  &-2 {
    border: 1px solid rgba(48, 48, 48, 0.16);

    .seller-image {
      @media #{$xs} {
        width: 100%;
        height: 200px;
      }

      @media #{$sm} {
        width: 100%;
        height: 123px;
      }
    }
  }

  &-3 {
    .seller-image {
      height: 210px;

      @media #{$xs} {
        width: 100%;
        height: 200px;
      }

      @media #{$sm} {
        width: 100%;
        height: 170px;
      }
    }
  }
}

.blog-card {
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .full-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .blog-image {
      width: 100%;
      height: 180px;
      overflow: hidden;
      border-radius: 16px 16px 0 0;

      img {
        width: 100%;
        height: 100%;
        border-radius: 16px 16px 0 0;
        object-fit: cover;
        overflow: hidden;
      }

      &:hover {
        img {
          transform: scale(1.1);
        }
      }
    }

    .blog-content {
      border: 1px solid var(--td-border-1);
      border-top: none;
      padding: 16px;
      border-radius: 0 0 16px 16px;
      flex: 1;

      .date {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 10px;

        .icon {
          .date-icon {
            font-size: 18px;
            color: var(--td-text-primary);
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: normal;
        }
      }

      h3 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 10px;

        &:hover {
          color: var(--td-secondary);
        }
      }

      p {
        color: var(--td-text-primary);
        font-size: 14px;
        font-weight: 400;
        line-height: lh(22, 14);
      }

      .action-btn {
        margin-top: 20px;
      }
    }
  }
}

.blog-horizontal-card {
  padding: 8px;
  border-radius: 14px;
  border: 1px solid rgba(48, 48, 48, 0.16);

  .blog-img {
    display: block;
    width: 100%;
    height: 102px;

    @media #{$xs} {
      height: 202px;
    }

    @media #{$sm} {
      height: 102px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }
  }

  .blog-content {
    .date {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 10px;

      .icon {
        .date-icon {
          font-size: 18px;
          color: var(--td-text-primary);
        }

        p {
          color: rgba(48, 48, 48, 0.80);
          font-size: rem(14);
          font-weight: 500;
          line-height: normal;
        }
      }
    }

    h3 {
      color: var(--td-heading);
      font-size: 18px;
      font-weight: 600;
      line-height: normal;
      transition: all 0.3s ease-in-out;

      &:hover {
        color: var(--td-secondary);
      }
    }
  }
}

.product-gallery {
  .main-slider {
    .slick-slide {
      width: 100%;
      height: 380px;
      border-radius: 16px;
      border: 1px solid rgba(48, 48, 48, 0.16);
      padding: 10px;

      @media #{$xs} {
        height: 280px;
      }

      @media #{$sm} {
        height: 380px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      }
    }
  }

  .nav-slider {
    margin-top: 16px;

    .slick-slide {
      width: 100px;
      margin-right: 10px;
      height: 68px;
      cursor: pointer;

      &.slick-current {
        border: 1px solid rgba(255, 98, 41, 0.60);
        border-radius: 6px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
    }
  }

}

.package-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  border-radius: 19px;
  border: 1px solid rgba(48, 48, 48, 0.16);

  @media #{$xs} {
    grid-template-columns: repeat(1, 1fr);
  }

  .package-card {
    border-right: 1px solid rgba(48, 48, 48, 0.16);
    padding: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    @media #{$lg} {
      padding: 30px;
    }

    @media #{$md,$xs} {
      padding: 20px;
    }

    @media #{$xs} {
      border-right: none;
      border-bottom: 1px solid rgba(48, 48, 48, 0.16);
    }

    &:last-child {
      border-right: none;

      @media #{$xs} {
        border-bottom: none
      }
    }

    .package-icon {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      margin-bottom: 20px;

      @media #{$md,$xs} {
        width: 45px;
        height: 45px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    h6 {
      color: var(--td-heading);
      text-align: center;
      font-size: 20px;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 30px;

      @media #{$md,$xs} {
        font-size: 18px;
        margin-bottom: 20px;
      }
    }

    .price {
      h2 {
        color: var(--td-heading);
        text-align: center;
        font-size: 40px;
        font-weight: 700;
        line-height: normal;

        @media #{$md,$xs} {
          font-size: 26px;
        }

        sup {
          color: var(--td-text-primary);
          text-align: center;
          font-size: 18px;
          font-weight: 600;
          line-height: normal;
          top: -1.2em;

          @media #{$md,$xs} {
            top: -.5em;
          }
        }
      }

      p {
        color: rgba(48, 48, 48, 0.5);
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
      }
    }

    ul {
      margin-top: 50px;

      @media #{$md,$xs} {
        margin-top: 30px;
      }

      li {
        display: flex;
        align-items: start;
        gap: 10px;
        margin-bottom: 19px;

        span {
          display: inline-flex;

          .double-check-mark {
            font-size: 20px;
            color: var(--td-secondary);

            @media #{$md,$xs} {
              font-size: 16px;
            }
          }
        }
      }
    }

    .action-btn {
      margin-top: 40px;

      @media #{$md,$xs} {
        margin-top: 30px;
      }
    }
  }
}

.info-card {
  padding: 45px 30px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;

  &.one {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 98, 41, 0.05) 50.71%, rgba(255, 255, 255, 0.20) 100%);
  }

  &.two {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(26, 155, 122, 0.04) 50.71%, rgba(255, 255, 255, 0.20) 100%);
  }

  &.three {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 169, 0, 0.04) 50.71%, rgba(255, 255, 255, 0.20) 100%);
  }

  .info-card-icon {
    display: flex;
    margin-bottom: 10px;

    .info-email-icon {
      font-size: 40px;
      color: var(--td-secondary);
    }

    .info-phone-icon {
      font-size: 40px;
      color: #1A9B7A;
    }

    .info-location-icon {
      font-size: 40px;
      color: #FFA900;
    }
  }

  p {
    color: var(--td-heading);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
  }

}