@use '../utils' as *;

/*----------------------------------------*/
/*  Cards
/*----------------------------------------*/
.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 10px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  transition: all 0.3s ease-in-out;

  @media #{$xs} {
    padding: 16px 10px;
  }

  @media #{$sm} {
    padding: 20px 10px;
  }

  &:hover {
    background: #FFEFEA;
  }

  .icon {
    width: 66px;
    height: 66px;
    flex-shrink: 0;
    margin-bottom: 24px;

    @media #{$md} {
      width: 55px;
      height: 55px;
    }

    @media #{$xs} {
      width: 45px;
      height: 45px;
      margin-bottom: 16px;
    }

    @media #{$sm} {
      width: 50px;
      height: 50px;
      margin-bottom: 20px;
    }

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .text {
    h5 {
      color: var(--td-heading);
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-bottom: 3px;
    }

    p {
      color: var(--td-secondary);
      text-align: center;
      font-size: 13px;
      font-weight: 600;
      line-height: normal;
    }
  }
}

.games-card {
  padding: 4px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;
  transition: all 0.3s ease-in-out;

  &:hover {
    border: 1px solid rgba(48, 48, 48, 0.16);
    box-shadow: 4px 4px 40px 0px rgba(62, 54, 131, 0.10);
  }

  .game-image {
    display: block;
    width: 100%;
    height: 180px;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;

    img {
      width: 100%;
      height: 100%;
      border-radius: 12px;
      object-fit: cover;
      overflow: hidden;
    }

    &:hover {
      img {
        transform: scale(1.1);
      }
    }
  }

  .game-content {
    padding: 12px;

    .category-and-trending {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      h6 {
        color: var(--td-heading);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;
      }

      .is-trending {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--td-heading);
        font-size: 13px;
        font-weight: 500;
        line-height: normal;

        img {
          width: 11px;
          height: 11px;
        }
      }
    }

    .title-and-price {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30px;

      .title {
        display: block;

        h3 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: lh(22, 16);
          margin-bottom: 3px;
          transition: all 0.3s ease-in-out;

          &:hover {
            color: var(--td-secondary);
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 13px;
          font-weight: 600;
          line-height: lh(22, 13);
        }
      }

      .price {
        width: 30%;

        h6 {
          color: var(--td-heading);
          font-size: 18px;
          font-weight: 600;
          line-height: lh(22, 18);
          text-align: right;
        }
      }

    }
  }
}

.seller-card {
  border-radius: 14px;
  background: #FFF;
  padding: 10px;

  .seller-image {
    width: 100%;
    height: 123px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      object-fit: cover;
    }
  }

  .seller-content {
    .top {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-top: 16px;

      .left {
        h5 {
          color: var(--td-heading);
          font-size: 16px;
          font-weight: 600;
          line-height: lh(20, 16);
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(20, 14);
        }
      }

      .right {
        p {
          color: var(--td-secondary);
          font-size: 14px;
          font-weight: 500;
          line-height: (20, 14);
        }
      }
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: start;
      margin-top: 10px;

      .left {
        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: lh(16, 14);
        }
      }

      .right {
        .success-rate {
          display: flex;
          width: 45px;
          height: 20px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 24px;
          background: #198754;
          color: #FFF;
          font-size: 12px;
          font-weight: 600;
          line-height: normal;

          &.success {
            background: #198754;
          }

          &.error {
            background: #DC3545;
          }

          &.warning {
            background: #FF8D29;
          }
        }
      }
    }
  }
}

.blog-card {
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .full-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .blog-image {
      width: 100%;
      height: 180px;
      overflow: hidden;
      border-radius: 16px 16px 0 0;

      img {
        width: 100%;
        height: 100%;
        border-radius: 16px 16px 0 0;
        object-fit: cover;
        overflow: hidden;
      }

      &:hover {
        img {
          transform: scale(1.1);
        }
      }
    }

    .blog-content {
      border: 1px solid var(--td-border-1);
      border-top: none;
      padding: 16px;
      border-radius: 0 0 16px 16px;
      flex: 1;

      .date {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 10px;

        .icon {
          .date-icon {
            font-size: 18px;
            color: var(--td-text-primary);
          }
        }

        p {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 500;
          line-height: normal;
        }
      }

      h3 {
        color: var(--td-heading);
        font-size: 18px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 10px;

        &:hover {
          color: var(--td-secondary);
        }
      }

      p {
        color: var(--td-text-primary);
        font-size: 14px;
        font-weight: 400;
        line-height: lh(22, 14);
      }

      .action-btn {
        margin-top: 20px;
      }
    }
  }
}