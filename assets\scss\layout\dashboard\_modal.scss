@use '../../utils' as *;

/*----------------------------------------*/
/*  Modal
/*----------------------------------------*/
.common-modal-full {
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(8, 8, 8, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  overflow-y: auto;

  &.open {
    visibility: visible;
    opacity: 1;
  }

  .common-modal-box {
    max-width: 650px;
    width: 90%;
    background-color: #FFF;
    border-radius: 14px;
    padding: 30px;

    @media #{$xs} {
      padding: 16px;
    }

    .content {
      .add-new-withdrawal {
        h4 {
          color: var(--td-heading);
          font-size: 20px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 20px;
        }

        .add-forms {
          padding: 30px;
          border-radius: 20px;
          border: 1px solid rgba(48, 48, 48, 0.16);

          @media #{$xs} {
            padding: 16px;
          }

          .modal-action-btn {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-top: 20px;

            @media #{$xs} {
              gap: 10px;
            }

            .withdraw-close {
              display: inline-flex;
              width: 92px;
              height: 40px;
              padding: 12px 24px;
              justify-content: center;
              align-items: center;
              gap: 3px;
              flex-shrink: 0;
              border-radius: 12px;
              border: 1px solid rgba(48, 48, 48, 0.16);
              color: var(--td-heading);
              text-align: center;
              font-size: 16px;
              font-weight: 600;
              line-height: normal;
            }
          }
        }
      }
    }
  }
}