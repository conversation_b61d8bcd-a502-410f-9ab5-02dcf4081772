@use '../../utils' as *;

/*----------------------------------------*/
/*  checkout
/*----------------------------------------*/

.checkout-area-content {
  padding-top: 64px;

  @media #{$lg,$md,$xs} {
    padding-top: 40px;
  }

  .checkout-box {
    padding: 0 140px;

    @media #{$lg,$md,$xs} {
      padding: 0 0px;
    }

    .left {
      .payment-method-box {
        .header-box {
          border: 1px solid rgba(48, 48, 48, 0.16);
          background: #FFF;
          padding: 27px 24px;
          border-radius: 20px 20px 0 0;

          h4 {
            color: var(--td-heading);
            font-size: rem(20);
            font-weight: 600;
            line-height: normal;
            text-align: start;
          }
        }

        .method-item {
          border: 1px solid rgba(48, 48, 48, 0.16);
          border-top: 0;

          &:last-child{
            border-radius: 0 0 20px 20px;
          }

          .method-button {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 32px;
            // border: 1px solid #DADADA;
            background: 1px solid rgba(48, 48, 48, 0.16);
            cursor: pointer;

            @media #{$md,$xs} {
              padding: 20px 20px;
            }

            .form-check {
              padding-inline-start: 0;

              input[type=checkbox]~label::before,
              input[type=radio]~label::before {
                top: 3px;
              }

              input[type=checkbox]~label::after,
              input[type=radio]~label::after {
                top: 6px;
              }

              input {
                ~label {
                  &::before {
                    background-color: #2c375d;
                    border: 1px solid #2c375d;
                  }
                }

                &:checked {
                  ~label {
                    &::before {
                      border: 1px solid #2c375d;
                    }

                    &::after {
                      opacity: 1;
                      background-color: #2A59FE;
                      border-bottom: 0;
                    }
                  }
                }
              }
            }

            &.open {
              border-radius: 12px 12px 0px 0px;
              border: 1px solid rgba(255, 255, 255, 0.60);
              background: var(--td-card-bg-1);
              backdrop-filter: blur(5px);
            }
          }

          .method-content {
            padding: 20px 32px;
            border-inline-end: 1px solid var(--td-card-bg-2);
            border-inline-start: 1px solid var(--td-card-bg-2);
            background: var(--td-card-bg-1);
            backdrop-filter: blur(5px);
            height: 0;
            opacity: 0;
            overflow: hidden;
            padding: 0 32px;
            transition: height 0.5s ease, opacity 0.4s ease, padding 0.3s ease;

            &.open {
              opacity: 1;
              height: auto;
              overflow: visible;
              padding: 20px 32px;

              @media #{$md,$xs} {
                padding: 20px 20px;
              }
            }

            .all-payment-checkbox {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
            }
          }

          &:first-child .method-button {
            border-radius: 12px 12px 0px 0px;
            border-bottom: 0;

            &.open {
              border-bottom: 1px solid rgba(255, 255, 255, 0.60);
            }
          }

          &:last-child .method-button {
            border-radius: 0px 0px 12px 12px;

            &.open {
              border-radius: 0;
            }
          }

          &:not(:first-child):not(:last-child) .method-button {
            border-radius: 0;
            border-bottom: 0;

            &.open {
              border-bottom: 1px solid rgba(255, 255, 255, 0.60);
            }
          }

          &:last-child .method-content {
            border-bottom: 1px solid var(--td-card-bg-2);
            border-radius: 0 0 12px 12px;
          }
        }
      }
    }

    .right {
      padding-inline-start: 8px;

      @media #{$md,$xs} {
        padding-inline-start: 0px;
      }

      h4 {
        color: var(--td-heading);
        font-size: rem(24);
        font-weight: 600;
        line-height: lh(32, 24);
        margin-bottom: 30px;

        @media #{$md} {
          margin-bottom: 25px;
          font-size: rem(20);
        }

        @media #{$xs} {
          margin-bottom: 20px;
          font-size: rem(20);
        }
      }

      .checkout-card-box {
        padding: 32px;
        border-radius: 18px;
        border: 1px solid rgba(255, 255, 255, 0.30);
        background: rgba(255, 255, 255, 0.00);

        @media #{$xs} {
          padding: 20px;
        }

        @media #{$sm} {
          padding: 32px;
        }

        .checkout-card {
          display: flex;
          align-items: center;
          gap: 32px;

          @media #{$xs} {
            gap: 15px;
          }

          .img-box {
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
            border: 1px solid var(--td-card-bg-2);
            background: rgba(255, 255, 255, 0.04);
            backdrop-filter: blur(2px);
            border-radius: 12px;
            width: 80px;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;

            .img {
              width: 56px;
              height: 56px;
              border-radius: 12px;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
              }
            }
          }

          .text {
            h6 {
              color: var(--td-heading);
              font-size: rem(14);
              font-weight: 600;
              line-height: lh(20, 14);
              margin-bottom: 8px;
            }

            p {
              color: var(--td-text-primary);
              font-size: 14px;
              font-weight: 500;
              line-height: lh(20, 14);
            }
          }
        }

        .checkout-payment {
          margin-top: 32px;

          .payment-point {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
              margin-top: 0;
            }

            .left {
              p {
                text-align: left;
                color: rgba(255, 255, 255, 0.75);
                font-size: rem(12);
                font-weight: 500;
                line-height: lh(18, 12);
              }
            }

            .right {
              p {
                text-align: right;
                color: rgba(255, 255, 255, 0.75);
                font-size: rem(12);
                font-weight: 500;
                line-height: lh(18, 12);
              }
            }

            &-bold {
              .left {
                p {
                  color: var(--td-heading);
                  font-size: rem(16);
                  font-weight: 600;
                  line-height: lh(20, 16);
                }
              }

              .right {
                p {
                  color: var(--td-heading);
                  font-size: rem(16);
                  font-weight: 600;
                  line-height: lh(20, 16);
                }
              }
            }
          }

          .pay-now-btn {
            margin-top: 32px;

            a {
              text-transform: uppercase;
            }
          }
        }
      }
    }
  }
}

.transaction-success {
  padding-top: 44px;

  @media #{$lg,$md,$xs} {
    padding-top: 20px;
  }

  .success-box {
    display: flex;
    justify-content: center;
    align-items: center;

    .success-box-content {
      width: 778px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .img {
        width: 400px;
        height: 400px;

        @media #{$xs} {
          width: 300px;
          height: 300px;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .content {
        h3 {
          color: var(--td-heading);
          text-align: center;
          font-size: rem(24);
          font-weight: 600;
          line-height: lh(32, 24);
          margin-bottom: 24px;
        }

        p {
          color: var(--td-text-primary);
          text-align: center;
          font-size: rem(14);
          font-weight: 500;
          line-height: lh(22, 14);
          margin-bottom: 24px;
        }

        .go-btn {
          display: flex;
          justify-content: center;
          align-items: center;

          a {
            display: inline-flex;
          }
        }
      }
    }
  }
}


.payment-method-checkbox {
	position: relative;
	margin: 10px 10px 0 0;

	input[type="radio"] {
		display: none;
	}

	.check-box-image {
		width: 50px;
		height: 50px;
		background-color: var(--td-white);
		border-radius: 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		border: 2px solid transparent;
		cursor: pointer;

		.img {
			width: 40px;
			height: 40px;

			img {
				height: 100%;
				width: 100%;
				object-fit: contain;
			}
		}

		&::before {
			content: "✔";
			position: absolute;
			top: 3px;
			inset-inline-start: 3px;
			width: 15px;
			height: 15px;
			border-radius: 50%;
			background-color: green;
			color: white;
			font-size: 12px;
			font-weight: bold;
			display: none;
			z-index: 2;
			align-items: center;
			justify-content: center;
		}

		&::after {
			content: "";
			position: absolute;
			background: rgba(0, 255, 0, 0.1);
			top: 0;
			inset-inline-start: 0;
			width: 100%;
			height: 100%;
			border-radius: 5px;
			z-index: 1;
			display: none;
		}
	}

	/* Show checkmark only when input is checked */
	input[type="radio"]:checked+.check-box-image {
		border-color: green;

		&::before {
			display: flex;
		}

		&::after {
			display: block;
		}
	}
}