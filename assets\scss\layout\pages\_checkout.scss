@use '../../utils' as *;

/*----------------------------------------*/
/*  checkout
/*----------------------------------------*/

.checkout-area-content {

  .checkout-box {
    padding: 0 140px;

    @media #{$lg,$md,$xs} {
      padding: 0 0px;
    }

    .left {
      .payment-method-box {
        .header-box {
          border: 1px solid rgba(48, 48, 48, 0.16);
          background: #FFF;
          padding: 27px 24px;
          border-radius: 20px 20px 0 0;

          @media #{$xs} {
            padding: 20px 14px;
          }

          h4 {
            color: var(--td-heading);
            font-size: rem(20);
            font-weight: 600;
            line-height: normal;
            text-align: start;

            @media #{$xs} {
              font-size: rem(18);
            }
          }
        }

        .method-item {
          border: 1px solid rgba(48, 48, 48, 0.16);
          border-top: 0;

          &:last-child {
            border-radius: 0 0 20px 20px;
          }

          .method-button {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 32px;
            // border: 1px solid #DADADA;
            background: 1px solid rgba(48, 48, 48, 0.16);
            cursor: pointer;

            @media #{$md,$xs} {
              padding: 20px 20px;
            }

            &.open {
              border-bottom: 1px solid rgba(48, 48, 48, 0.16) !important;
            }

            .form-check {
              padding-inline-start: 0;

              .form-check-input:checked {
                background-color: #FF6229;
                border-color: #FF6229;
              }

              @media #{$lg,$md,$xs} {
                .form-check-input {
                  float: left;
                  margin-left: -4px;
                  margin-right: 4px;
                }
              }

              input[type=checkbox]~label::before,
              input[type=radio]~label::before {
                top: 3px;
              }

              input[type=checkbox]~label::after,
              input[type=radio]~label::after {
                top: 6px;
              }

              input {
                ~label {
                  &::before {
                    background-color: #2c375d;
                    border: 1px solid #2c375d;
                  }
                }

                &:checked {
                  ~label {
                    &::before {
                      border: 1px solid #2c375d;
                    }

                    &::after {
                      opacity: 1;
                      background-color: #2A59FE;
                      border-bottom: 0;
                    }
                  }
                }
              }
            }

            &.open {
              border-radius: 12px 12px 0px 0px;
              border: 1px solid rgba(255, 255, 255, 0.60);
              background: var(--td-card-bg-1);
              backdrop-filter: blur(5px);
            }
          }

          .method-content {
            padding: 20px 32px;
            border-inline-end: 1px solid var(--td-card-bg-2);
            border-inline-start: 1px solid var(--td-card-bg-2);
            background: var(--td-card-bg-1);
            backdrop-filter: blur(5px);
            height: 0;
            opacity: 0;
            overflow: hidden;
            padding: 0 32px;
            transition: height 0.5s ease, opacity 0.4s ease, padding 0.3s ease;

            &:last-child {
              &.open {
                border-radius: 0 0 20px 20px;
              }
            }

            &.open {
              opacity: 1;
              height: auto;
              overflow: visible;
              padding: 20px 32px;

              @media #{$md,$xs} {
                padding: 20px 20px;
              }
            }

            .common-payment-form {

              input[type="text"],
              input[type="search"],
              input[type="email"],
              input[type="tel"],
              input[type="number"],
              input[type="password"],
              textarea {
                outline: none;
                height: 52px;
                width: 100%;
                padding: 0 15px;
                border-radius: 12px;
                border: 1px solid rgba(48, 48, 48, 0.16);
                color: var(--td-heading);
                color: rgba(48, 48, 48, 0.60);
                font-size: 14px;
                font-weight: 400;
                line-height: normal;

                &:focus {
                  border-color: var(--td-secondary) !important;
                  color: var(--td-heading) !important;
                  box-shadow: unset;
                }

                &::placeholder {
                  color: var(--td-text-secondary);
                }
              }
            }

            .all-payment-checkbox {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
            }
          }

          &:first-child .method-button {
            border-radius: 12px 12px 0px 0px;
            border-bottom: 0;

            &.open {
              border-bottom: 1px solid rgba(255, 255, 255, 0.60);
            }
          }

          &:last-child .method-button {
            border-radius: 0px 0px 12px 12px;

            &.open {
              border-radius: 0;
            }
          }

          &:not(:first-child):not(:last-child) .method-button {
            border-radius: 0;
            border-bottom: 0;

            &.open {
              border-bottom: 1px solid rgba(255, 255, 255, 0.60);
            }
          }

          &:last-child .method-content {
            border-bottom: 1px solid var(--td-card-bg-2);
            border-radius: 0 0 12px 12px;
          }
        }
      }
    }

    .right {
      padding-inline-start: 8px;

      @media #{$md,$xs} {
        padding-inline-start: 0px;
      }

      h4 {
        color: var(--td-heading);
        font-size: rem(24);
        font-weight: 600;
        line-height: lh(32, 24);
        margin-bottom: 30px;

        @media #{$md} {
          margin-bottom: 25px;
          font-size: rem(20);
        }

        @media #{$xs} {
          margin-bottom: 20px;
          font-size: rem(20);
        }
      }

      .checkout-card-box {
        .title {
          border: 1px solid rgba(48, 48, 48, 0.16);
          background: #FFF;
          padding: 27px 24px;
          border-radius: 20px 20px 0 0;

          @media #{$xs} {
            padding: 20px 14px;
          }

          h4 {
            color: var(--td-heading);
            font-size: rem(20);
            font-weight: 600;
            line-height: lh(28, 20);
            margin-bottom: 0px;

            @media #{$xs} {
              font-size: rem(18);
            }
          }
        }

        .full-box {
          border: 1px solid rgba(48, 48, 48, 0.16);
          border-top: 0;
          background: #FFF;
          padding: 20px;
          border-radius: 0 0 20px 20px;
        }

        .checkout-card {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-top: 20px;

          @media #{$xs} {
            gap: 15px;
          }

          .img-box {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;

            .img {
              width: 72px;
              height: 72px;
              border-radius: 5px;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;
              }
            }
          }

          .text {
            h6 {
              color: var(--td-heading);
              font-size: 16px;
              font-weight: 600;
              line-height: 22px;
            }

            span {
              color: var(--td-secondary);
              font-size: 12px;
              font-weight: 600;
              line-height: normal;
            }

            p {
              color: var(--td-heading);
              font-size: 16px;
              font-weight: 600;
              line-height: normal;
            }
          }
        }

        .checkout-payment {
          margin-top: 32px;
          padding: 24px;
          border-radius: 8px;
          background: #F1F1F1;

          .payment-point {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
              margin-top: 0;
            }

            &.total {
              margin-bottom: 0px !important;
            }

            .left {
              p {
                text-align: left;
                color: rgba(48, 48, 48, 0.80);
                font-size: 14px;
                font-weight: 500;
                line-height: 13px;
              }
            }

            .right {
              p {
                text-align: right;
                color: #303030;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 13px;
              }
            }

            &-bold {
              .left {
                p {
                  color: var(--td-heading);
                  font-size: rem(16);
                  font-weight: 600;
                  line-height: lh(20, 16);
                }
              }

              .right {
                p {
                  color: var(--td-heading);
                  font-size: rem(16);
                  font-weight: 600;
                  line-height: lh(20, 16);
                }
              }
            }
          }

          .payment-line {
            border-color: rgba(48, 48, 48, 0.16);
          }

          .pay-now-btn {
            margin-top: 32px;

            a {
              text-transform: uppercase;
            }
          }
        }

        .pay-now-btn {
          margin-top: 30px;
        }
      }
    }
  }
}

.transaction-success-area {
  .transaction-success-area-box {
    display: flex;
    justify-content: center;
    align-items: center;

    .content {
      max-width: 900px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .success-icon {
        width: 140px;
        height: 140px;
        margin-bottom: 40px;

        @media #{$xs} {
          width: 100px;
          height: 100px;
          margin-bottom: 20px;
        }
      }

      h5 {
        color: var(--td-heading);
        text-align: center;
        font-size: 30px;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 16px;

        @media #{$xs} {
          font-size: 20px;
        }
      }

      p {
        color: rgba(48, 48, 48, 0.80);
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        line-height: lh(26, 16);

        @media #{$xs} {
          font-size: 14px;
        }
      }

      .cta-btn {
        margin-top: 40px;

        @media #{$xs} {
          margin-top: 20px;
        }
      }
    }
  }
}


.payment-method-checkbox {
  position: relative;
  margin: 3px 10px 0 0;

  input[type="radio"] {
    display: none;
  }

  .check-box-image {
    width: 42px;
    height: 31px;
    background-color: var(--td-white);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;

    .img {
      width: 40px;
      height: 40px;

      img {
        height: 100%;
        width: 100%;
        object-fit: contain;
      }
    }

    &::before {
      content: "✔";
      position: absolute;
      top: 3px;
      left: 3px;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      background-color: #28a745;
      color: white;
      font-size: 10px;
      font-weight: bold;
      display: none;
      z-index: 2;
      align-items: center;
      justify-content: center;
    }

    &::after {
      content: "";
      position: absolute;
      background: rgba(40, 167, 69, 0.1);
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 5px;
      z-index: 1;
      display: none;
    }
  }

  /* Show checkmark only when input is checked */
  input[type="radio"]:checked+label.check-box-image {
    border-color: #28a745;

    &::before {
      display: flex;
    }

    &::after {
      display: block;
    }
  }
}