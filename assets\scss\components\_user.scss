@use "../utils" as *;

/*----------------------------------------*/
/*  2.8 User
/*----------------------------------------*/

.user-box {
    display: flex;
    align-items: center;
    position: relative;

    .user {
        display: inline-flex;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
        margin-inline-start: 8px;

        @media #{$lg,$md,$xs} {
            margin-inline-start: 0px;
        }

        // @media #{$lg,$md} {
        //     width: 30px;
        //     height: 30px;
        //     border-radius: 8px;
        //     margin-inline-start: 0px;
        // }

        // @media #{$xs} {
        //     width: 30px;
        //     height: 30px;
        //     border-radius: 8px;
        //     margin-inline-start: 0px;
        // }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.2);

            @media #{$lg,$md} {
                border-radius: 50%;
            }

            @media #{$xs} {
                border-radius: 50%;
            }
        }
    }

    &-mobile {
        .user {
            display: none;

            @media #{$lg,$md,$xs} {
                display: inline-flex;
            }
        }
    }

    .user-content {
        position: absolute;
        top: 44px;
        inset-inline-end: 0;
        width: 292px;
        border-radius: 14px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #fff;
        visibility: hidden;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        z-index: 5;

        @media #{$lg,$md} {
            top: 44px;
        }

        @media #{$xs} {
            top: 44px;
            width: 250px;
        }

        &.open {
            visibility: visible;
            opacity: 1;
            transform: translateY(0);
        }

        .user-box-content {
            margin-inline-start: 16px;
            margin-inline-end: 16px;
            margin-top: 16px;

            .user-balance {
                padding: 16px;
                background: #2a59fe;
                border-radius: 16px;
                margin-top: 10px;
                color: #fff;

                h6 {
                    font-size: 14px;
                    color: #fff;
                    margin-bottom: 10px;
                }

                .topup-btn {
                    background-color: #fff;
                    color: #080808;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: 500;
                }
            }

            h5 {
                color: var(--td-heading);
                font-size: 18px;
                font-weight: 600;
                line-height: normal;
            }

            a {
                color: rgba(48, 48, 48, 0.8);
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
                transition: all 0.3s ease-in-out;
                display: block;
                margin-top: 4px;

                &:hover {
                    color: var(--td-secondary);
                    text-decoration: underline;
                }
            }
        }

        .topup {
            padding: 14px;
            border-radius: 12px;
            background: #f1f1f1;
            margin: 16px;

            h5 {
                color: var(--td-heading);
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
                margin-bottom: 10px;
            }

            a {
                display: inline-flex;
                height: 26px;
                padding: 0px 8px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border-radius: 8px;
                background: var(--td-secondary);
                color: var(--td-white);
                font-size: 13px;
                font-weight: 600;
                line-height: normal;
            }
        }

        .content {
            padding-top: 10px;
            margin: 16px;
            border-top: 1px solid rgba(48, 48, 48, 0.16);

            ul {
                list-style-type: none;

                li {
                    a {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        padding: 12px 16px;
                        position: relative;

                        .icon {
                            display: inline-flex;
                            height: 18px;
                            width: 18px;

                            img {
                                height: 100%;
                                width: 100%;
                                object-fit: contain;
                            }
                        }

                        .text {
                            color: rgba(48, 48, 48, 0.8);
                            font-size: 14px;
                            font-weight: 600;
                            line-height: normal;
                        }

                        &:hover {
                            border-radius: 10px;
                            background: rgba(var(--td-secondary-rgb), 0.1);
                        }
                    }

                    button {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        padding: 16px 16px;
                        position: relative;
                        width: 100%;
                        border-radius: 8px;
                        background: rgba(255, 83, 83, 0.1);

                        .icon {
                            display: inline-flex;
                            height: 18px;
                            width: 18px;

                            img {
                                height: 100%;
                                width: 100%;
                                object-fit: contain;
                            }
                        }

                        .text {
                            color: #ff5353;
                            font-size: 14px;
                            font-weight: 600;
                            line-height: normal;
                        }

                        &:hover {
                            background: rgba(255, 98, 41, 0.2);
                        }
                    }
                }

                li:last-child {
                    a::after {
                        display: none;
                    }

                    button::after {
                        display: none;
                    }
                }
            }
        }
    }
}
