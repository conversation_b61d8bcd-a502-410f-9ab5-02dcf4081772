@use '../../utils' as *;

/*----------------------------------------*/
/*  All Games
/*----------------------------------------*/
.all-games-area {
  background: #F9F9F9;

  .all-games-filter {
    .filter-button-box {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .filter-button {
        display: inline-flex;
        padding: 10px 15px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 12px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        backdrop-filter: blur(8px);

        .tab-iocn{
          font-size: 18px;

          &.all-icon {
            color: #6456FE;
          }

          &.games-icon {
            color: #6456FE;
          }

          &.games-coin-icon {
            color: #FBAD26;
          }

          &.software-icon {
            color: #FB2D26;
          }

          &.payment-icon {
            color: #5B2C8E;
          }

          &.item-icon {
            color: #2674FB;
          }

          &.boosting-icon {
            color: #E830CF;
          }

          &.coaching-icon {
            color: #2674FB;
          }

          &.gift-icon {
            color: #19C8CF;
          }

          &.account-icon {
            color: #31B269;
          }

          &.telco-icon {
            color: #665773;
          }

          &.skins-icon {
            color: #31ADB2;
          }

          &.zalando-icon {
            color: #FF6229;
          }

          &.google-play-icon {
            color: #932FBA;
          }

          &.riotgames-icon {
            color: #FB2D26;
          }

          &.itunes-icon {
            color: #26B4FB;
          }

          &.airbnb-icon {
            color: #2674FB;
          }

          &.xbox-icon {
            color: #9C9538;
          }

          &.wallet-icon {
            color: #5B2C8E;
          }
        }

        span {
          color: var(--td-heading);
          font-size: 13px;
          font-weight: 700;
          line-height: 20px;
        }

        &:hover,
        &.active {
          border: 1px solid var(--td-secondary);
          background-color: var(--td-secondary);

          span {
            color: var(--td-white);
          }

          .tab-iocn {
            color: var(--td-white);
          }
        }
      }
    }
  }
}
