<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Support Ticket || Easy, Secure and Best Way to Earn Money From Your Own Place</title>
  <meta name="description" content="">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Place favicon.ico in the root directory -->
  <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
  <!-- CSS here -->
  <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
  <link rel="stylesheet" href="../assets/css/all.min.css">
  <link rel="stylesheet" href="../assets/css/flag-icon.css">
  <link rel="stylesheet" href="../assets/css/swiper.min.css">
  <link rel="stylesheet" href="../assets/css/magnific-popup.css">
  <link rel="stylesheet" href="../assets/css/nice-select.css">
  <link rel="stylesheet" href="../assets/css/select2.min.css">
  <link rel="stylesheet" href="../assets/css/flatpickr.min.css">
  <link rel="stylesheet" href="../assets/css/apexcharts.css">
  <link rel="stylesheet" href="../assets/css/styles.css">
  <style>
    .flatpickr-day.selected,
    .flatpickr-day.startRange,
    .flatpickr-day.endRange,
    .flatpickr-day.selected.inRange,
    .flatpickr-day.startRange.inRange,
    .flatpickr-day.endRange.inRange,
    .flatpickr-day.selected:focus,
    .flatpickr-day.startRange:focus,
    .flatpickr-day.endRange:focus,
    .flatpickr-day.selected:hover,
    .flatpickr-day.startRange:hover,
    .flatpickr-day.endRange:hover,
    .flatpickr-day.selected.prevMonthDay,
    .flatpickr-day.startRange.prevMonthDay,
    .flatpickr-day.endRange.prevMonthDay,
    .flatpickr-day.selected.nextMonthDay,
    .flatpickr-day.startRange.nextMonthDay,
    .flatpickr-day.endRange.nextMonthDay {
      background: #FF6229;
      -webkit-box-shadow: none;
      box-shadow: none;
      color: #fff;
      border-color: #FF6229;
    }

    .select2-selection__rendered {
      color: rgba(48, 48, 48, 0.80) !important;
    }
  </style>
</head>

<body>

  <!-- Body main wrapper start -->
  <main>
    <div class="no-data-found mt-3">
      <div class="img-box">
        <img src="../assets/images/icon/no-data-img.png" alt="No Data Found">
      </div>
      <span class="text">No Transaction Found!</span>
    </div>
  </main>
  <!-- Body main wrapper end -->

  <!-- JS here -->
  <script src="../assets/js/jquery-3.7.1.min.js"></script>
  <script src="../assets/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/js/jquery.nice-select.min.js"></script>
  <script src="../assets/js/magnific-popup.min.js"></script>
  <script src="../assets/js/swiper.min.js"></script>
  <script src="../assets/js/jarallax.min.js"></script>
  <script src="../assets/js/iconify.min.js"></script>
  <script src="../assets/js/moment.min.js"></script>
  <script src="../assets/js/select2.js"></script>
  <script src="../assets/js/flatpickr.js"></script>
  <script src="../assets/js/cookie.js"></script>
  <script src="../assets/js/dashboard-script.js"></script>
  <script src="../assets/js/apexcharts.min.js"></script>
  <script src="../assets/js/flatpickr.js"></script>
  <script src="../assets/js/flatpicker-activation.js"></script>
  <script src="../assets/js/main.js"></script>

  <script>
    $(document).ready(function () {
      $('.attach-file').on('click', function (e) {
        e.preventDefault();
        $('#fileInput').click();
      });

      $('.upload-thumb').on('click', function (e) {
        if ($(e.target).is('.upload-thumb') ||
          $(e.target).is('.upload-thumb-content') ||
          $(e.target).is('.upload-thumb-content *:not(.attach-file)')) {
          $('#fileInput').click();
        }
      });

      // Handle file selection
      $('#fileInput').on('change', function (e) {
        const files = e.target.files;
        if (files.length > 0) {
          processFiles(files);
        }
      });

      // Drag and drop functionality
      $('.upload-thumb').on('dragover', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).css('border-color', 'var(--td-secondary)');
      });

      $('.upload-thumb').on('dragleave', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).css('border-color', 'rgba(48, 48, 48, 0.30)');
      });

      $('.upload-thumb').on('drop', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).css('border-color', 'rgba(48, 48, 48, 0.30)');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
          $('#fileInput')[0].files = files;
          $('#fileInput').trigger('change');
        }
      });

      // Function to process uploaded files
      function processFiles(files) {
        const $uploadThumbImg = $('.upload-thumb-img');
        const $uploadThumbContent = $('.upload-thumb-content');

        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const imageBox = $('<div class="image-box"></div>');

          // Create delete button
          const deleteBtn = $('<span class="delete-btn">×</span>')
            .css({
              'position': 'absolute',
              'top': '5px',
              'right': '5px',
              'width': '20px',
              'height': '20px',
              'background': 'rgba(0,0,0,0.5)',
              'color': 'white',
              'border-radius': '50%',
              'display': 'flex',
              'align-items': 'center',
              'justify-content': 'center',
              'cursor': 'pointer',
              'font-size': '14px',
              'z-index': '10'
            })
            .on('click', function (e) {
              e.stopPropagation();
              $(this).parent().remove();
              checkEmptyState();
            });

          imageBox.append(deleteBtn);

          if (file.type.match('image.*')) {
            const reader = new FileReader();
            reader.onload = function (e) {
              const img = $('<img>').attr('src', e.target.result);
              imageBox.append(img);
              $uploadThumbImg.append(imageBox);
              updateUploadState();
            };
            reader.readAsDataURL(file);
          } else {
            const fileInfo = $('<div></div>')
              .text(file.name)
              .css({
                'width': '100%',
                'height': '100%',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'padding': '10px',
                'word-break': 'break-all'
              });
            imageBox.append(fileInfo);
            $uploadThumbImg.append(imageBox);
            updateUploadState();
          }
        }
      }

      function updateUploadState() {
        const $uploadThumbImg = $('.upload-thumb-img');
        const $uploadThumbContent = $('.upload-thumb-content');
        $uploadThumbImg.addClass('has-img');
        $uploadThumbContent.addClass('has-img');
      }

      function checkEmptyState() {
        const $uploadThumbImg = $('.upload-thumb-img');
        const $uploadThumbContent = $('.upload-thumb-content');

        if ($uploadThumbImg.children().length === 0) {
          $uploadThumbImg.removeClass('has-img');
          $uploadThumbContent.removeClass('has-img');
          $('#fileInput').val('');
        }
      }
    });
  </script>
</body>

</html>