@use "../../utils" as *;

/*----------------------------------------*/
/*  3.4.3 Dashbaord Card
/*----------------------------------------*/
.seller-overview-card {
  border-radius: 10px;
  padding: 24px 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--td-border-2);

  .icon {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;
    border-radius: 8px;
    background: #783efd;
    display: flex;
    justify-content: center;
    align-items: center;

    .seller-card-icon {
      font-size: 20px;
      color: var(--td-white);
    }
  }

  .text {
    position: relative;
    z-index: 1;

    p {
      color: var(--td-heading);
      font-size: 15px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      margin-bottom: 8px;
    }

    h3 {
      color: var(--td-secondary);
      font-size: 26px;
      font-weight: 600;
      line-height: normal;
    }
  }

  .bg-element {
    position: absolute;
    top: 0;
    inset-inline-end: 0;

    @include rtl {
      transform: rotate(180deg);
    }
  }

  &.active {
    background-color: rgba(var(--td-secondary-rgb), 0.1);
  }

  &-2 {
    .seller-inner {
      display: flex;
      align-items: start;
      gap: 20px;

      .text {
        h3 {
          margin-bottom: 8px;
        }
        p {
          margin-bottom: 0;
        }
      }
    }
  }
}

.seller-overview {
  .row {
    > div:nth-child(1) .seller-overview-card .icon {
      background-color: #783efd;
    }
    > div:nth-child(2) .seller-overview-card .icon {
      background-color: #31adb2;
    }
    > div:nth-child(3) .seller-overview-card .icon {
      background-color: #fbad26;
    }
    > div:nth-child(4) .seller-overview-card .icon {
      background-color: #31b269;
    }
    > div:nth-child(5) .seller-overview-card .icon {
      background-color: #2674fb;
    }
    > div:nth-child(6) .seller-overview-card .icon {
      background-color: #7052e8;
    }
    > div:nth-child(7) .seller-overview-card .icon {
      background-color: #ff6229;
    }
    > div:nth-child(8) .seller-overview-card .icon {
      background-color: #9c9538;
    }
    > div:nth-child(9) .seller-overview-card .icon {
      background-color: #2674fb;
    }
    > div:nth-child(10) .seller-overview-card .icon {
      background-color: #e830cf;
    }
    > div:nth-child(11) .seller-overview-card .icon {
      background-color: #1e708e;
    }
    > div:nth-child(12) .seller-overview-card .icon {
      background-color: #9c388e;
    }
  }
}

.activity {
  h4 {
    color: var(--td-heading);
    font-size: 20px;
    font-weight: 700;
    line-height: normal;
    margin-bottom: 20px;
  }
  .activity-chart {
    border-radius: 10px;
    background: var(--td-white);
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--td-border-2);
  }
}

.frontend-editor-data {
  p {
    color: var(--td-white);
  }
}

.package-overview {
  .package-overview-card {
    padding: 19px 20px;
    border-radius: 10px;
    background: #fff;
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--td-border-2);

    .content-box {
      .title-box {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 8px;

        h5 {
          color: var(--td-heading);
          font-size: 18px;
          font-weight: 600;
          line-height: normal;
        }
      }
      p {
        color: var(--td-secondary);
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
      }
    }
  }
}
