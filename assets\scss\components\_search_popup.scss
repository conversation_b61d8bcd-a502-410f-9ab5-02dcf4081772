@use '../utils' as *;

/*----------------------------------------*/
/*  2.11 search_popup
/*----------------------------------------*/
.mobile-search-popup {
    padding-top: 40px;
    padding-bottom: 40px;
    position: fixed;
    top: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    width: 100%;
    height: 100%;
    background-color: var(--td-white);
    z-index: 99;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-100%);
    transition: transform 0.6s ease, opacity 0.6s ease, visibility 0.6s ease;

    @media #{$xs} {
        padding-top: 28px;
    }

    @media #{$sm} {
        padding-top: 40px;
    }

    &.open {
        visibility: visible;
        opacity: 1;
        transform: translateY(0%);
    }

    .logo-and-close {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;

        .logo {
            img {
                height: 40px;

                @media #{$xs} {
                    height: 30px;
                }
            }
        }

        SVG * {
            fill: var(--td-heading);
        }
    }
}