@use "../../utils" as *;

/*----------------------------------------*/
/*  3.2.2 Hero
/*----------------------------------------*/
// .hero-area {
//   padding-top: 80px;
//   padding-bottom: 429px;
//   position: relative;

//   @media #{$xl} {
//     padding-top: 70px;
//     padding-bottom: 350px;
//   }

//   @media #{$lg} {
//     padding-top: 60px;
//     padding-bottom: 300px;
//   }

//   @media #{$md} {
//     padding-top: 50px;
//     padding-bottom: 215px;
//   }

//   @media #{$xs} {
//     padding-top: 30px;
//     padding-bottom: 100px;
//   }

//   @media #{$sm} {
//     padding-top: 40px;
//     padding-bottom: 160px;
//   }

//   .full-title-and-cta {
//     display: flex;
//     flex-direction: column;
//     gap: 20px;
//     align-items: center;
//     justify-content: center;

//     .hero-cta-button {
//       display: flex;
//       gap: 16px;
//       align-items: center;

//       @media #{$xs} {
//         flex-direction: column;
//         align-items: start;

//         a {
//           width: 100%;
//         }
//       }

//       @media #{$sm} {
//         flex-direction: row;
//         align-items: center;
//         a {
//           width: unset;
//         }
//       }
//     }
//   }

//   .hero-img {
//     position: absolute;
//     bottom: 0;
//     left: 50%;
//     transform: translateX(-50%);
//     width: 100%;
//     margin: 0 auto;
//     max-width: 1440px;
//     padding: 0 40px;

//     .hero-full-img {
//       width: 100%;
//       height: 329px;
//       display: flex;
//       justify-content: center;

//       @media #{$xl} {
//         height: 270px;
//       }

//       @media #{$lg} {
//         height: 220px;
//       }

//       @media #{$md} {
//         height: 170px;
//       }

//       @media #{$xs} {
//         height: 60px;
//       }

//       @media #{$sm} {
//         height: 125px;
//       }

//       img {
//         width: 100%;
//         height: 100%;
//         object-fit: unset;
//       }
//     }
//   }
// }

.hero-area {
  // background: var(--td-primary);
  padding: 80px 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;

  @media #{$xl} {
    padding: 70px 0;
  }
  @media #{$lg} {
    padding: 60px 0;
  }
  @media #{$md} {
    padding: 50px 0;
  }
  @media #{$xs} {
    padding: 40px 0;
  }

  .left {
    @media #{$md} {
      margin-bottom: 30px;
    }
    @media #{$xs} {
      margin-bottom: 16px;
    }
    .hero-text-container {
      h1 {
        color: var(--td-heading);
        font-size: 64px;
        font-weight: 700;
        line-height: lh(76, 64);
        margin-bottom: 20px;
        width: 94%;

        span {
          color: var(--td-secondary);
        }

        @media #{$xl} {
          font-size: 60px;
          width: 100%;
        }

        @media #{$lg} {
          font-size: 55px;
          width: 100%;
        }

        @media #{$md} {
          font-size: 50px;
          width: 100%;
        }

        @media #{$xs} {
          font-size: 32px;
          width: 100%;
        }

        @media #{$sm} {
          font-size: 40px;
          width: 100%;
        }
      }
      p {
        color: var(--td-text-primary);
        font-size: 20px;
        font-weight: 400;
        line-height: lh(32, 20);
        margin-top: 10px;
        width: 100%;

        @media #{$lg,$md} {
          font-size: 18px;
          width: 100%;
        }
        @media #{$xs} {
          font-size: 16px;
          width: 100%;
        }
      }

      .action-button {
        margin-top: 30px;
        @media #{$xs} {
          margin-top: 16px;
        }
        .primary-button {
          border-radius: 8px;
        }
      }

      .hero-stats {
        margin-top: 40px;
        display: flex;
        align-items: center;
        gap: 24px;

        @media #{$xs} {
          gap: 16px;
          margin-top: 20px;
          flex-direction: column;
          align-items: start;
        }

        @media #{$sm} {
          gap: 16px;
          margin-top: 20px;
          flex-direction: row;
          align-items: center;
        }

        .all-user-card {
          background: var(--td-white);
          border-radius: 8px;
          padding: 15px 25px;
          display: flex;
          align-items: center;
          gap: 14px;

          .left-content {
            img {
              width: 38px;
              height: 38px;
              object-fit: cover;
              border-radius: 50%;
              border: 2px solid var(--td-white);
              margin-left: -20px;

              &:first-child {
                margin-left: 0;
              }
            }
          }
          .right-content {
            h6 {
              color: var(--td-heading);
              font-size: 20px;
              font-weight: 800;
              line-height: normal;
            }
            p {
              color: var(--td-heading);
              font-size: 14px;
              font-weight: 400;
              line-height: normal;
              margin-top: 0px;
            }
          }
        }
        .positive-review {
          background: var(--td-white);
          border-radius: 8px;
          padding: 15px 25px;
          display: flex;
          align-items: center;
          gap: 14px;

          .left-content {
            img {
              width: 40px;
              height: 40px;
              flex-shrink: 0;
              object-fit: cover;
            }
          }

          .right-content {
            h6 {
              color: var(--td-heading);
              font-size: 20px;
              font-weight: 800;
              line-height: normal;
            }
            p {
              color: var(--td-heading);
              font-size: 14px;
              font-weight: 400;
              line-height: normal;
              margin-top: 0px;
            }
          }
        }
      }
    }
  }

  .right {
    padding-left: 55px;

    @media #{$md,$xs} {
      padding-left: 0px;
    }

    .hero-main-img {
      height: 750px;
      width: 100%;

      @media #{$xxl} {
        height: 733px;
      }
      @media #{$xl} {
        height: 620px;
      }
      @media #{$lg} {
        height: 506px;
      }
      @media #{$md,$xs} {
        height: 100%;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
