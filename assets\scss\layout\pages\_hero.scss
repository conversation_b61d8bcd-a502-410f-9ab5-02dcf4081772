@use '../../utils' as *;

/*----------------------------------------*/
/*  Hero
/*----------------------------------------*/
.hero-area {
  padding-top: 80px;
  padding-bottom: 429px;
  position: relative;

  @media #{$xl} {
    padding-top: 70px;
    padding-bottom: 350px;
  }

  @media #{$lg} {
    padding-top: 60px;
    padding-bottom: 300px;
  }

  @media #{$md} {
    padding-top: 50px;
    padding-bottom: 215px;
  }

  @media #{$xs} {
    padding-top: 30px;
    padding-bottom: 100px;
  }

  @media #{$sm} {
    padding-top: 40px;
    padding-bottom: 160px;
  }

  .full-title-and-cta {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    justify-content: center;

    .hero-cta-button {
      display: flex;
      gap: 16px;
      align-items: center;

      @media #{$xs} {
        flex-direction: column;
        align-items: start;
      }

      @media #{$sm} {
        flex-direction: row;
        align-items: center;
      }
    }
  }

  .hero-img {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    margin: 0 auto;
    max-width: 1440px;
    padding: 0 40px;

    .hero-full-img {
      width: 100%;
      height: 329px;
      display: flex;
      justify-content: center;

      @media #{$xl} {
        height: 270px;
      }

      @media #{$lg} {
        height: 220px;
      }

      @media #{$md} {
        height: 170px;
      }

      @media #{$xs} {
        height: 60px;
      }

      @media #{$sm} {
        height: 125px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: unset;
      }
    }
  }

}