@charset "UTF-8";
/*-----------------------------------------------------------------------------------

  Project Name:  Gamecon - Easy, Secure and Best Way to Earn Money From Your Own Place
  Author: Tdevs
  Support: Tdevs
  Description:  Easy, Secure and Best Way to Earn Money From Your Own Place 
  Version: 1.0

-----------------------------------------------------------------------------------


/*----------------------------------------*/
/*   1.1 Globals
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");
.mt-1 {
  margin-top: 1px;
}

.mb-1 {
  margin-bottom: 1px;
}

.ml-1 {
  margin-inset-inline-start: 1px;
}

.mr-1 {
  margin-inset-inline-end: 1px;
}

.mt-2 {
  margin-top: 2px;
}

.mb-2 {
  margin-bottom: 2px;
}

.ml-2 {
  margin-inset-inline-start: 2px;
}

.mr-2 {
  margin-inset-inline-end: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mb-3 {
  margin-bottom: 3px;
}

.ml-3 {
  margin-inset-inline-start: 3px;
}

.mr-3 {
  margin-inset-inline-end: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-inset-inline-start: 4px;
}

.mr-4 {
  margin-inset-inline-end: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-inset-inline-start: 5px;
}

.mr-5 {
  margin-inset-inline-end: 5px;
}

.mt-6 {
  margin-top: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-inset-inline-start: 6px;
}

.mr-6 {
  margin-inset-inline-end: 6px;
}

.mt-7 {
  margin-top: 7px;
}

.mb-7 {
  margin-bottom: 7px;
}

.ml-7 {
  margin-inset-inline-start: 7px;
}

.mr-7 {
  margin-inset-inline-end: 7px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-inset-inline-start: 8px;
}

.mr-8 {
  margin-inset-inline-end: 8px;
}

.mt-9 {
  margin-top: 9px;
}

.mb-9 {
  margin-bottom: 9px;
}

.ml-9 {
  margin-inset-inline-start: 9px;
}

.mr-9 {
  margin-inset-inline-end: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-inset-inline-start: 10px;
}

.mr-10 {
  margin-inset-inline-end: 10px;
}

.mt-11 {
  margin-top: 11px;
}

.mb-11 {
  margin-bottom: 11px;
}

.ml-11 {
  margin-inset-inline-start: 11px;
}

.mr-11 {
  margin-inset-inline-end: 11px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-inset-inline-start: 12px;
}

.mr-12 {
  margin-inset-inline-end: 12px;
}

.mt-13 {
  margin-top: 13px;
}

.mb-13 {
  margin-bottom: 13px;
}

.ml-13 {
  margin-inset-inline-start: 13px;
}

.mr-13 {
  margin-inset-inline-end: 13px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.ml-14 {
  margin-inset-inline-start: 14px;
}

.mr-14 {
  margin-inset-inline-end: 14px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-inset-inline-start: 15px;
}

.mr-15 {
  margin-inset-inline-end: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-inset-inline-start: 16px;
}

.mr-16 {
  margin-inset-inline-end: 16px;
}

.mt-17 {
  margin-top: 17px;
}

.mb-17 {
  margin-bottom: 17px;
}

.ml-17 {
  margin-inset-inline-start: 17px;
}

.mr-17 {
  margin-inset-inline-end: 17px;
}

.mt-18 {
  margin-top: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-inset-inline-start: 18px;
}

.mr-18 {
  margin-inset-inline-end: 18px;
}

.mt-19 {
  margin-top: 19px;
}

.mb-19 {
  margin-bottom: 19px;
}

.ml-19 {
  margin-inset-inline-start: 19px;
}

.mr-19 {
  margin-inset-inline-end: 19px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-inset-inline-start: 20px;
}

.mr-20 {
  margin-inset-inline-end: 20px;
}

.mt-21 {
  margin-top: 21px;
}

.mb-21 {
  margin-bottom: 21px;
}

.ml-21 {
  margin-inset-inline-start: 21px;
}

.mr-21 {
  margin-inset-inline-end: 21px;
}

.mt-22 {
  margin-top: 22px;
}

.mb-22 {
  margin-bottom: 22px;
}

.ml-22 {
  margin-inset-inline-start: 22px;
}

.mr-22 {
  margin-inset-inline-end: 22px;
}

.mt-23 {
  margin-top: 23px;
}

.mb-23 {
  margin-bottom: 23px;
}

.ml-23 {
  margin-inset-inline-start: 23px;
}

.mr-23 {
  margin-inset-inline-end: 23px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-inset-inline-start: 24px;
}

.mr-24 {
  margin-inset-inline-end: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-inset-inline-start: 25px;
}

.mr-25 {
  margin-inset-inline-end: 25px;
}

.mt-26 {
  margin-top: 26px;
}

.mb-26 {
  margin-bottom: 26px;
}

.ml-26 {
  margin-inset-inline-start: 26px;
}

.mr-26 {
  margin-inset-inline-end: 26px;
}

.mt-27 {
  margin-top: 27px;
}

.mb-27 {
  margin-bottom: 27px;
}

.ml-27 {
  margin-inset-inline-start: 27px;
}

.mr-27 {
  margin-inset-inline-end: 27px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.ml-28 {
  margin-inset-inline-start: 28px;
}

.mr-28 {
  margin-inset-inline-end: 28px;
}

.mt-29 {
  margin-top: 29px;
}

.mb-29 {
  margin-bottom: 29px;
}

.ml-29 {
  margin-inset-inline-start: 29px;
}

.mr-29 {
  margin-inset-inline-end: 29px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-inset-inline-start: 30px;
}

.mr-30 {
  margin-inset-inline-end: 30px;
}

.mt-31 {
  margin-top: 31px;
}

.mb-31 {
  margin-bottom: 31px;
}

.ml-31 {
  margin-inset-inline-start: 31px;
}

.mr-31 {
  margin-inset-inline-end: 31px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.ml-32 {
  margin-inset-inline-start: 32px;
}

.mr-32 {
  margin-inset-inline-end: 32px;
}

.mt-33 {
  margin-top: 33px;
}

.mb-33 {
  margin-bottom: 33px;
}

.ml-33 {
  margin-inset-inline-start: 33px;
}

.mr-33 {
  margin-inset-inline-end: 33px;
}

.mt-34 {
  margin-top: 34px;
}

.mb-34 {
  margin-bottom: 34px;
}

.ml-34 {
  margin-inset-inline-start: 34px;
}

.mr-34 {
  margin-inset-inline-end: 34px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-inset-inline-start: 35px;
}

.mr-35 {
  margin-inset-inline-end: 35px;
}

.mt-36 {
  margin-top: 36px;
}

.mb-36 {
  margin-bottom: 36px;
}

.ml-36 {
  margin-inset-inline-start: 36px;
}

.mr-36 {
  margin-inset-inline-end: 36px;
}

.mt-37 {
  margin-top: 37px;
}

.mb-37 {
  margin-bottom: 37px;
}

.ml-37 {
  margin-inset-inline-start: 37px;
}

.mr-37 {
  margin-inset-inline-end: 37px;
}

.mt-38 {
  margin-top: 38px;
}

.mb-38 {
  margin-bottom: 38px;
}

.ml-38 {
  margin-inset-inline-start: 38px;
}

.mr-38 {
  margin-inset-inline-end: 38px;
}

.mt-39 {
  margin-top: 39px;
}

.mb-39 {
  margin-bottom: 39px;
}

.ml-39 {
  margin-inset-inline-start: 39px;
}

.mr-39 {
  margin-inset-inline-end: 39px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-inset-inline-start: 40px;
}

.mr-40 {
  margin-inset-inline-end: 40px;
}

.mt-41 {
  margin-top: 41px;
}

.mb-41 {
  margin-bottom: 41px;
}

.ml-41 {
  margin-inset-inline-start: 41px;
}

.mr-41 {
  margin-inset-inline-end: 41px;
}

.mt-42 {
  margin-top: 42px;
}

.mb-42 {
  margin-bottom: 42px;
}

.ml-42 {
  margin-inset-inline-start: 42px;
}

.mr-42 {
  margin-inset-inline-end: 42px;
}

.mt-43 {
  margin-top: 43px;
}

.mb-43 {
  margin-bottom: 43px;
}

.ml-43 {
  margin-inset-inline-start: 43px;
}

.mr-43 {
  margin-inset-inline-end: 43px;
}

.mt-44 {
  margin-top: 44px;
}

.mb-44 {
  margin-bottom: 44px;
}

.ml-44 {
  margin-inset-inline-start: 44px;
}

.mr-44 {
  margin-inset-inline-end: 44px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-inset-inline-start: 45px;
}

.mr-45 {
  margin-inset-inline-end: 45px;
}

.mt-46 {
  margin-top: 46px;
}

.mb-46 {
  margin-bottom: 46px;
}

.ml-46 {
  margin-inset-inline-start: 46px;
}

.mr-46 {
  margin-inset-inline-end: 46px;
}

.mt-47 {
  margin-top: 47px;
}

.mb-47 {
  margin-bottom: 47px;
}

.ml-47 {
  margin-inset-inline-start: 47px;
}

.mr-47 {
  margin-inset-inline-end: 47px;
}

.mt-48 {
  margin-top: 48px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-48 {
  margin-inset-inline-start: 48px;
}

.mr-48 {
  margin-inset-inline-end: 48px;
}

.mt-49 {
  margin-top: 49px;
}

.mb-49 {
  margin-bottom: 49px;
}

.ml-49 {
  margin-inset-inline-start: 49px;
}

.mr-49 {
  margin-inset-inline-end: 49px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-inset-inline-start: 50px;
}

.mr-50 {
  margin-inset-inline-end: 50px;
}

.mt-51 {
  margin-top: 51px;
}

.mb-51 {
  margin-bottom: 51px;
}

.ml-51 {
  margin-inset-inline-start: 51px;
}

.mr-51 {
  margin-inset-inline-end: 51px;
}

.mt-52 {
  margin-top: 52px;
}

.mb-52 {
  margin-bottom: 52px;
}

.ml-52 {
  margin-inset-inline-start: 52px;
}

.mr-52 {
  margin-inset-inline-end: 52px;
}

.mt-53 {
  margin-top: 53px;
}

.mb-53 {
  margin-bottom: 53px;
}

.ml-53 {
  margin-inset-inline-start: 53px;
}

.mr-53 {
  margin-inset-inline-end: 53px;
}

.mt-54 {
  margin-top: 54px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-54 {
  margin-inset-inline-start: 54px;
}

.mr-54 {
  margin-inset-inline-end: 54px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-inset-inline-start: 55px;
}

.mr-55 {
  margin-inset-inline-end: 55px;
}

.mt-56 {
  margin-top: 56px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-56 {
  margin-inset-inline-start: 56px;
}

.mr-56 {
  margin-inset-inline-end: 56px;
}

.mt-57 {
  margin-top: 57px;
}

.mb-57 {
  margin-bottom: 57px;
}

.ml-57 {
  margin-inset-inline-start: 57px;
}

.mr-57 {
  margin-inset-inline-end: 57px;
}

.mt-58 {
  margin-top: 58px;
}

.mb-58 {
  margin-bottom: 58px;
}

.ml-58 {
  margin-inset-inline-start: 58px;
}

.mr-58 {
  margin-inset-inline-end: 58px;
}

.mt-59 {
  margin-top: 59px;
}

.mb-59 {
  margin-bottom: 59px;
}

.ml-59 {
  margin-inset-inline-start: 59px;
}

.mr-59 {
  margin-inset-inline-end: 59px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-inset-inline-start: 60px;
}

.mr-60 {
  margin-inset-inline-end: 60px;
}

.mt-61 {
  margin-top: 61px;
}

.mb-61 {
  margin-bottom: 61px;
}

.ml-61 {
  margin-inset-inline-start: 61px;
}

.mr-61 {
  margin-inset-inline-end: 61px;
}

.mt-62 {
  margin-top: 62px;
}

.mb-62 {
  margin-bottom: 62px;
}

.ml-62 {
  margin-inset-inline-start: 62px;
}

.mr-62 {
  margin-inset-inline-end: 62px;
}

.mt-63 {
  margin-top: 63px;
}

.mb-63 {
  margin-bottom: 63px;
}

.ml-63 {
  margin-inset-inline-start: 63px;
}

.mr-63 {
  margin-inset-inline-end: 63px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.ml-64 {
  margin-inset-inline-start: 64px;
}

.mr-64 {
  margin-inset-inline-end: 64px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-inset-inline-start: 65px;
}

.mr-65 {
  margin-inset-inline-end: 65px;
}

.mt-66 {
  margin-top: 66px;
}

.mb-66 {
  margin-bottom: 66px;
}

.ml-66 {
  margin-inset-inline-start: 66px;
}

.mr-66 {
  margin-inset-inline-end: 66px;
}

.mt-67 {
  margin-top: 67px;
}

.mb-67 {
  margin-bottom: 67px;
}

.ml-67 {
  margin-inset-inline-start: 67px;
}

.mr-67 {
  margin-inset-inline-end: 67px;
}

.mt-68 {
  margin-top: 68px;
}

.mb-68 {
  margin-bottom: 68px;
}

.ml-68 {
  margin-inset-inline-start: 68px;
}

.mr-68 {
  margin-inset-inline-end: 68px;
}

.mt-69 {
  margin-top: 69px;
}

.mb-69 {
  margin-bottom: 69px;
}

.ml-69 {
  margin-inset-inline-start: 69px;
}

.mr-69 {
  margin-inset-inline-end: 69px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-inset-inline-start: 70px;
}

.mr-70 {
  margin-inset-inline-end: 70px;
}

.mt-71 {
  margin-top: 71px;
}

.mb-71 {
  margin-bottom: 71px;
}

.ml-71 {
  margin-inset-inline-start: 71px;
}

.mr-71 {
  margin-inset-inline-end: 71px;
}

.mt-72 {
  margin-top: 72px;
}

.mb-72 {
  margin-bottom: 72px;
}

.ml-72 {
  margin-inset-inline-start: 72px;
}

.mr-72 {
  margin-inset-inline-end: 72px;
}

.mt-73 {
  margin-top: 73px;
}

.mb-73 {
  margin-bottom: 73px;
}

.ml-73 {
  margin-inset-inline-start: 73px;
}

.mr-73 {
  margin-inset-inline-end: 73px;
}

.mt-74 {
  margin-top: 74px;
}

.mb-74 {
  margin-bottom: 74px;
}

.ml-74 {
  margin-inset-inline-start: 74px;
}

.mr-74 {
  margin-inset-inline-end: 74px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-inset-inline-start: 75px;
}

.mr-75 {
  margin-inset-inline-end: 75px;
}

.mt-76 {
  margin-top: 76px;
}

.mb-76 {
  margin-bottom: 76px;
}

.ml-76 {
  margin-inset-inline-start: 76px;
}

.mr-76 {
  margin-inset-inline-end: 76px;
}

.mt-77 {
  margin-top: 77px;
}

.mb-77 {
  margin-bottom: 77px;
}

.ml-77 {
  margin-inset-inline-start: 77px;
}

.mr-77 {
  margin-inset-inline-end: 77px;
}

.mt-78 {
  margin-top: 78px;
}

.mb-78 {
  margin-bottom: 78px;
}

.ml-78 {
  margin-inset-inline-start: 78px;
}

.mr-78 {
  margin-inset-inline-end: 78px;
}

.mt-79 {
  margin-top: 79px;
}

.mb-79 {
  margin-bottom: 79px;
}

.ml-79 {
  margin-inset-inline-start: 79px;
}

.mr-79 {
  margin-inset-inline-end: 79px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-inset-inline-start: 80px;
}

.mr-80 {
  margin-inset-inline-end: 80px;
}

.mt-81 {
  margin-top: 81px;
}

.mb-81 {
  margin-bottom: 81px;
}

.ml-81 {
  margin-inset-inline-start: 81px;
}

.mr-81 {
  margin-inset-inline-end: 81px;
}

.mt-82 {
  margin-top: 82px;
}

.mb-82 {
  margin-bottom: 82px;
}

.ml-82 {
  margin-inset-inline-start: 82px;
}

.mr-82 {
  margin-inset-inline-end: 82px;
}

.mt-83 {
  margin-top: 83px;
}

.mb-83 {
  margin-bottom: 83px;
}

.ml-83 {
  margin-inset-inline-start: 83px;
}

.mr-83 {
  margin-inset-inline-end: 83px;
}

.mt-84 {
  margin-top: 84px;
}

.mb-84 {
  margin-bottom: 84px;
}

.ml-84 {
  margin-inset-inline-start: 84px;
}

.mr-84 {
  margin-inset-inline-end: 84px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-inset-inline-start: 85px;
}

.mr-85 {
  margin-inset-inline-end: 85px;
}

.mt-86 {
  margin-top: 86px;
}

.mb-86 {
  margin-bottom: 86px;
}

.ml-86 {
  margin-inset-inline-start: 86px;
}

.mr-86 {
  margin-inset-inline-end: 86px;
}

.mt-87 {
  margin-top: 87px;
}

.mb-87 {
  margin-bottom: 87px;
}

.ml-87 {
  margin-inset-inline-start: 87px;
}

.mr-87 {
  margin-inset-inline-end: 87px;
}

.mt-88 {
  margin-top: 88px;
}

.mb-88 {
  margin-bottom: 88px;
}

.ml-88 {
  margin-inset-inline-start: 88px;
}

.mr-88 {
  margin-inset-inline-end: 88px;
}

.mt-89 {
  margin-top: 89px;
}

.mb-89 {
  margin-bottom: 89px;
}

.ml-89 {
  margin-inset-inline-start: 89px;
}

.mr-89 {
  margin-inset-inline-end: 89px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-inset-inline-start: 90px;
}

.mr-90 {
  margin-inset-inline-end: 90px;
}

.mt-91 {
  margin-top: 91px;
}

.mb-91 {
  margin-bottom: 91px;
}

.ml-91 {
  margin-inset-inline-start: 91px;
}

.mr-91 {
  margin-inset-inline-end: 91px;
}

.mt-92 {
  margin-top: 92px;
}

.mb-92 {
  margin-bottom: 92px;
}

.ml-92 {
  margin-inset-inline-start: 92px;
}

.mr-92 {
  margin-inset-inline-end: 92px;
}

.mt-93 {
  margin-top: 93px;
}

.mb-93 {
  margin-bottom: 93px;
}

.ml-93 {
  margin-inset-inline-start: 93px;
}

.mr-93 {
  margin-inset-inline-end: 93px;
}

.mt-94 {
  margin-top: 94px;
}

.mb-94 {
  margin-bottom: 94px;
}

.ml-94 {
  margin-inset-inline-start: 94px;
}

.mr-94 {
  margin-inset-inline-end: 94px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-inset-inline-start: 95px;
}

.mr-95 {
  margin-inset-inline-end: 95px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-96 {
  margin-bottom: 96px;
}

.ml-96 {
  margin-inset-inline-start: 96px;
}

.mr-96 {
  margin-inset-inline-end: 96px;
}

.mt-97 {
  margin-top: 97px;
}

.mb-97 {
  margin-bottom: 97px;
}

.ml-97 {
  margin-inset-inline-start: 97px;
}

.mr-97 {
  margin-inset-inline-end: 97px;
}

.mt-98 {
  margin-top: 98px;
}

.mb-98 {
  margin-bottom: 98px;
}

.ml-98 {
  margin-inset-inline-start: 98px;
}

.mr-98 {
  margin-inset-inline-end: 98px;
}

.mt-99 {
  margin-top: 99px;
}

.mb-99 {
  margin-bottom: 99px;
}

.ml-99 {
  margin-inset-inline-start: 99px;
}

.mr-99 {
  margin-inset-inline-end: 99px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-inset-inline-start: 100px;
}

.mr-100 {
  margin-inset-inline-end: 100px;
}

.mt-101 {
  margin-top: 101px;
}

.mb-101 {
  margin-bottom: 101px;
}

.ml-101 {
  margin-inset-inline-start: 101px;
}

.mr-101 {
  margin-inset-inline-end: 101px;
}

.mt-102 {
  margin-top: 102px;
}

.mb-102 {
  margin-bottom: 102px;
}

.ml-102 {
  margin-inset-inline-start: 102px;
}

.mr-102 {
  margin-inset-inline-end: 102px;
}

.mt-103 {
  margin-top: 103px;
}

.mb-103 {
  margin-bottom: 103px;
}

.ml-103 {
  margin-inset-inline-start: 103px;
}

.mr-103 {
  margin-inset-inline-end: 103px;
}

.mt-104 {
  margin-top: 104px;
}

.mb-104 {
  margin-bottom: 104px;
}

.ml-104 {
  margin-inset-inline-start: 104px;
}

.mr-104 {
  margin-inset-inline-end: 104px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-inset-inline-start: 105px;
}

.mr-105 {
  margin-inset-inline-end: 105px;
}

.mt-106 {
  margin-top: 106px;
}

.mb-106 {
  margin-bottom: 106px;
}

.ml-106 {
  margin-inset-inline-start: 106px;
}

.mr-106 {
  margin-inset-inline-end: 106px;
}

.mt-107 {
  margin-top: 107px;
}

.mb-107 {
  margin-bottom: 107px;
}

.ml-107 {
  margin-inset-inline-start: 107px;
}

.mr-107 {
  margin-inset-inline-end: 107px;
}

.mt-108 {
  margin-top: 108px;
}

.mb-108 {
  margin-bottom: 108px;
}

.ml-108 {
  margin-inset-inline-start: 108px;
}

.mr-108 {
  margin-inset-inline-end: 108px;
}

.mt-109 {
  margin-top: 109px;
}

.mb-109 {
  margin-bottom: 109px;
}

.ml-109 {
  margin-inset-inline-start: 109px;
}

.mr-109 {
  margin-inset-inline-end: 109px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-inset-inline-start: 110px;
}

.mr-110 {
  margin-inset-inline-end: 110px;
}

.mt-111 {
  margin-top: 111px;
}

.mb-111 {
  margin-bottom: 111px;
}

.ml-111 {
  margin-inset-inline-start: 111px;
}

.mr-111 {
  margin-inset-inline-end: 111px;
}

.mt-112 {
  margin-top: 112px;
}

.mb-112 {
  margin-bottom: 112px;
}

.ml-112 {
  margin-inset-inline-start: 112px;
}

.mr-112 {
  margin-inset-inline-end: 112px;
}

.mt-113 {
  margin-top: 113px;
}

.mb-113 {
  margin-bottom: 113px;
}

.ml-113 {
  margin-inset-inline-start: 113px;
}

.mr-113 {
  margin-inset-inline-end: 113px;
}

.mt-114 {
  margin-top: 114px;
}

.mb-114 {
  margin-bottom: 114px;
}

.ml-114 {
  margin-inset-inline-start: 114px;
}

.mr-114 {
  margin-inset-inline-end: 114px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-inset-inline-start: 115px;
}

.mr-115 {
  margin-inset-inline-end: 115px;
}

.mt-116 {
  margin-top: 116px;
}

.mb-116 {
  margin-bottom: 116px;
}

.ml-116 {
  margin-inset-inline-start: 116px;
}

.mr-116 {
  margin-inset-inline-end: 116px;
}

.mt-117 {
  margin-top: 117px;
}

.mb-117 {
  margin-bottom: 117px;
}

.ml-117 {
  margin-inset-inline-start: 117px;
}

.mr-117 {
  margin-inset-inline-end: 117px;
}

.mt-118 {
  margin-top: 118px;
}

.mb-118 {
  margin-bottom: 118px;
}

.ml-118 {
  margin-inset-inline-start: 118px;
}

.mr-118 {
  margin-inset-inline-end: 118px;
}

.mt-119 {
  margin-top: 119px;
}

.mb-119 {
  margin-bottom: 119px;
}

.ml-119 {
  margin-inset-inline-start: 119px;
}

.mr-119 {
  margin-inset-inline-end: 119px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-inset-inline-start: 120px;
}

.mr-120 {
  margin-inset-inline-end: 120px;
}

.mt-121 {
  margin-top: 121px;
}

.mb-121 {
  margin-bottom: 121px;
}

.ml-121 {
  margin-inset-inline-start: 121px;
}

.mr-121 {
  margin-inset-inline-end: 121px;
}

.mt-122 {
  margin-top: 122px;
}

.mb-122 {
  margin-bottom: 122px;
}

.ml-122 {
  margin-inset-inline-start: 122px;
}

.mr-122 {
  margin-inset-inline-end: 122px;
}

.mt-123 {
  margin-top: 123px;
}

.mb-123 {
  margin-bottom: 123px;
}

.ml-123 {
  margin-inset-inline-start: 123px;
}

.mr-123 {
  margin-inset-inline-end: 123px;
}

.mt-124 {
  margin-top: 124px;
}

.mb-124 {
  margin-bottom: 124px;
}

.ml-124 {
  margin-inset-inline-start: 124px;
}

.mr-124 {
  margin-inset-inline-end: 124px;
}

.mt-125 {
  margin-top: 125px;
}

.mb-125 {
  margin-bottom: 125px;
}

.ml-125 {
  margin-inset-inline-start: 125px;
}

.mr-125 {
  margin-inset-inline-end: 125px;
}

.mt-126 {
  margin-top: 126px;
}

.mb-126 {
  margin-bottom: 126px;
}

.ml-126 {
  margin-inset-inline-start: 126px;
}

.mr-126 {
  margin-inset-inline-end: 126px;
}

.mt-127 {
  margin-top: 127px;
}

.mb-127 {
  margin-bottom: 127px;
}

.ml-127 {
  margin-inset-inline-start: 127px;
}

.mr-127 {
  margin-inset-inline-end: 127px;
}

.mt-128 {
  margin-top: 128px;
}

.mb-128 {
  margin-bottom: 128px;
}

.ml-128 {
  margin-inset-inline-start: 128px;
}

.mr-128 {
  margin-inset-inline-end: 128px;
}

.mt-129 {
  margin-top: 129px;
}

.mb-129 {
  margin-bottom: 129px;
}

.ml-129 {
  margin-inset-inline-start: 129px;
}

.mr-129 {
  margin-inset-inline-end: 129px;
}

.mt-130 {
  margin-top: 130px;
}

.mb-130 {
  margin-bottom: 130px;
}

.ml-130 {
  margin-inset-inline-start: 130px;
}

.mr-130 {
  margin-inset-inline-end: 130px;
}

.mt-131 {
  margin-top: 131px;
}

.mb-131 {
  margin-bottom: 131px;
}

.ml-131 {
  margin-inset-inline-start: 131px;
}

.mr-131 {
  margin-inset-inline-end: 131px;
}

.mt-132 {
  margin-top: 132px;
}

.mb-132 {
  margin-bottom: 132px;
}

.ml-132 {
  margin-inset-inline-start: 132px;
}

.mr-132 {
  margin-inset-inline-end: 132px;
}

.mt-133 {
  margin-top: 133px;
}

.mb-133 {
  margin-bottom: 133px;
}

.ml-133 {
  margin-inset-inline-start: 133px;
}

.mr-133 {
  margin-inset-inline-end: 133px;
}

.mt-134 {
  margin-top: 134px;
}

.mb-134 {
  margin-bottom: 134px;
}

.ml-134 {
  margin-inset-inline-start: 134px;
}

.mr-134 {
  margin-inset-inline-end: 134px;
}

.mt-135 {
  margin-top: 135px;
}

.mb-135 {
  margin-bottom: 135px;
}

.ml-135 {
  margin-inset-inline-start: 135px;
}

.mr-135 {
  margin-inset-inline-end: 135px;
}

.mt-136 {
  margin-top: 136px;
}

.mb-136 {
  margin-bottom: 136px;
}

.ml-136 {
  margin-inset-inline-start: 136px;
}

.mr-136 {
  margin-inset-inline-end: 136px;
}

.mt-137 {
  margin-top: 137px;
}

.mb-137 {
  margin-bottom: 137px;
}

.ml-137 {
  margin-inset-inline-start: 137px;
}

.mr-137 {
  margin-inset-inline-end: 137px;
}

.mt-138 {
  margin-top: 138px;
}

.mb-138 {
  margin-bottom: 138px;
}

.ml-138 {
  margin-inset-inline-start: 138px;
}

.mr-138 {
  margin-inset-inline-end: 138px;
}

.mt-139 {
  margin-top: 139px;
}

.mb-139 {
  margin-bottom: 139px;
}

.ml-139 {
  margin-inset-inline-start: 139px;
}

.mr-139 {
  margin-inset-inline-end: 139px;
}

.mt-140 {
  margin-top: 140px;
}

.mb-140 {
  margin-bottom: 140px;
}

.ml-140 {
  margin-inset-inline-start: 140px;
}

.mr-140 {
  margin-inset-inline-end: 140px;
}

.mt-141 {
  margin-top: 141px;
}

.mb-141 {
  margin-bottom: 141px;
}

.ml-141 {
  margin-inset-inline-start: 141px;
}

.mr-141 {
  margin-inset-inline-end: 141px;
}

.mt-142 {
  margin-top: 142px;
}

.mb-142 {
  margin-bottom: 142px;
}

.ml-142 {
  margin-inset-inline-start: 142px;
}

.mr-142 {
  margin-inset-inline-end: 142px;
}

.mt-143 {
  margin-top: 143px;
}

.mb-143 {
  margin-bottom: 143px;
}

.ml-143 {
  margin-inset-inline-start: 143px;
}

.mr-143 {
  margin-inset-inline-end: 143px;
}

.mt-144 {
  margin-top: 144px;
}

.mb-144 {
  margin-bottom: 144px;
}

.ml-144 {
  margin-inset-inline-start: 144px;
}

.mr-144 {
  margin-inset-inline-end: 144px;
}

.mt-145 {
  margin-top: 145px;
}

.mb-145 {
  margin-bottom: 145px;
}

.ml-145 {
  margin-inset-inline-start: 145px;
}

.mr-145 {
  margin-inset-inline-end: 145px;
}

.mt-146 {
  margin-top: 146px;
}

.mb-146 {
  margin-bottom: 146px;
}

.ml-146 {
  margin-inset-inline-start: 146px;
}

.mr-146 {
  margin-inset-inline-end: 146px;
}

.mt-147 {
  margin-top: 147px;
}

.mb-147 {
  margin-bottom: 147px;
}

.ml-147 {
  margin-inset-inline-start: 147px;
}

.mr-147 {
  margin-inset-inline-end: 147px;
}

.mt-148 {
  margin-top: 148px;
}

.mb-148 {
  margin-bottom: 148px;
}

.ml-148 {
  margin-inset-inline-start: 148px;
}

.mr-148 {
  margin-inset-inline-end: 148px;
}

.mt-149 {
  margin-top: 149px;
}

.mb-149 {
  margin-bottom: 149px;
}

.ml-149 {
  margin-inset-inline-start: 149px;
}

.mr-149 {
  margin-inset-inline-end: 149px;
}

.mt-150 {
  margin-top: 150px;
}

.mb-150 {
  margin-bottom: 150px;
}

.ml-150 {
  margin-inset-inline-start: 150px;
}

.mr-150 {
  margin-inset-inline-end: 150px;
}

.mt-151 {
  margin-top: 151px;
}

.mb-151 {
  margin-bottom: 151px;
}

.ml-151 {
  margin-inset-inline-start: 151px;
}

.mr-151 {
  margin-inset-inline-end: 151px;
}

.mt-152 {
  margin-top: 152px;
}

.mb-152 {
  margin-bottom: 152px;
}

.ml-152 {
  margin-inset-inline-start: 152px;
}

.mr-152 {
  margin-inset-inline-end: 152px;
}

.mt-153 {
  margin-top: 153px;
}

.mb-153 {
  margin-bottom: 153px;
}

.ml-153 {
  margin-inset-inline-start: 153px;
}

.mr-153 {
  margin-inset-inline-end: 153px;
}

.mt-154 {
  margin-top: 154px;
}

.mb-154 {
  margin-bottom: 154px;
}

.ml-154 {
  margin-inset-inline-start: 154px;
}

.mr-154 {
  margin-inset-inline-end: 154px;
}

.mt-155 {
  margin-top: 155px;
}

.mb-155 {
  margin-bottom: 155px;
}

.ml-155 {
  margin-inset-inline-start: 155px;
}

.mr-155 {
  margin-inset-inline-end: 155px;
}

.mt-156 {
  margin-top: 156px;
}

.mb-156 {
  margin-bottom: 156px;
}

.ml-156 {
  margin-inset-inline-start: 156px;
}

.mr-156 {
  margin-inset-inline-end: 156px;
}

.mt-157 {
  margin-top: 157px;
}

.mb-157 {
  margin-bottom: 157px;
}

.ml-157 {
  margin-inset-inline-start: 157px;
}

.mr-157 {
  margin-inset-inline-end: 157px;
}

.mt-158 {
  margin-top: 158px;
}

.mb-158 {
  margin-bottom: 158px;
}

.ml-158 {
  margin-inset-inline-start: 158px;
}

.mr-158 {
  margin-inset-inline-end: 158px;
}

.mt-159 {
  margin-top: 159px;
}

.mb-159 {
  margin-bottom: 159px;
}

.ml-159 {
  margin-inset-inline-start: 159px;
}

.mr-159 {
  margin-inset-inline-end: 159px;
}

.mt-160 {
  margin-top: 160px;
}

.mb-160 {
  margin-bottom: 160px;
}

.ml-160 {
  margin-inset-inline-start: 160px;
}

.mr-160 {
  margin-inset-inline-end: 160px;
}

.mt-161 {
  margin-top: 161px;
}

.mb-161 {
  margin-bottom: 161px;
}

.ml-161 {
  margin-inset-inline-start: 161px;
}

.mr-161 {
  margin-inset-inline-end: 161px;
}

.mt-162 {
  margin-top: 162px;
}

.mb-162 {
  margin-bottom: 162px;
}

.ml-162 {
  margin-inset-inline-start: 162px;
}

.mr-162 {
  margin-inset-inline-end: 162px;
}

.mt-163 {
  margin-top: 163px;
}

.mb-163 {
  margin-bottom: 163px;
}

.ml-163 {
  margin-inset-inline-start: 163px;
}

.mr-163 {
  margin-inset-inline-end: 163px;
}

.mt-164 {
  margin-top: 164px;
}

.mb-164 {
  margin-bottom: 164px;
}

.ml-164 {
  margin-inset-inline-start: 164px;
}

.mr-164 {
  margin-inset-inline-end: 164px;
}

.mt-165 {
  margin-top: 165px;
}

.mb-165 {
  margin-bottom: 165px;
}

.ml-165 {
  margin-inset-inline-start: 165px;
}

.mr-165 {
  margin-inset-inline-end: 165px;
}

.mt-166 {
  margin-top: 166px;
}

.mb-166 {
  margin-bottom: 166px;
}

.ml-166 {
  margin-inset-inline-start: 166px;
}

.mr-166 {
  margin-inset-inline-end: 166px;
}

.mt-167 {
  margin-top: 167px;
}

.mb-167 {
  margin-bottom: 167px;
}

.ml-167 {
  margin-inset-inline-start: 167px;
}

.mr-167 {
  margin-inset-inline-end: 167px;
}

.mt-168 {
  margin-top: 168px;
}

.mb-168 {
  margin-bottom: 168px;
}

.ml-168 {
  margin-inset-inline-start: 168px;
}

.mr-168 {
  margin-inset-inline-end: 168px;
}

.mt-169 {
  margin-top: 169px;
}

.mb-169 {
  margin-bottom: 169px;
}

.ml-169 {
  margin-inset-inline-start: 169px;
}

.mr-169 {
  margin-inset-inline-end: 169px;
}

.mt-170 {
  margin-top: 170px;
}

.mb-170 {
  margin-bottom: 170px;
}

.ml-170 {
  margin-inset-inline-start: 170px;
}

.mr-170 {
  margin-inset-inline-end: 170px;
}

.mt-171 {
  margin-top: 171px;
}

.mb-171 {
  margin-bottom: 171px;
}

.ml-171 {
  margin-inset-inline-start: 171px;
}

.mr-171 {
  margin-inset-inline-end: 171px;
}

.mt-172 {
  margin-top: 172px;
}

.mb-172 {
  margin-bottom: 172px;
}

.ml-172 {
  margin-inset-inline-start: 172px;
}

.mr-172 {
  margin-inset-inline-end: 172px;
}

.mt-173 {
  margin-top: 173px;
}

.mb-173 {
  margin-bottom: 173px;
}

.ml-173 {
  margin-inset-inline-start: 173px;
}

.mr-173 {
  margin-inset-inline-end: 173px;
}

.mt-174 {
  margin-top: 174px;
}

.mb-174 {
  margin-bottom: 174px;
}

.ml-174 {
  margin-inset-inline-start: 174px;
}

.mr-174 {
  margin-inset-inline-end: 174px;
}

.mt-175 {
  margin-top: 175px;
}

.mb-175 {
  margin-bottom: 175px;
}

.ml-175 {
  margin-inset-inline-start: 175px;
}

.mr-175 {
  margin-inset-inline-end: 175px;
}

.mt-176 {
  margin-top: 176px;
}

.mb-176 {
  margin-bottom: 176px;
}

.ml-176 {
  margin-inset-inline-start: 176px;
}

.mr-176 {
  margin-inset-inline-end: 176px;
}

.mt-177 {
  margin-top: 177px;
}

.mb-177 {
  margin-bottom: 177px;
}

.ml-177 {
  margin-inset-inline-start: 177px;
}

.mr-177 {
  margin-inset-inline-end: 177px;
}

.mt-178 {
  margin-top: 178px;
}

.mb-178 {
  margin-bottom: 178px;
}

.ml-178 {
  margin-inset-inline-start: 178px;
}

.mr-178 {
  margin-inset-inline-end: 178px;
}

.mt-179 {
  margin-top: 179px;
}

.mb-179 {
  margin-bottom: 179px;
}

.ml-179 {
  margin-inset-inline-start: 179px;
}

.mr-179 {
  margin-inset-inline-end: 179px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.ml-180 {
  margin-inset-inline-start: 180px;
}

.mr-180 {
  margin-inset-inline-end: 180px;
}

.mt-181 {
  margin-top: 181px;
}

.mb-181 {
  margin-bottom: 181px;
}

.ml-181 {
  margin-inset-inline-start: 181px;
}

.mr-181 {
  margin-inset-inline-end: 181px;
}

.mt-182 {
  margin-top: 182px;
}

.mb-182 {
  margin-bottom: 182px;
}

.ml-182 {
  margin-inset-inline-start: 182px;
}

.mr-182 {
  margin-inset-inline-end: 182px;
}

.mt-183 {
  margin-top: 183px;
}

.mb-183 {
  margin-bottom: 183px;
}

.ml-183 {
  margin-inset-inline-start: 183px;
}

.mr-183 {
  margin-inset-inline-end: 183px;
}

.mt-184 {
  margin-top: 184px;
}

.mb-184 {
  margin-bottom: 184px;
}

.ml-184 {
  margin-inset-inline-start: 184px;
}

.mr-184 {
  margin-inset-inline-end: 184px;
}

.mt-185 {
  margin-top: 185px;
}

.mb-185 {
  margin-bottom: 185px;
}

.ml-185 {
  margin-inset-inline-start: 185px;
}

.mr-185 {
  margin-inset-inline-end: 185px;
}

.mt-186 {
  margin-top: 186px;
}

.mb-186 {
  margin-bottom: 186px;
}

.ml-186 {
  margin-inset-inline-start: 186px;
}

.mr-186 {
  margin-inset-inline-end: 186px;
}

.mt-187 {
  margin-top: 187px;
}

.mb-187 {
  margin-bottom: 187px;
}

.ml-187 {
  margin-inset-inline-start: 187px;
}

.mr-187 {
  margin-inset-inline-end: 187px;
}

.mt-188 {
  margin-top: 188px;
}

.mb-188 {
  margin-bottom: 188px;
}

.ml-188 {
  margin-inset-inline-start: 188px;
}

.mr-188 {
  margin-inset-inline-end: 188px;
}

.mt-189 {
  margin-top: 189px;
}

.mb-189 {
  margin-bottom: 189px;
}

.ml-189 {
  margin-inset-inline-start: 189px;
}

.mr-189 {
  margin-inset-inline-end: 189px;
}

.mt-190 {
  margin-top: 190px;
}

.mb-190 {
  margin-bottom: 190px;
}

.ml-190 {
  margin-inset-inline-start: 190px;
}

.mr-190 {
  margin-inset-inline-end: 190px;
}

.mt-191 {
  margin-top: 191px;
}

.mb-191 {
  margin-bottom: 191px;
}

.ml-191 {
  margin-inset-inline-start: 191px;
}

.mr-191 {
  margin-inset-inline-end: 191px;
}

.mt-192 {
  margin-top: 192px;
}

.mb-192 {
  margin-bottom: 192px;
}

.ml-192 {
  margin-inset-inline-start: 192px;
}

.mr-192 {
  margin-inset-inline-end: 192px;
}

.mt-193 {
  margin-top: 193px;
}

.mb-193 {
  margin-bottom: 193px;
}

.ml-193 {
  margin-inset-inline-start: 193px;
}

.mr-193 {
  margin-inset-inline-end: 193px;
}

.mt-194 {
  margin-top: 194px;
}

.mb-194 {
  margin-bottom: 194px;
}

.ml-194 {
  margin-inset-inline-start: 194px;
}

.mr-194 {
  margin-inset-inline-end: 194px;
}

.mt-195 {
  margin-top: 195px;
}

.mb-195 {
  margin-bottom: 195px;
}

.ml-195 {
  margin-inset-inline-start: 195px;
}

.mr-195 {
  margin-inset-inline-end: 195px;
}

.mt-196 {
  margin-top: 196px;
}

.mb-196 {
  margin-bottom: 196px;
}

.ml-196 {
  margin-inset-inline-start: 196px;
}

.mr-196 {
  margin-inset-inline-end: 196px;
}

.mt-197 {
  margin-top: 197px;
}

.mb-197 {
  margin-bottom: 197px;
}

.ml-197 {
  margin-inset-inline-start: 197px;
}

.mr-197 {
  margin-inset-inline-end: 197px;
}

.mt-198 {
  margin-top: 198px;
}

.mb-198 {
  margin-bottom: 198px;
}

.ml-198 {
  margin-inset-inline-start: 198px;
}

.mr-198 {
  margin-inset-inline-end: 198px;
}

.mt-199 {
  margin-top: 199px;
}

.mb-199 {
  margin-bottom: 199px;
}

.ml-199 {
  margin-inset-inline-start: 199px;
}

.mr-199 {
  margin-inset-inline-end: 199px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-200 {
  margin-inset-inline-start: 200px;
}

.mr-200 {
  margin-inset-inline-end: 200px;
}

.pt-1 {
  padding-top: 1px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pl-1 {
  padding-inset-inline-start: 1px;
}

.pr-1 {
  padding-inset-inline-end: 1px;
}

.pt-2 {
  padding-top: 2px;
}

.pb-2 {
  padding-bottom: 2px;
}

.pl-2 {
  padding-inset-inline-start: 2px;
}

.pr-2 {
  padding-inset-inline-end: 2px;
}

.pt-3 {
  padding-top: 3px;
}

.pb-3 {
  padding-bottom: 3px;
}

.pl-3 {
  padding-inset-inline-start: 3px;
}

.pr-3 {
  padding-inset-inline-end: 3px;
}

.pt-4 {
  padding-top: 4px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pl-4 {
  padding-inset-inline-start: 4px;
}

.pr-4 {
  padding-inset-inline-end: 4px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-inset-inline-start: 5px;
}

.pr-5 {
  padding-inset-inline-end: 5px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.pl-6 {
  padding-inset-inline-start: 6px;
}

.pr-6 {
  padding-inset-inline-end: 6px;
}

.pt-7 {
  padding-top: 7px;
}

.pb-7 {
  padding-bottom: 7px;
}

.pl-7 {
  padding-inset-inline-start: 7px;
}

.pr-7 {
  padding-inset-inline-end: 7px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pl-8 {
  padding-inset-inline-start: 8px;
}

.pr-8 {
  padding-inset-inline-end: 8px;
}

.pt-9 {
  padding-top: 9px;
}

.pb-9 {
  padding-bottom: 9px;
}

.pl-9 {
  padding-inset-inline-start: 9px;
}

.pr-9 {
  padding-inset-inline-end: 9px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-inset-inline-start: 10px;
}

.pr-10 {
  padding-inset-inline-end: 10px;
}

.pt-11 {
  padding-top: 11px;
}

.pb-11 {
  padding-bottom: 11px;
}

.pl-11 {
  padding-inset-inline-start: 11px;
}

.pr-11 {
  padding-inset-inline-end: 11px;
}

.pt-12 {
  padding-top: 12px;
}

.pb-12 {
  padding-bottom: 12px;
}

.pl-12 {
  padding-inset-inline-start: 12px;
}

.pr-12 {
  padding-inset-inline-end: 12px;
}

.pt-13 {
  padding-top: 13px;
}

.pb-13 {
  padding-bottom: 13px;
}

.pl-13 {
  padding-inset-inline-start: 13px;
}

.pr-13 {
  padding-inset-inline-end: 13px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-14 {
  padding-bottom: 14px;
}

.pl-14 {
  padding-inset-inline-start: 14px;
}

.pr-14 {
  padding-inset-inline-end: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-inset-inline-start: 15px;
}

.pr-15 {
  padding-inset-inline-end: 15px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pl-16 {
  padding-inset-inline-start: 16px;
}

.pr-16 {
  padding-inset-inline-end: 16px;
}

.pt-17 {
  padding-top: 17px;
}

.pb-17 {
  padding-bottom: 17px;
}

.pl-17 {
  padding-inset-inline-start: 17px;
}

.pr-17 {
  padding-inset-inline-end: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pb-18 {
  padding-bottom: 18px;
}

.pl-18 {
  padding-inset-inline-start: 18px;
}

.pr-18 {
  padding-inset-inline-end: 18px;
}

.pt-19 {
  padding-top: 19px;
}

.pb-19 {
  padding-bottom: 19px;
}

.pl-19 {
  padding-inset-inline-start: 19px;
}

.pr-19 {
  padding-inset-inline-end: 19px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-inset-inline-start: 20px;
}

.pr-20 {
  padding-inset-inline-end: 20px;
}

.pt-21 {
  padding-top: 21px;
}

.pb-21 {
  padding-bottom: 21px;
}

.pl-21 {
  padding-inset-inline-start: 21px;
}

.pr-21 {
  padding-inset-inline-end: 21px;
}

.pt-22 {
  padding-top: 22px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pl-22 {
  padding-inset-inline-start: 22px;
}

.pr-22 {
  padding-inset-inline-end: 22px;
}

.pt-23 {
  padding-top: 23px;
}

.pb-23 {
  padding-bottom: 23px;
}

.pl-23 {
  padding-inset-inline-start: 23px;
}

.pr-23 {
  padding-inset-inline-end: 23px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pl-24 {
  padding-inset-inline-start: 24px;
}

.pr-24 {
  padding-inset-inline-end: 24px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-inset-inline-start: 25px;
}

.pr-25 {
  padding-inset-inline-end: 25px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pl-26 {
  padding-inset-inline-start: 26px;
}

.pr-26 {
  padding-inset-inline-end: 26px;
}

.pt-27 {
  padding-top: 27px;
}

.pb-27 {
  padding-bottom: 27px;
}

.pl-27 {
  padding-inset-inline-start: 27px;
}

.pr-27 {
  padding-inset-inline-end: 27px;
}

.pt-28 {
  padding-top: 28px;
}

.pb-28 {
  padding-bottom: 28px;
}

.pl-28 {
  padding-inset-inline-start: 28px;
}

.pr-28 {
  padding-inset-inline-end: 28px;
}

.pt-29 {
  padding-top: 29px;
}

.pb-29 {
  padding-bottom: 29px;
}

.pl-29 {
  padding-inset-inline-start: 29px;
}

.pr-29 {
  padding-inset-inline-end: 29px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-inset-inline-start: 30px;
}

.pr-30 {
  padding-inset-inline-end: 30px;
}

.pt-31 {
  padding-top: 31px;
}

.pb-31 {
  padding-bottom: 31px;
}

.pl-31 {
  padding-inset-inline-start: 31px;
}

.pr-31 {
  padding-inset-inline-end: 31px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.pl-32 {
  padding-inset-inline-start: 32px;
}

.pr-32 {
  padding-inset-inline-end: 32px;
}

.pt-33 {
  padding-top: 33px;
}

.pb-33 {
  padding-bottom: 33px;
}

.pl-33 {
  padding-inset-inline-start: 33px;
}

.pr-33 {
  padding-inset-inline-end: 33px;
}

.pt-34 {
  padding-top: 34px;
}

.pb-34 {
  padding-bottom: 34px;
}

.pl-34 {
  padding-inset-inline-start: 34px;
}

.pr-34 {
  padding-inset-inline-end: 34px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-inset-inline-start: 35px;
}

.pr-35 {
  padding-inset-inline-end: 35px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pl-36 {
  padding-inset-inline-start: 36px;
}

.pr-36 {
  padding-inset-inline-end: 36px;
}

.pt-37 {
  padding-top: 37px;
}

.pb-37 {
  padding-bottom: 37px;
}

.pl-37 {
  padding-inset-inline-start: 37px;
}

.pr-37 {
  padding-inset-inline-end: 37px;
}

.pt-38 {
  padding-top: 38px;
}

.pb-38 {
  padding-bottom: 38px;
}

.pl-38 {
  padding-inset-inline-start: 38px;
}

.pr-38 {
  padding-inset-inline-end: 38px;
}

.pt-39 {
  padding-top: 39px;
}

.pb-39 {
  padding-bottom: 39px;
}

.pl-39 {
  padding-inset-inline-start: 39px;
}

.pr-39 {
  padding-inset-inline-end: 39px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-inset-inline-start: 40px;
}

.pr-40 {
  padding-inset-inline-end: 40px;
}

.pt-41 {
  padding-top: 41px;
}

.pb-41 {
  padding-bottom: 41px;
}

.pl-41 {
  padding-inset-inline-start: 41px;
}

.pr-41 {
  padding-inset-inline-end: 41px;
}

.pt-42 {
  padding-top: 42px;
}

.pb-42 {
  padding-bottom: 42px;
}

.pl-42 {
  padding-inset-inline-start: 42px;
}

.pr-42 {
  padding-inset-inline-end: 42px;
}

.pt-43 {
  padding-top: 43px;
}

.pb-43 {
  padding-bottom: 43px;
}

.pl-43 {
  padding-inset-inline-start: 43px;
}

.pr-43 {
  padding-inset-inline-end: 43px;
}

.pt-44 {
  padding-top: 44px;
}

.pb-44 {
  padding-bottom: 44px;
}

.pl-44 {
  padding-inset-inline-start: 44px;
}

.pr-44 {
  padding-inset-inline-end: 44px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-inset-inline-start: 45px;
}

.pr-45 {
  padding-inset-inline-end: 45px;
}

.pt-46 {
  padding-top: 46px;
}

.pb-46 {
  padding-bottom: 46px;
}

.pl-46 {
  padding-inset-inline-start: 46px;
}

.pr-46 {
  padding-inset-inline-end: 46px;
}

.pt-47 {
  padding-top: 47px;
}

.pb-47 {
  padding-bottom: 47px;
}

.pl-47 {
  padding-inset-inline-start: 47px;
}

.pr-47 {
  padding-inset-inline-end: 47px;
}

.pt-48 {
  padding-top: 48px;
}

.pb-48 {
  padding-bottom: 48px;
}

.pl-48 {
  padding-inset-inline-start: 48px;
}

.pr-48 {
  padding-inset-inline-end: 48px;
}

.pt-49 {
  padding-top: 49px;
}

.pb-49 {
  padding-bottom: 49px;
}

.pl-49 {
  padding-inset-inline-start: 49px;
}

.pr-49 {
  padding-inset-inline-end: 49px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-inset-inline-start: 50px;
}

.pr-50 {
  padding-inset-inline-end: 50px;
}

.pt-51 {
  padding-top: 51px;
}

.pb-51 {
  padding-bottom: 51px;
}

.pl-51 {
  padding-inset-inline-start: 51px;
}

.pr-51 {
  padding-inset-inline-end: 51px;
}

.pt-52 {
  padding-top: 52px;
}

.pb-52 {
  padding-bottom: 52px;
}

.pl-52 {
  padding-inset-inline-start: 52px;
}

.pr-52 {
  padding-inset-inline-end: 52px;
}

.pt-53 {
  padding-top: 53px;
}

.pb-53 {
  padding-bottom: 53px;
}

.pl-53 {
  padding-inset-inline-start: 53px;
}

.pr-53 {
  padding-inset-inline-end: 53px;
}

.pt-54 {
  padding-top: 54px;
}

.pb-54 {
  padding-bottom: 54px;
}

.pl-54 {
  padding-inset-inline-start: 54px;
}

.pr-54 {
  padding-inset-inline-end: 54px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-inset-inline-start: 55px;
}

.pr-55 {
  padding-inset-inline-end: 55px;
}

.pt-56 {
  padding-top: 56px;
}

.pb-56 {
  padding-bottom: 56px;
}

.pl-56 {
  padding-inset-inline-start: 56px;
}

.pr-56 {
  padding-inset-inline-end: 56px;
}

.pt-57 {
  padding-top: 57px;
}

.pb-57 {
  padding-bottom: 57px;
}

.pl-57 {
  padding-inset-inline-start: 57px;
}

.pr-57 {
  padding-inset-inline-end: 57px;
}

.pt-58 {
  padding-top: 58px;
}

.pb-58 {
  padding-bottom: 58px;
}

.pl-58 {
  padding-inset-inline-start: 58px;
}

.pr-58 {
  padding-inset-inline-end: 58px;
}

.pt-59 {
  padding-top: 59px;
}

.pb-59 {
  padding-bottom: 59px;
}

.pl-59 {
  padding-inset-inline-start: 59px;
}

.pr-59 {
  padding-inset-inline-end: 59px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-inset-inline-start: 60px;
}

.pr-60 {
  padding-inset-inline-end: 60px;
}

.pt-61 {
  padding-top: 61px;
}

.pb-61 {
  padding-bottom: 61px;
}

.pl-61 {
  padding-inset-inline-start: 61px;
}

.pr-61 {
  padding-inset-inline-end: 61px;
}

.pt-62 {
  padding-top: 62px;
}

.pb-62 {
  padding-bottom: 62px;
}

.pl-62 {
  padding-inset-inline-start: 62px;
}

.pr-62 {
  padding-inset-inline-end: 62px;
}

.pt-63 {
  padding-top: 63px;
}

.pb-63 {
  padding-bottom: 63px;
}

.pl-63 {
  padding-inset-inline-start: 63px;
}

.pr-63 {
  padding-inset-inline-end: 63px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.pl-64 {
  padding-inset-inline-start: 64px;
}

.pr-64 {
  padding-inset-inline-end: 64px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-inset-inline-start: 65px;
}

.pr-65 {
  padding-inset-inline-end: 65px;
}

.pt-66 {
  padding-top: 66px;
}

.pb-66 {
  padding-bottom: 66px;
}

.pl-66 {
  padding-inset-inline-start: 66px;
}

.pr-66 {
  padding-inset-inline-end: 66px;
}

.pt-67 {
  padding-top: 67px;
}

.pb-67 {
  padding-bottom: 67px;
}

.pl-67 {
  padding-inset-inline-start: 67px;
}

.pr-67 {
  padding-inset-inline-end: 67px;
}

.pt-68 {
  padding-top: 68px;
}

.pb-68 {
  padding-bottom: 68px;
}

.pl-68 {
  padding-inset-inline-start: 68px;
}

.pr-68 {
  padding-inset-inline-end: 68px;
}

.pt-69 {
  padding-top: 69px;
}

.pb-69 {
  padding-bottom: 69px;
}

.pl-69 {
  padding-inset-inline-start: 69px;
}

.pr-69 {
  padding-inset-inline-end: 69px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-inset-inline-start: 70px;
}

.pr-70 {
  padding-inset-inline-end: 70px;
}

.pt-71 {
  padding-top: 71px;
}

.pb-71 {
  padding-bottom: 71px;
}

.pl-71 {
  padding-inset-inline-start: 71px;
}

.pr-71 {
  padding-inset-inline-end: 71px;
}

.pt-72 {
  padding-top: 72px;
}

.pb-72 {
  padding-bottom: 72px;
}

.pl-72 {
  padding-inset-inline-start: 72px;
}

.pr-72 {
  padding-inset-inline-end: 72px;
}

.pt-73 {
  padding-top: 73px;
}

.pb-73 {
  padding-bottom: 73px;
}

.pl-73 {
  padding-inset-inline-start: 73px;
}

.pr-73 {
  padding-inset-inline-end: 73px;
}

.pt-74 {
  padding-top: 74px;
}

.pb-74 {
  padding-bottom: 74px;
}

.pl-74 {
  padding-inset-inline-start: 74px;
}

.pr-74 {
  padding-inset-inline-end: 74px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-inset-inline-start: 75px;
}

.pr-75 {
  padding-inset-inline-end: 75px;
}

.pt-76 {
  padding-top: 76px;
}

.pb-76 {
  padding-bottom: 76px;
}

.pl-76 {
  padding-inset-inline-start: 76px;
}

.pr-76 {
  padding-inset-inline-end: 76px;
}

.pt-77 {
  padding-top: 77px;
}

.pb-77 {
  padding-bottom: 77px;
}

.pl-77 {
  padding-inset-inline-start: 77px;
}

.pr-77 {
  padding-inset-inline-end: 77px;
}

.pt-78 {
  padding-top: 78px;
}

.pb-78 {
  padding-bottom: 78px;
}

.pl-78 {
  padding-inset-inline-start: 78px;
}

.pr-78 {
  padding-inset-inline-end: 78px;
}

.pt-79 {
  padding-top: 79px;
}

.pb-79 {
  padding-bottom: 79px;
}

.pl-79 {
  padding-inset-inline-start: 79px;
}

.pr-79 {
  padding-inset-inline-end: 79px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-inset-inline-start: 80px;
}

.pr-80 {
  padding-inset-inline-end: 80px;
}

.pt-81 {
  padding-top: 81px;
}

.pb-81 {
  padding-bottom: 81px;
}

.pl-81 {
  padding-inset-inline-start: 81px;
}

.pr-81 {
  padding-inset-inline-end: 81px;
}

.pt-82 {
  padding-top: 82px;
}

.pb-82 {
  padding-bottom: 82px;
}

.pl-82 {
  padding-inset-inline-start: 82px;
}

.pr-82 {
  padding-inset-inline-end: 82px;
}

.pt-83 {
  padding-top: 83px;
}

.pb-83 {
  padding-bottom: 83px;
}

.pl-83 {
  padding-inset-inline-start: 83px;
}

.pr-83 {
  padding-inset-inline-end: 83px;
}

.pt-84 {
  padding-top: 84px;
}

.pb-84 {
  padding-bottom: 84px;
}

.pl-84 {
  padding-inset-inline-start: 84px;
}

.pr-84 {
  padding-inset-inline-end: 84px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-inset-inline-start: 85px;
}

.pr-85 {
  padding-inset-inline-end: 85px;
}

.pt-86 {
  padding-top: 86px;
}

.pb-86 {
  padding-bottom: 86px;
}

.pl-86 {
  padding-inset-inline-start: 86px;
}

.pr-86 {
  padding-inset-inline-end: 86px;
}

.pt-87 {
  padding-top: 87px;
}

.pb-87 {
  padding-bottom: 87px;
}

.pl-87 {
  padding-inset-inline-start: 87px;
}

.pr-87 {
  padding-inset-inline-end: 87px;
}

.pt-88 {
  padding-top: 88px;
}

.pb-88 {
  padding-bottom: 88px;
}

.pl-88 {
  padding-inset-inline-start: 88px;
}

.pr-88 {
  padding-inset-inline-end: 88px;
}

.pt-89 {
  padding-top: 89px;
}

.pb-89 {
  padding-bottom: 89px;
}

.pl-89 {
  padding-inset-inline-start: 89px;
}

.pr-89 {
  padding-inset-inline-end: 89px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-inset-inline-start: 90px;
}

.pr-90 {
  padding-inset-inline-end: 90px;
}

.pt-91 {
  padding-top: 91px;
}

.pb-91 {
  padding-bottom: 91px;
}

.pl-91 {
  padding-inset-inline-start: 91px;
}

.pr-91 {
  padding-inset-inline-end: 91px;
}

.pt-92 {
  padding-top: 92px;
}

.pb-92 {
  padding-bottom: 92px;
}

.pl-92 {
  padding-inset-inline-start: 92px;
}

.pr-92 {
  padding-inset-inline-end: 92px;
}

.pt-93 {
  padding-top: 93px;
}

.pb-93 {
  padding-bottom: 93px;
}

.pl-93 {
  padding-inset-inline-start: 93px;
}

.pr-93 {
  padding-inset-inline-end: 93px;
}

.pt-94 {
  padding-top: 94px;
}

.pb-94 {
  padding-bottom: 94px;
}

.pl-94 {
  padding-inset-inline-start: 94px;
}

.pr-94 {
  padding-inset-inline-end: 94px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-inset-inline-start: 95px;
}

.pr-95 {
  padding-inset-inline-end: 95px;
}

.pt-96 {
  padding-top: 96px;
}

.pb-96 {
  padding-bottom: 96px;
}

.pl-96 {
  padding-inset-inline-start: 96px;
}

.pr-96 {
  padding-inset-inline-end: 96px;
}

.pt-97 {
  padding-top: 97px;
}

.pb-97 {
  padding-bottom: 97px;
}

.pl-97 {
  padding-inset-inline-start: 97px;
}

.pr-97 {
  padding-inset-inline-end: 97px;
}

.pt-98 {
  padding-top: 98px;
}

.pb-98 {
  padding-bottom: 98px;
}

.pl-98 {
  padding-inset-inline-start: 98px;
}

.pr-98 {
  padding-inset-inline-end: 98px;
}

.pt-99 {
  padding-top: 99px;
}

.pb-99 {
  padding-bottom: 99px;
}

.pl-99 {
  padding-inset-inline-start: 99px;
}

.pr-99 {
  padding-inset-inline-end: 99px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-inset-inline-start: 100px;
}

.pr-100 {
  padding-inset-inline-end: 100px;
}

.pt-101 {
  padding-top: 101px;
}

.pb-101 {
  padding-bottom: 101px;
}

.pl-101 {
  padding-inset-inline-start: 101px;
}

.pr-101 {
  padding-inset-inline-end: 101px;
}

.pt-102 {
  padding-top: 102px;
}

.pb-102 {
  padding-bottom: 102px;
}

.pl-102 {
  padding-inset-inline-start: 102px;
}

.pr-102 {
  padding-inset-inline-end: 102px;
}

.pt-103 {
  padding-top: 103px;
}

.pb-103 {
  padding-bottom: 103px;
}

.pl-103 {
  padding-inset-inline-start: 103px;
}

.pr-103 {
  padding-inset-inline-end: 103px;
}

.pt-104 {
  padding-top: 104px;
}

.pb-104 {
  padding-bottom: 104px;
}

.pl-104 {
  padding-inset-inline-start: 104px;
}

.pr-104 {
  padding-inset-inline-end: 104px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-inset-inline-start: 105px;
}

.pr-105 {
  padding-inset-inline-end: 105px;
}

.pt-106 {
  padding-top: 106px;
}

.pb-106 {
  padding-bottom: 106px;
}

.pl-106 {
  padding-inset-inline-start: 106px;
}

.pr-106 {
  padding-inset-inline-end: 106px;
}

.pt-107 {
  padding-top: 107px;
}

.pb-107 {
  padding-bottom: 107px;
}

.pl-107 {
  padding-inset-inline-start: 107px;
}

.pr-107 {
  padding-inset-inline-end: 107px;
}

.pt-108 {
  padding-top: 108px;
}

.pb-108 {
  padding-bottom: 108px;
}

.pl-108 {
  padding-inset-inline-start: 108px;
}

.pr-108 {
  padding-inset-inline-end: 108px;
}

.pt-109 {
  padding-top: 109px;
}

.pb-109 {
  padding-bottom: 109px;
}

.pl-109 {
  padding-inset-inline-start: 109px;
}

.pr-109 {
  padding-inset-inline-end: 109px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-inset-inline-start: 110px;
}

.pr-110 {
  padding-inset-inline-end: 110px;
}

.pt-111 {
  padding-top: 111px;
}

.pb-111 {
  padding-bottom: 111px;
}

.pl-111 {
  padding-inset-inline-start: 111px;
}

.pr-111 {
  padding-inset-inline-end: 111px;
}

.pt-112 {
  padding-top: 112px;
}

.pb-112 {
  padding-bottom: 112px;
}

.pl-112 {
  padding-inset-inline-start: 112px;
}

.pr-112 {
  padding-inset-inline-end: 112px;
}

.pt-113 {
  padding-top: 113px;
}

.pb-113 {
  padding-bottom: 113px;
}

.pl-113 {
  padding-inset-inline-start: 113px;
}

.pr-113 {
  padding-inset-inline-end: 113px;
}

.pt-114 {
  padding-top: 114px;
}

.pb-114 {
  padding-bottom: 114px;
}

.pl-114 {
  padding-inset-inline-start: 114px;
}

.pr-114 {
  padding-inset-inline-end: 114px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-inset-inline-start: 115px;
}

.pr-115 {
  padding-inset-inline-end: 115px;
}

.pt-116 {
  padding-top: 116px;
}

.pb-116 {
  padding-bottom: 116px;
}

.pl-116 {
  padding-inset-inline-start: 116px;
}

.pr-116 {
  padding-inset-inline-end: 116px;
}

.pt-117 {
  padding-top: 117px;
}

.pb-117 {
  padding-bottom: 117px;
}

.pl-117 {
  padding-inset-inline-start: 117px;
}

.pr-117 {
  padding-inset-inline-end: 117px;
}

.pt-118 {
  padding-top: 118px;
}

.pb-118 {
  padding-bottom: 118px;
}

.pl-118 {
  padding-inset-inline-start: 118px;
}

.pr-118 {
  padding-inset-inline-end: 118px;
}

.pt-119 {
  padding-top: 119px;
}

.pb-119 {
  padding-bottom: 119px;
}

.pl-119 {
  padding-inset-inline-start: 119px;
}

.pr-119 {
  padding-inset-inline-end: 119px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-inset-inline-start: 120px;
}

.pr-120 {
  padding-inset-inline-end: 120px;
}

.pt-121 {
  padding-top: 121px;
}

.pb-121 {
  padding-bottom: 121px;
}

.pl-121 {
  padding-inset-inline-start: 121px;
}

.pr-121 {
  padding-inset-inline-end: 121px;
}

.pt-122 {
  padding-top: 122px;
}

.pb-122 {
  padding-bottom: 122px;
}

.pl-122 {
  padding-inset-inline-start: 122px;
}

.pr-122 {
  padding-inset-inline-end: 122px;
}

.pt-123 {
  padding-top: 123px;
}

.pb-123 {
  padding-bottom: 123px;
}

.pl-123 {
  padding-inset-inline-start: 123px;
}

.pr-123 {
  padding-inset-inline-end: 123px;
}

.pt-124 {
  padding-top: 124px;
}

.pb-124 {
  padding-bottom: 124px;
}

.pl-124 {
  padding-inset-inline-start: 124px;
}

.pr-124 {
  padding-inset-inline-end: 124px;
}

.pt-125 {
  padding-top: 125px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pl-125 {
  padding-inset-inline-start: 125px;
}

.pr-125 {
  padding-inset-inline-end: 125px;
}

.pt-126 {
  padding-top: 126px;
}

.pb-126 {
  padding-bottom: 126px;
}

.pl-126 {
  padding-inset-inline-start: 126px;
}

.pr-126 {
  padding-inset-inline-end: 126px;
}

.pt-127 {
  padding-top: 127px;
}

.pb-127 {
  padding-bottom: 127px;
}

.pl-127 {
  padding-inset-inline-start: 127px;
}

.pr-127 {
  padding-inset-inline-end: 127px;
}

.pt-128 {
  padding-top: 128px;
}

.pb-128 {
  padding-bottom: 128px;
}

.pl-128 {
  padding-inset-inline-start: 128px;
}

.pr-128 {
  padding-inset-inline-end: 128px;
}

.pt-129 {
  padding-top: 129px;
}

.pb-129 {
  padding-bottom: 129px;
}

.pl-129 {
  padding-inset-inline-start: 129px;
}

.pr-129 {
  padding-inset-inline-end: 129px;
}

.pt-130 {
  padding-top: 130px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pl-130 {
  padding-inset-inline-start: 130px;
}

.pr-130 {
  padding-inset-inline-end: 130px;
}

.pt-131 {
  padding-top: 131px;
}

.pb-131 {
  padding-bottom: 131px;
}

.pl-131 {
  padding-inset-inline-start: 131px;
}

.pr-131 {
  padding-inset-inline-end: 131px;
}

.pt-132 {
  padding-top: 132px;
}

.pb-132 {
  padding-bottom: 132px;
}

.pl-132 {
  padding-inset-inline-start: 132px;
}

.pr-132 {
  padding-inset-inline-end: 132px;
}

.pt-133 {
  padding-top: 133px;
}

.pb-133 {
  padding-bottom: 133px;
}

.pl-133 {
  padding-inset-inline-start: 133px;
}

.pr-133 {
  padding-inset-inline-end: 133px;
}

.pt-134 {
  padding-top: 134px;
}

.pb-134 {
  padding-bottom: 134px;
}

.pl-134 {
  padding-inset-inline-start: 134px;
}

.pr-134 {
  padding-inset-inline-end: 134px;
}

.pt-135 {
  padding-top: 135px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pl-135 {
  padding-inset-inline-start: 135px;
}

.pr-135 {
  padding-inset-inline-end: 135px;
}

.pt-136 {
  padding-top: 136px;
}

.pb-136 {
  padding-bottom: 136px;
}

.pl-136 {
  padding-inset-inline-start: 136px;
}

.pr-136 {
  padding-inset-inline-end: 136px;
}

.pt-137 {
  padding-top: 137px;
}

.pb-137 {
  padding-bottom: 137px;
}

.pl-137 {
  padding-inset-inline-start: 137px;
}

.pr-137 {
  padding-inset-inline-end: 137px;
}

.pt-138 {
  padding-top: 138px;
}

.pb-138 {
  padding-bottom: 138px;
}

.pl-138 {
  padding-inset-inline-start: 138px;
}

.pr-138 {
  padding-inset-inline-end: 138px;
}

.pt-139 {
  padding-top: 139px;
}

.pb-139 {
  padding-bottom: 139px;
}

.pl-139 {
  padding-inset-inline-start: 139px;
}

.pr-139 {
  padding-inset-inline-end: 139px;
}

.pt-140 {
  padding-top: 140px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pl-140 {
  padding-inset-inline-start: 140px;
}

.pr-140 {
  padding-inset-inline-end: 140px;
}

.pt-141 {
  padding-top: 141px;
}

.pb-141 {
  padding-bottom: 141px;
}

.pl-141 {
  padding-inset-inline-start: 141px;
}

.pr-141 {
  padding-inset-inline-end: 141px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-142 {
  padding-bottom: 142px;
}

.pl-142 {
  padding-inset-inline-start: 142px;
}

.pr-142 {
  padding-inset-inline-end: 142px;
}

.pt-143 {
  padding-top: 143px;
}

.pb-143 {
  padding-bottom: 143px;
}

.pl-143 {
  padding-inset-inline-start: 143px;
}

.pr-143 {
  padding-inset-inline-end: 143px;
}

.pt-144 {
  padding-top: 144px;
}

.pb-144 {
  padding-bottom: 144px;
}

.pl-144 {
  padding-inset-inline-start: 144px;
}

.pr-144 {
  padding-inset-inline-end: 144px;
}

.pt-145 {
  padding-top: 145px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pl-145 {
  padding-inset-inline-start: 145px;
}

.pr-145 {
  padding-inset-inline-end: 145px;
}

.pt-146 {
  padding-top: 146px;
}

.pb-146 {
  padding-bottom: 146px;
}

.pl-146 {
  padding-inset-inline-start: 146px;
}

.pr-146 {
  padding-inset-inline-end: 146px;
}

.pt-147 {
  padding-top: 147px;
}

.pb-147 {
  padding-bottom: 147px;
}

.pl-147 {
  padding-inset-inline-start: 147px;
}

.pr-147 {
  padding-inset-inline-end: 147px;
}

.pt-148 {
  padding-top: 148px;
}

.pb-148 {
  padding-bottom: 148px;
}

.pl-148 {
  padding-inset-inline-start: 148px;
}

.pr-148 {
  padding-inset-inline-end: 148px;
}

.pt-149 {
  padding-top: 149px;
}

.pb-149 {
  padding-bottom: 149px;
}

.pl-149 {
  padding-inset-inline-start: 149px;
}

.pr-149 {
  padding-inset-inline-end: 149px;
}

.pt-150 {
  padding-top: 150px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pl-150 {
  padding-inset-inline-start: 150px;
}

.pr-150 {
  padding-inset-inline-end: 150px;
}

.pt-151 {
  padding-top: 151px;
}

.pb-151 {
  padding-bottom: 151px;
}

.pl-151 {
  padding-inset-inline-start: 151px;
}

.pr-151 {
  padding-inset-inline-end: 151px;
}

.pt-152 {
  padding-top: 152px;
}

.pb-152 {
  padding-bottom: 152px;
}

.pl-152 {
  padding-inset-inline-start: 152px;
}

.pr-152 {
  padding-inset-inline-end: 152px;
}

.pt-153 {
  padding-top: 153px;
}

.pb-153 {
  padding-bottom: 153px;
}

.pl-153 {
  padding-inset-inline-start: 153px;
}

.pr-153 {
  padding-inset-inline-end: 153px;
}

.pt-154 {
  padding-top: 154px;
}

.pb-154 {
  padding-bottom: 154px;
}

.pl-154 {
  padding-inset-inline-start: 154px;
}

.pr-154 {
  padding-inset-inline-end: 154px;
}

.pt-155 {
  padding-top: 155px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pl-155 {
  padding-inset-inline-start: 155px;
}

.pr-155 {
  padding-inset-inline-end: 155px;
}

.pt-156 {
  padding-top: 156px;
}

.pb-156 {
  padding-bottom: 156px;
}

.pl-156 {
  padding-inset-inline-start: 156px;
}

.pr-156 {
  padding-inset-inline-end: 156px;
}

.pt-157 {
  padding-top: 157px;
}

.pb-157 {
  padding-bottom: 157px;
}

.pl-157 {
  padding-inset-inline-start: 157px;
}

.pr-157 {
  padding-inset-inline-end: 157px;
}

.pt-158 {
  padding-top: 158px;
}

.pb-158 {
  padding-bottom: 158px;
}

.pl-158 {
  padding-inset-inline-start: 158px;
}

.pr-158 {
  padding-inset-inline-end: 158px;
}

.pt-159 {
  padding-top: 159px;
}

.pb-159 {
  padding-bottom: 159px;
}

.pl-159 {
  padding-inset-inline-start: 159px;
}

.pr-159 {
  padding-inset-inline-end: 159px;
}

.pt-160 {
  padding-top: 160px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pl-160 {
  padding-inset-inline-start: 160px;
}

.pr-160 {
  padding-inset-inline-end: 160px;
}

.pt-161 {
  padding-top: 161px;
}

.pb-161 {
  padding-bottom: 161px;
}

.pl-161 {
  padding-inset-inline-start: 161px;
}

.pr-161 {
  padding-inset-inline-end: 161px;
}

.pt-162 {
  padding-top: 162px;
}

.pb-162 {
  padding-bottom: 162px;
}

.pl-162 {
  padding-inset-inline-start: 162px;
}

.pr-162 {
  padding-inset-inline-end: 162px;
}

.pt-163 {
  padding-top: 163px;
}

.pb-163 {
  padding-bottom: 163px;
}

.pl-163 {
  padding-inset-inline-start: 163px;
}

.pr-163 {
  padding-inset-inline-end: 163px;
}

.pt-164 {
  padding-top: 164px;
}

.pb-164 {
  padding-bottom: 164px;
}

.pl-164 {
  padding-inset-inline-start: 164px;
}

.pr-164 {
  padding-inset-inline-end: 164px;
}

.pt-165 {
  padding-top: 165px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pl-165 {
  padding-inset-inline-start: 165px;
}

.pr-165 {
  padding-inset-inline-end: 165px;
}

.pt-166 {
  padding-top: 166px;
}

.pb-166 {
  padding-bottom: 166px;
}

.pl-166 {
  padding-inset-inline-start: 166px;
}

.pr-166 {
  padding-inset-inline-end: 166px;
}

.pt-167 {
  padding-top: 167px;
}

.pb-167 {
  padding-bottom: 167px;
}

.pl-167 {
  padding-inset-inline-start: 167px;
}

.pr-167 {
  padding-inset-inline-end: 167px;
}

.pt-168 {
  padding-top: 168px;
}

.pb-168 {
  padding-bottom: 168px;
}

.pl-168 {
  padding-inset-inline-start: 168px;
}

.pr-168 {
  padding-inset-inline-end: 168px;
}

.pt-169 {
  padding-top: 169px;
}

.pb-169 {
  padding-bottom: 169px;
}

.pl-169 {
  padding-inset-inline-start: 169px;
}

.pr-169 {
  padding-inset-inline-end: 169px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pl-170 {
  padding-inset-inline-start: 170px;
}

.pr-170 {
  padding-inset-inline-end: 170px;
}

.pt-171 {
  padding-top: 171px;
}

.pb-171 {
  padding-bottom: 171px;
}

.pl-171 {
  padding-inset-inline-start: 171px;
}

.pr-171 {
  padding-inset-inline-end: 171px;
}

.pt-172 {
  padding-top: 172px;
}

.pb-172 {
  padding-bottom: 172px;
}

.pl-172 {
  padding-inset-inline-start: 172px;
}

.pr-172 {
  padding-inset-inline-end: 172px;
}

.pt-173 {
  padding-top: 173px;
}

.pb-173 {
  padding-bottom: 173px;
}

.pl-173 {
  padding-inset-inline-start: 173px;
}

.pr-173 {
  padding-inset-inline-end: 173px;
}

.pt-174 {
  padding-top: 174px;
}

.pb-174 {
  padding-bottom: 174px;
}

.pl-174 {
  padding-inset-inline-start: 174px;
}

.pr-174 {
  padding-inset-inline-end: 174px;
}

.pt-175 {
  padding-top: 175px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pl-175 {
  padding-inset-inline-start: 175px;
}

.pr-175 {
  padding-inset-inline-end: 175px;
}

.pt-176 {
  padding-top: 176px;
}

.pb-176 {
  padding-bottom: 176px;
}

.pl-176 {
  padding-inset-inline-start: 176px;
}

.pr-176 {
  padding-inset-inline-end: 176px;
}

.pt-177 {
  padding-top: 177px;
}

.pb-177 {
  padding-bottom: 177px;
}

.pl-177 {
  padding-inset-inline-start: 177px;
}

.pr-177 {
  padding-inset-inline-end: 177px;
}

.pt-178 {
  padding-top: 178px;
}

.pb-178 {
  padding-bottom: 178px;
}

.pl-178 {
  padding-inset-inline-start: 178px;
}

.pr-178 {
  padding-inset-inline-end: 178px;
}

.pt-179 {
  padding-top: 179px;
}

.pb-179 {
  padding-bottom: 179px;
}

.pl-179 {
  padding-inset-inline-start: 179px;
}

.pr-179 {
  padding-inset-inline-end: 179px;
}

.pt-180 {
  padding-top: 180px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pl-180 {
  padding-inset-inline-start: 180px;
}

.pr-180 {
  padding-inset-inline-end: 180px;
}

.pt-181 {
  padding-top: 181px;
}

.pb-181 {
  padding-bottom: 181px;
}

.pl-181 {
  padding-inset-inline-start: 181px;
}

.pr-181 {
  padding-inset-inline-end: 181px;
}

.pt-182 {
  padding-top: 182px;
}

.pb-182 {
  padding-bottom: 182px;
}

.pl-182 {
  padding-inset-inline-start: 182px;
}

.pr-182 {
  padding-inset-inline-end: 182px;
}

.pt-183 {
  padding-top: 183px;
}

.pb-183 {
  padding-bottom: 183px;
}

.pl-183 {
  padding-inset-inline-start: 183px;
}

.pr-183 {
  padding-inset-inline-end: 183px;
}

.pt-184 {
  padding-top: 184px;
}

.pb-184 {
  padding-bottom: 184px;
}

.pl-184 {
  padding-inset-inline-start: 184px;
}

.pr-184 {
  padding-inset-inline-end: 184px;
}

.pt-185 {
  padding-top: 185px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pl-185 {
  padding-inset-inline-start: 185px;
}

.pr-185 {
  padding-inset-inline-end: 185px;
}

.pt-186 {
  padding-top: 186px;
}

.pb-186 {
  padding-bottom: 186px;
}

.pl-186 {
  padding-inset-inline-start: 186px;
}

.pr-186 {
  padding-inset-inline-end: 186px;
}

.pt-187 {
  padding-top: 187px;
}

.pb-187 {
  padding-bottom: 187px;
}

.pl-187 {
  padding-inset-inline-start: 187px;
}

.pr-187 {
  padding-inset-inline-end: 187px;
}

.pt-188 {
  padding-top: 188px;
}

.pb-188 {
  padding-bottom: 188px;
}

.pl-188 {
  padding-inset-inline-start: 188px;
}

.pr-188 {
  padding-inset-inline-end: 188px;
}

.pt-189 {
  padding-top: 189px;
}

.pb-189 {
  padding-bottom: 189px;
}

.pl-189 {
  padding-inset-inline-start: 189px;
}

.pr-189 {
  padding-inset-inline-end: 189px;
}

.pt-190 {
  padding-top: 190px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pl-190 {
  padding-inset-inline-start: 190px;
}

.pr-190 {
  padding-inset-inline-end: 190px;
}

.pt-191 {
  padding-top: 191px;
}

.pb-191 {
  padding-bottom: 191px;
}

.pl-191 {
  padding-inset-inline-start: 191px;
}

.pr-191 {
  padding-inset-inline-end: 191px;
}

.pt-192 {
  padding-top: 192px;
}

.pb-192 {
  padding-bottom: 192px;
}

.pl-192 {
  padding-inset-inline-start: 192px;
}

.pr-192 {
  padding-inset-inline-end: 192px;
}

.pt-193 {
  padding-top: 193px;
}

.pb-193 {
  padding-bottom: 193px;
}

.pl-193 {
  padding-inset-inline-start: 193px;
}

.pr-193 {
  padding-inset-inline-end: 193px;
}

.pt-194 {
  padding-top: 194px;
}

.pb-194 {
  padding-bottom: 194px;
}

.pl-194 {
  padding-inset-inline-start: 194px;
}

.pr-194 {
  padding-inset-inline-end: 194px;
}

.pt-195 {
  padding-top: 195px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pl-195 {
  padding-inset-inline-start: 195px;
}

.pr-195 {
  padding-inset-inline-end: 195px;
}

.pt-196 {
  padding-top: 196px;
}

.pb-196 {
  padding-bottom: 196px;
}

.pl-196 {
  padding-inset-inline-start: 196px;
}

.pr-196 {
  padding-inset-inline-end: 196px;
}

.pt-197 {
  padding-top: 197px;
}

.pb-197 {
  padding-bottom: 197px;
}

.pl-197 {
  padding-inset-inline-start: 197px;
}

.pr-197 {
  padding-inset-inline-end: 197px;
}

.pt-198 {
  padding-top: 198px;
}

.pb-198 {
  padding-bottom: 198px;
}

.pl-198 {
  padding-inset-inline-start: 198px;
}

.pr-198 {
  padding-inset-inline-end: 198px;
}

.pt-199 {
  padding-top: 199px;
}

.pb-199 {
  padding-bottom: 199px;
}

.pl-199 {
  padding-inset-inline-start: 199px;
}

.pr-199 {
  padding-inset-inline-end: 199px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-200 {
  padding-inset-inline-start: 200px;
}

.pr-200 {
  padding-inset-inline-end: 200px;
}

:root {
  --td-body-font:"DM Sans", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Free";
  --td-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --td-heading: #303030;
  --td-primary: #174AFF;
  --td-secondary:#FF6229;
  --td-text-primary: rgba(48, 48, 48, 0.80);
  --td-text-secondary: rgba(48, 48, 48, 0.60);
  --td-bg-1: #EFFDFF;
  --td-bg-2: rgba(0, 0, 0, 0.04);
  --td-border-1: rgba(48, 48, 48, 0.16);
}

/*---------------------------------
/*  1.2 spacing
---------------------------------*/
.section_space-py {
  padding-block-start: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
  padding-block-end: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.section_space-my {
  margin-block-start: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
  margin-block-end: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.section_space-mT {
  margin-block-start: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.section_space-mB {
  margin-block-end: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.section_space-pT {
  padding-block-start: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.section_space-pB {
  padding-block-end: clamp(1.5rem, 6.5vw + 1rem, 4.375rem);
}

.title_mt {
  margin-block-start: clamp(1rem, 6.5vw, 2rem);
}

.title_mb {
  margin-block-end: clamp(1rem, 6.5vw, 2rem);
}

/*----------------------------------------*/
/*   1.3 typography
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--td-body-font);
  font-size: 14px;
  font-weight: normal;
  line-height: 1.5;
  color: var(--td-text-primary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--td-body-font);
  color: var(--td-heading);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
}

h1,
.h1 {
  font-size: clamp(1.5rem, 2vw + 1rem, 3.75rem);
  line-height: 1.2;
  font-weight: 500;
}

h2,
.h2 {
  font-size: clamp(1.25rem, 0.75vw + 1rem, 48px);
  line-height: 1.3;
  font-weight: 500;
}

h3,
.h3 {
  font-size: clamp(1.125rem, 1vw + 1rem, 1.875rem);
  line-height: 1.4;
  font-weight: 500;
}

h4,
.h4 {
  font-size: clamp(1.25rem, 1vw + 1rem, 1.5rem);
  line-height: 1.5;
  font-weight: 500;
}

h5,
.h5 {
  font-size: clamp(1rem, 0.5vw + 1rem, 1.125rem);
  line-height: 1.6;
  font-weight: 500;
}

h6,
.h6 {
  font-size: clamp(0.875rem, 0.4vw + 1rem, 1rem);
  line-height: 1.6;
  font-weight: 500;
}

ul {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

p {
  font-size: clamp(0.75rem, 0.4vw + 0.875rem, 0.875rem);
  line-height: 1.625;
  color: var(--td-text-primary);
  margin-bottom: 0;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

.o-x-clip {
  overflow-x: clip;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button {
  font-family: var(--td-ff-body) !important;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
  font-family: var(--td-ff-body);
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: var(--td-card-bg-1);
  opacity: 1;
  border-width: 1px;
}

*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.z-index-1 {
  z-index: 1;
}

.z-index-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1345px;
  }
}
/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 4000px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 20px;
  font-weight: 900;
  inset-inline-end: -5px;
  margin-top: -16px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

.home-2 {
  background-color: var(--td-bg);
}

/*----------------------------------------*/
/*  buttons
/*----------------------------------------*/
.primary-button {
  display: inline-flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-secondary);
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.primary-button:hover {
  background: #DA4F1D;
  color: var(--td-white);
}
.primary-button.xl-btn {
  height: 52px;
  padding: 10px 32px;
  font-size: 16px;
}
@media (max-width: 767px) {
  .primary-button.xl-btn {
    height: 40px;
    padding: 10px 24px;
    font-size: 12px;
  }
}
.primary-button.sm-btn {
  height: 32px;
  padding: 12px 18px;
  font-size: 14px;
}
.primary-button.md-btn {
  height: 36;
  padding: 10px 20px;
  font-size: 14px;
}
.primary-button.reject-btn {
  background-color: #DC3545;
  color: var(--td-white);
}
.primary-button.border-btn {
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: transparent;
  color: var(--td-secondary);
}
.primary-button.border-btn:hover {
  background: var(--td-secondary);
  color: var(--td-white);
}
.primary-button.border-btn-secondary {
  border-radius: 12px;
  border: 1px solid var(--td-secondary);
  background: transparent;
  color: var(--td-secondary);
}
.primary-button.border-btn-secondary:hover {
  background: var(--td-secondary);
  color: var(--td-white);
}
.primary-button.border-btn-2 {
  border-radius: 12px;
  border: 2px solid var(--td-secondary);
  background: transparent;
  color: var(--td-secondary);
}
.primary-button.border-btn-2:hover {
  background: var(--td-secondary);
  color: var(--td-white);
}

.secondary-button {
  display: flex;
  height: 40px;
  padding: 10px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  background: var(--td-white);
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
}
.secondary-button:hover {
  background: var(--td-white);
  color: var(--td-heading);
}
.secondary-button.xl-btn {
  height: 52px;
  padding: 10px 32px;
  font-size: 16px;
}
@media (max-width: 767px) {
  .secondary-button.xl-btn {
    height: 40px;
    padding: 10px 24px;
    font-size: 12px;
  }
}

.view-all {
  color: var(--td-heading);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.0714285714;
  transition: all 0.3s ease-in-out;
}
.view-all:hover {
  color: var(--td-secondary);
}

.tag-btn {
  display: inline-flex;
  height: 27px;
  padding: 6px 8px;
  align-items: center;
  gap: 2px;
  border-radius: 5px;
  background: rgba(255, 98, 41, 0.1);
  color: rgba(48, 48, 48, 0.8);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
}
.tag-btn span {
  display: flex;
  font-size: 14px;
  color: var(--td-secondary);
}

.border-button {
  display: inline-flex;
  height: 30px;
  padding: 12px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid var(--td-secondary);
  background: rgba(255, 98, 41, 0.04);
  color: rgba(48, 48, 48, 0.8);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.border-button span {
  display: inline-flex;
  align-items: center;
}
.border-button span .chat-icon {
  font-size: 16px;
  color: #6F5BFE;
}
.border-button:hover {
  background: var(--td-secondary);
  color: var(--td-white);
}
.border-button.xl-btn {
  height: 32px;
  border-radius: 11px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background-color: transparent;
  transition: all 0.3s ease-in-out;
}
.border-button.xl-btn:hover {
  background-color: var(--td-secondary);
  border: 1px solid var(--td-secondary);
}

/*----------------------------------------*/
/*  Cards
/*----------------------------------------*/
.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 10px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .category-card {
    padding: 16px 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .category-card {
    padding: 20px 10px;
  }
}
.category-card:hover {
  background: #FFEFEA;
}
.category-card .icon {
  width: 66px;
  height: 66px;
  flex-shrink: 0;
  margin-bottom: 24px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .category-card .icon {
    width: 55px;
    height: 55px;
  }
}
@media (max-width: 767px) {
  .category-card .icon {
    width: 45px;
    height: 45px;
    margin-bottom: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .category-card .icon {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }
}
.category-card .icon img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.category-card .text h5 {
  color: var(--td-heading);
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 3px;
}
.category-card .text p {
  color: var(--td-secondary);
  text-align: center;
  font-size: 13px;
  font-weight: 600;
  line-height: normal;
}

.games-card {
  padding: 4px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;
  transition: all 0.3s ease-in-out;
  background: #FFF;
}
.games-card:hover {
  border: 1px solid rgba(48, 48, 48, 0.16);
  box-shadow: 4px 4px 40px 0px rgba(62, 54, 131, 0.1);
}
.games-card .game-image {
  display: block;
  width: 100%;
  height: 180px;
  border-radius: 12px;
  overflow: hidden;
}
.games-card .game-image img {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
  overflow: hidden;
}
.games-card .game-content {
  padding: 12px;
}
.games-card .game-content .category-and-trending {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.games-card .game-content .category-and-trending h6 {
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
}
.games-card .game-content .category-and-trending .is-trending {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
}
.games-card .game-content .category-and-trending .is-trending img {
  width: 11px;
  height: 11px;
}
.games-card .game-content .title-and-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}
.games-card .game-content .title-and-price .title {
  display: block;
}
.games-card .game-content .title-and-price .title h3 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: 1.375;
  margin-bottom: 3px;
  transition: all 0.3s ease-in-out;
}
.games-card .game-content .title-and-price .title h3:hover {
  color: var(--td-secondary);
}
.games-card .game-content .title-and-price .title p {
  color: var(--td-text-primary);
  font-size: 13px;
  font-weight: 600;
  line-height: 1.6923076923;
}
.games-card .game-content .title-and-price .price {
  width: 30%;
}
.games-card .game-content .title-and-price .price h6 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2222222222;
  text-align: right;
}

.seller-card {
  border-radius: 14px;
  background: #FFF;
  padding: 10px;
}
.seller-card .seller-image {
  width: 100%;
  height: 123px;
}
.seller-card .seller-image img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: cover;
}
.seller-card .seller-content .top {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-top: 16px;
}
.seller-card .seller-content .top .left h5 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: 1.25;
}
.seller-card .seller-content .top .left p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4285714286;
}
.seller-card .seller-content .top .right p {
  color: var(--td-secondary);
  font-size: 14px;
  font-weight: 500;
  line-height: 20, 14;
}
.seller-card .seller-content .bottom {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-top: 10px;
}
.seller-card .seller-content .bottom .left p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.1428571429;
}
.seller-card .seller-content .bottom .right .success-rate {
  display: flex;
  width: 45px;
  height: 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 24px;
  background: #198754;
  color: #FFF;
  font-size: 12px;
  font-weight: 600;
  line-height: normal;
}
.seller-card .seller-content .bottom .right .success-rate.success {
  background: #198754;
}
.seller-card .seller-content .bottom .right .success-rate.error {
  background: #DC3545;
}
.seller-card .seller-content .bottom .right .success-rate.warning {
  background: #FF8D29;
}
.seller-card-2 {
  border: 1px solid rgba(48, 48, 48, 0.16);
}
@media (max-width: 767px) {
  .seller-card-2 .seller-image {
    width: 100%;
    height: 200px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .seller-card-2 .seller-image {
    width: 100%;
    height: 123px;
  }
}

.blog-card {
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.blog-card .full-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.blog-card .full-card .blog-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
}
.blog-card .full-card .blog-image img {
  width: 100%;
  height: 100%;
  border-radius: 16px 16px 0 0;
  object-fit: cover;
  overflow: hidden;
}
.blog-card .full-card .blog-image:hover img {
  transform: scale(1.1);
}
.blog-card .full-card .blog-content {
  border: 1px solid var(--td-border-1);
  border-top: none;
  padding: 16px;
  border-radius: 0 0 16px 16px;
  flex: 1;
}
.blog-card .full-card .blog-content .date {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 10px;
}
.blog-card .full-card .blog-content .date .icon .date-icon {
  font-size: 18px;
  color: var(--td-text-primary);
}
.blog-card .full-card .blog-content .date p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.blog-card .full-card .blog-content h3 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.blog-card .full-card .blog-content h3:hover {
  color: var(--td-secondary);
}
.blog-card .full-card .blog-content p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
}
.blog-card .full-card .blog-content .action-btn {
  margin-top: 20px;
}

.blog-horizontal-card {
  padding: 8px;
  border-radius: 14px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
.blog-horizontal-card .blog-img {
  display: block;
  width: 100%;
  height: 102px;
}
@media (max-width: 767px) {
  .blog-horizontal-card .blog-img {
    height: 202px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-horizontal-card .blog-img {
    height: 102px;
  }
}
.blog-horizontal-card .blog-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}
.blog-horizontal-card .blog-content .date {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 10px;
}
.blog-horizontal-card .blog-content .date .icon .date-icon {
  font-size: 18px;
  color: var(--td-text-primary);
}
.blog-horizontal-card .blog-content .date .icon p {
  color: rgba(48, 48, 48, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: normal;
}
.blog-horizontal-card .blog-content h3 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.blog-horizontal-card .blog-content h3:hover {
  color: var(--td-secondary);
}

.product-gallery .main-slider .slick-slide {
  width: 100%;
  height: 380px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  padding: 10px;
}
@media (max-width: 767px) {
  .product-gallery .main-slider .slick-slide {
    height: 280px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-gallery .main-slider .slick-slide {
    height: 380px;
  }
}
.product-gallery .main-slider .slick-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}
.product-gallery .nav-slider {
  margin-top: 16px;
}
.product-gallery .nav-slider .slick-slide {
  width: 100px;
  margin-right: 10px;
  height: 68px;
  cursor: pointer;
}
.product-gallery .nav-slider .slick-slide.slick-current {
  border: 1px solid rgba(255, 98, 41, 0.6);
  border-radius: 6px;
}
.product-gallery .nav-slider .slick-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.package-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  border-radius: 19px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
@media (max-width: 767px) {
  .package-cards {
    grid-template-columns: repeat(1, 1fr);
  }
}
.package-cards .package-card {
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  padding: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .package-cards .package-card {
    padding: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card {
    padding: 20px;
  }
}
@media (max-width: 767px) {
  .package-cards .package-card {
    border-right: none;
    border-bottom: 1px solid rgba(48, 48, 48, 0.16);
  }
}
.package-cards .package-card:last-child {
  border-right: none;
}
@media (max-width: 767px) {
  .package-cards .package-card:last-child {
    border-bottom: none;
  }
}
.package-cards .package-card .package-icon {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  margin-bottom: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card .package-icon {
    width: 45px;
    height: 45px;
  }
}
.package-cards .package-card .package-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.package-cards .package-card h6 {
  color: var(--td-heading);
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card h6 {
    font-size: 18px;
    margin-bottom: 20px;
  }
}
.package-cards .package-card .price h2 {
  color: var(--td-heading);
  text-align: center;
  font-size: 40px;
  font-weight: 700;
  line-height: normal;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card .price h2 {
    font-size: 26px;
  }
}
.package-cards .package-card .price h2 sup {
  color: var(--td-text-primary);
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  top: -1.2em;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card .price h2 sup {
    top: -0.5em;
  }
}
.package-cards .package-card .price p {
  color: rgba(48, 48, 48, 0.5);
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.package-cards .package-card ul {
  margin-top: 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card ul {
    margin-top: 30px;
  }
}
.package-cards .package-card ul li {
  display: flex;
  align-items: start;
  gap: 10px;
  margin-bottom: 19px;
}
.package-cards .package-card ul li span {
  display: inline-flex;
}
.package-cards .package-card ul li span .double-check-mark {
  font-size: 20px;
  color: var(--td-secondary);
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card ul li span .double-check-mark {
    font-size: 16px;
  }
}
.package-cards .package-card .action-btn {
  margin-top: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .package-cards .package-card .action-btn {
    margin-top: 30px;
  }
}

.info-card {
  padding: 45px 30px;
  border-radius: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  height: 100%;
}
.info-card.one {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 98, 41, 0.05) 50.71%, rgba(255, 255, 255, 0.2) 100%);
}
.info-card.two {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(26, 155, 122, 0.04) 50.71%, rgba(255, 255, 255, 0.2) 100%);
}
.info-card.three {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 169, 0, 0.04) 50.71%, rgba(255, 255, 255, 0.2) 100%);
}
.info-card .info-card-icon {
  display: flex;
  margin-bottom: 10px;
}
.info-card .info-card-icon .info-email-icon {
  font-size: 40px;
  color: var(--td-secondary);
}
.info-card .info-card-icon .info-phone-icon {
  font-size: 40px;
  color: #1A9B7A;
}
.info-card .info-card-icon .info-location-icon {
  font-size: 40px;
  color: #FFA900;
}
.info-card p {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}

/*----------------------------------------*/
/*  2.6 search
/*----------------------------------------*/
.search {
  position: relative;
  display: block;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .search {
    display: none;
  }
}
.search input[type=text],
.search input[type=search] {
  outline: none;
  height: 44px;
  width: 500px;
  padding: 6px 185px 6px 18px;
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.2);
  color: var(--td-heading);
  transition: border-radius 0.3s ease;
}
.search input[type=text]::placeholder,
.search input[type=search]::placeholder {
  color: var(--td-text-secondary);
}
.search input[type=text]:focus,
.search input[type=search]:focus {
  border: 1px solid var(--td-secondary);
}
[dir=rtl] .search input[type=text],
[dir=rtl] .search input[type=search] {
  padding: 6px 18px 6px 200px;
}
.search input[type=text].open,
.search input[type=search].open {
  border-radius: 10px 10px 0 0;
}
.search .search-dropdown {
  position: absolute;
  top: 5px;
  inset-inline-end: 10.5%;
  display: flex;
  height: 34px;
  padding: 0px 16px;
  align-items: center;
  gap: 7px;
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 600;
  line-height: normal;
  border-radius: 8px;
  border: 1px solid var(--td-secondary);
  cursor: pointer;
  transition: transform 0.3s ease;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .search .search-dropdown {
    inset-inline-end: 16%;
  }
}
.search .search-dropdown svg {
  transition: transform 0.3s ease;
}
.search .search-dropdown svg path {
  stroke: var(--td-heading);
}
.search .search-dropdown.open .arrow svg {
  transform: rotate(180deg);
}
.search .search-dropdown-2 {
  background-color: var(--td-primary);
  color: var(--td-white);
}
.search .search-dropdown-2 svg path {
  stroke: var(--td-white);
}
.search .search-suggestion-box,
.search .search-dropdown-lists {
  position: absolute;
  top: 44px;
  inset-inline-start: 0;
  width: 100%;
  padding: 20px;
  border-radius: 0px 0px 10px 10px;
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
  border-left: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 10;
}
.search .search-suggestion-box.open,
.search .search-dropdown-lists.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.search .search-button {
  position: absolute;
  top: 5px;
  inset-inline-end: 1%;
  display: flex;
  height: 34px;
  padding: 10px 13px;
  justify-content: center;
  align-items: center;
  gap: 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 98, 41, 0.6);
  background: var(--td-secondary);
}
.search .search-button svg path {
  stroke: var(--td-white);
}
.search .search-suggestion-box .recently-search h6 {
  color: var(--td-text-primary);
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.3333333333;
  margin-bottom: 8px;
}
.search .search-suggestion-box .recently-search .recently-search-buttons {
  display: flex;
  flex-wrap: wrap;
}
.search .search-suggestion-box .recently-search .recently-search-buttons .suggestion-button {
  display: inline-flex;
  padding: 4px 8px;
  align-items: center;
  gap: 18px;
  border-radius: 6px;
  border: 1px solid var(--td-card-bg-1);
  background: var(--td-card-bg-1);
  margin: 8px 8px 0 0;
  color: var(--td-white);
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.3333333333;
  cursor: pointer;
}
.search .search-suggestion-box .top-search h6 {
  color: var(--td-heading);
  font-size: 15px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 8px;
}
.search .search-suggestion-box .top-search .top-search-buttons {
  display: flex;
  flex-wrap: wrap;
}
.search .search-suggestion-box .top-search .top-search-buttons .top-search-button {
  display: inline-flex;
  height: 28px;
  padding: 6px 12px;
  align-items: center;
  gap: 10px;
  color: var(--td-heading);
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.16);
  margin: 8px 8px 0 0;
}
.search .search-dropdown-lists .category-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .search .search-dropdown-lists .category-buttons {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 767px) {
  .search .search-dropdown-lists .category-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .search .search-dropdown-lists .category-buttons {
    grid-template-columns: repeat(3, 1fr);
  }
}
.search .search-dropdown-lists .category-buttons .category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 6px;
  flex-shrink: 0;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0);
  background: rgba(255, 255, 255, 0);
  transition: all 0.3s ease-in-out;
}
.search .search-dropdown-lists .category-buttons .category-btn:hover, .search .search-dropdown-lists .category-buttons .category-btn.active {
  background: rgba(255, 98, 41, 0.1);
}
.search .search-dropdown-lists .category-buttons .category-btn .img {
  display: inline-flex;
  width: 36px;
  height: 36px;
  padding: 7px;
  border-radius: 8px;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: rgb(48, 146, 235);
}
.search .search-dropdown-lists .category-buttons .category-btn .img img {
  width: 22px;
  height: 22px;
}
.search .search-dropdown-lists .category-buttons .category-btn p {
  color: var(--td-heading);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4285714286;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(1) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(2) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(3) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(4) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(5) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(6) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(7) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(8) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(9) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(10) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(11) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(12) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(13) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(14) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(15) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(16) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(17) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(18) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(19) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(20) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(21) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(22) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(23) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(24) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(25) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(26) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(27) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(28) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(29) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(30) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(31) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(32) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(33) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(34) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(35) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(36) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(37) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(38) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(39) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(40) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(41) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(42) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(43) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(44) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(45) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(46) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(47) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(48) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(49) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(50) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(51) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(52) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(53) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(54) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(55) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(56) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(57) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(58) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(59) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(60) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(61) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(62) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(63) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(64) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(65) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(66) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(67) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(68) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(69) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(70) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(71) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(72) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(73) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(74) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(75) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(76) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(77) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(78) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(79) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(80) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(81) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(82) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(83) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(84) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(85) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(86) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(87) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(88) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(89) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(90) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(91) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(92) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(93) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(94) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(95) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(96) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(97) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(98) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(99) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(100) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(101) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(102) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(103) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(104) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(105) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(106) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(107) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(108) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(109) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(110) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(111) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(112) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(113) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(114) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(115) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(116) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(117) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(118) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(119) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(120) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(121) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(122) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(123) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(124) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(125) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(126) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(127) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(128) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(129) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(130) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(131) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(132) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(133) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(134) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(135) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(136) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(137) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(138) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(139) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(140) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(141) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(142) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(143) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(144) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(145) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(146) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(147) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(148) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(149) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(150) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(151) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(152) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(153) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(154) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(155) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(156) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(157) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(158) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(159) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(160) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(161) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(162) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(163) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(164) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(165) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(166) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(167) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(168) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(169) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(170) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(171) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(172) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(173) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(174) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(175) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(176) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(177) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(178) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(179) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(180) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(181) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(182) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(183) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(184) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(185) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(186) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(187) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(188) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(189) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(190) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(191) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(192) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(193) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(194) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(195) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(196) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(197) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(198) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(199) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(200) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(201) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(202) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(203) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(204) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(205) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(206) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(207) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(208) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(209) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(210) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(211) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(212) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(213) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(214) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(215) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(216) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(217) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(218) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(219) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(220) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(221) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(222) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(223) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(224) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(225) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(226) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(227) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(228) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(229) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(230) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(231) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(232) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(233) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(234) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(235) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(236) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(237) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(238) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(239) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(240) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(241) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(242) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(243) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(244) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(245) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(246) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(247) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(248) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(249) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(250) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(251) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(252) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(253) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(254) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(255) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(256) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(257) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(258) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(259) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(260) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(261) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(262) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(263) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(264) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(265) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(266) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(267) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(268) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(269) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(270) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(271) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(272) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(273) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(274) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(275) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(276) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(277) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(278) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(279) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(280) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(281) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(282) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(283) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(284) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(285) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(286) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(287) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(288) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(289) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(290) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(291) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(292) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(293) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(294) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(295) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(296) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(297) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(298) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(299) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(300) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(301) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(302) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(303) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(304) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(305) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(306) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(307) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(308) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(309) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(310) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(311) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(312) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(313) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(314) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(315) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(316) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(317) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(318) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(319) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(320) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(321) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(322) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(323) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(324) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(325) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(326) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(327) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(328) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(329) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(330) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(331) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(332) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(333) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(334) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(335) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(336) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(337) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(338) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(339) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(340) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(341) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(342) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(343) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(344) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(345) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(346) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(347) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(348) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(349) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(350) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(351) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(352) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(353) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(354) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(355) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(356) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(357) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(358) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(359) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(360) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(361) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(362) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(363) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(364) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(365) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(366) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(367) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(368) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(369) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(370) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(371) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(372) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(373) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(374) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(375) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(376) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(377) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(378) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(379) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(380) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(381) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(382) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(383) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(384) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(385) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(386) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(387) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(388) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(389) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(390) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(391) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(392) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(393) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(394) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(395) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(396) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(397) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(398) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(399) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(400) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(401) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(402) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(403) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(404) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(405) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(406) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(407) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(408) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(409) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(410) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(411) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(412) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(413) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(414) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(415) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(416) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(417) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(418) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(419) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(420) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(421) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(422) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(423) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(424) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(425) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(426) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(427) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(428) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(429) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(430) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(431) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(432) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(433) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(434) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(435) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(436) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(437) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(438) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(439) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(440) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(441) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(442) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(443) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(444) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(445) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(446) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(447) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(448) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(449) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(450) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(451) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(452) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(453) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(454) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(455) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(456) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(457) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(458) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(459) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(460) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(461) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(462) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(463) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(464) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(465) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(466) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(467) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(468) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(469) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(470) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(471) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(472) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(473) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(474) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(475) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(476) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(477) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(478) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(479) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(480) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(481) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(482) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(483) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(484) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(485) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(486) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(487) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(488) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(489) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(490) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(491) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(492) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(493) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(494) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(495) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(496) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(497) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(498) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(499) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(500) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(501) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(502) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(503) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(504) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(505) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(506) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(507) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(508) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(509) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(510) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(511) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(512) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(513) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(514) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(515) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(516) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(517) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(518) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(519) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(520) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(521) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(522) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(523) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(524) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(525) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(526) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(527) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(528) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(529) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(530) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(531) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(532) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(533) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(534) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(535) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(536) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(537) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(538) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(539) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(540) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(541) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(542) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(543) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(544) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(545) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(546) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(547) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(548) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(549) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(550) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(551) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(552) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(553) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(554) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(555) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(556) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(557) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(558) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(559) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(560) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(561) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(562) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(563) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(564) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(565) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(566) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(567) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(568) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(569) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(570) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(571) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(572) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(573) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(574) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(575) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(576) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(577) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(578) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(579) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(580) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(581) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(582) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(583) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(584) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(585) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(586) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(587) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(588) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(589) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(590) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(591) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(592) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(593) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(594) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(595) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(596) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(597) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(598) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(599) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(600) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(601) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(602) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(603) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(604) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(605) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(606) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(607) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(608) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(609) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(610) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(611) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(612) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(613) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(614) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(615) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(616) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(617) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(618) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(619) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(620) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(621) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(622) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(623) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(624) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(625) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(626) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(627) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(628) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(629) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(630) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(631) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(632) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(633) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(634) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(635) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(636) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(637) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(638) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(639) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(640) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(641) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(642) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(643) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(644) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(645) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(646) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(647) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(648) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(649) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(650) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(651) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(652) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(653) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(654) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(655) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(656) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(657) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(658) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(659) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(660) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(661) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(662) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(663) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(664) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(665) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(666) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(667) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(668) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(669) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(670) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(671) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(672) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(673) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(674) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(675) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(676) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(677) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(678) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(679) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(680) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(681) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(682) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(683) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(684) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(685) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(686) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(687) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(688) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(689) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(690) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(691) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(692) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(693) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(694) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(695) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(696) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(697) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(698) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(699) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(700) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(701) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(702) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(703) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(704) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(705) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(706) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(707) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(708) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(709) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(710) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(711) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(712) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(713) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(714) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(715) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(716) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(717) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(718) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(719) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(720) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(721) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(722) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(723) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(724) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(725) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(726) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(727) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(728) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(729) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(730) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(731) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(732) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(733) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(734) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(735) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(736) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(737) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(738) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(739) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(740) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(741) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(742) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(743) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(744) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(745) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(746) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(747) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(748) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(749) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(750) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(751) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(752) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(753) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(754) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(755) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(756) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(757) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(758) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(759) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(760) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(761) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(762) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(763) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(764) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(765) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(766) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(767) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(768) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(769) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(770) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(771) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(772) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(773) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(774) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(775) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(776) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(777) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(778) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(779) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(780) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(781) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(782) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(783) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(784) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(785) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(786) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(787) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(788) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(789) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(790) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(791) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(792) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(793) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(794) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(795) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(796) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(797) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(798) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(799) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(800) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(801) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(802) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(803) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(804) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(805) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(806) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(807) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(808) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(809) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(810) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(811) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(812) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(813) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(814) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(815) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(816) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(817) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(818) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(819) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(820) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(821) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(822) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(823) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(824) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(825) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(826) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(827) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(828) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(829) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(830) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(831) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(832) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(833) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(834) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(835) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(836) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(837) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(838) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(839) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(840) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(841) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(842) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(843) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(844) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(845) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(846) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(847) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(848) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(849) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(850) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(851) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(852) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(853) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(854) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(855) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(856) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(857) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(858) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(859) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(860) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(861) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(862) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(863) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(864) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(865) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(866) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(867) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(868) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(869) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(870) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(871) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(872) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(873) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(874) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(875) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(876) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(877) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(878) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(879) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(880) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(881) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(882) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(883) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(884) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(885) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(886) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(887) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(888) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(889) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(890) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(891) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(892) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(893) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(894) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(895) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(896) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(897) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(898) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(899) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(900) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(901) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(902) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(903) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(904) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(905) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(906) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(907) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(908) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(909) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(910) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(911) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(912) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(913) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(914) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(915) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(916) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(917) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(918) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(919) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(920) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(921) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(922) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(923) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(924) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(925) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(926) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(927) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(928) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(929) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(930) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(931) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(932) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(933) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(934) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(935) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(936) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(937) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(938) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(939) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(940) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(941) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(942) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(943) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(944) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(945) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(946) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(947) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(948) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(949) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(950) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(951) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(952) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(953) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(954) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(955) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(956) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(957) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(958) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(959) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(960) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(961) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(962) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(963) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(964) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(965) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(966) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(967) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(968) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(969) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(970) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(971) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(972) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(973) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(974) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(975) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(976) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(977) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(978) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(979) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(980) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(981) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(982) .img {
  background-color: #665773;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(983) .img {
  background-color: #31AEB2;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(984) .img {
  background-color: #FF6229;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(985) .img {
  background-color: #932FBA;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(986) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(987) .img {
  background-color: #26B4FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(988) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(989) .img {
  background-color: #9C9538;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(990) .img {
  background-color: #30CAAE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(991) .img {
  background-color: #1283B4;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(992) .img {
  background-color: #6456FE;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(993) .img {
  background-color: #FB2D26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(994) .img {
  background-color: #5B2C8E;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(995) .img {
  background-color: #FBAD26;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(996) .img {
  background-color: #E830CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(997) .img {
  background-color: #2674FB;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(998) .img {
  background-color: #19C8CF;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(999) .img {
  background-color: #31B269;
}
.search .search-dropdown-lists .category-buttons .category-btn:nth-child(1000) .img {
  background-color: #665773;
}
.search-modal {
  display: none;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .search-modal {
    display: block;
  }
}
.search-modal input[type=text],
.search-modal input[type=search] {
  width: 100%;
  padding: 6px 18px 6px 18px;
}
@media (max-width: 767px) {
  .search-modal input[type=text],
  .search-modal input[type=search] {
    height: 42px;
  }
}
.search-modal input[type=text].open,
.search-modal input[type=search].open {
  border-radius: 14px 14px 14px 14px;
}
.search-modal .search-suggestion-box {
  border-radius: 14px 14px 14px 14px;
}
@media (max-width: 767px) {
  .search-modal .search-suggestion-box {
    top: 42px;
  }
}
.search-modal .search-dropdown {
  position: unset;
  width: 100%;
  justify-content: space-between;
  margin-top: 16px;
  height: 50px;
  background: var(--td-secondary);
  color: var(--td-white);
}
@media (max-width: 767px) {
  .search-modal .search-dropdown {
    height: 42px;
  }
}
.search-modal .search-dropdown svg path {
  stroke: var(--td-white);
}
.search-modal .search-dropdown-lists {
  top: 118px;
  border-radius: 14px 14px 14px 14px;
  height: 300px;
  overflow-y: auto;
  padding: 12px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .search-modal .search-dropdown-lists {
    height: 192px;
  }
}
@media (max-width: 767px) {
  .search-modal .search-dropdown-lists {
    height: 295px;
    top: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .search-modal .search-dropdown-lists {
    height: 250px;
    top: 100px;
  }
}
.search-modal .search-button {
  position: unset;
  display: flex;
  height: 50px;
  width: 100%;
  margin-top: 16px;
  gap: 5px;
}
@media (max-width: 767px) {
  .search-modal .search-button {
    height: 42px;
  }
}
.search-modal .search-button SVG path {
  stroke: var(--td-heading);
}
@media (max-width: 767px) {
  .search-modal .search-dropdown-lists .category-buttons .category-btn {
    gap: 10px;
    padding: 5px 12px 5px 0px;
  }
  .search-modal .search-dropdown-lists .category-buttons .category-btn .img {
    height: 40px;
    width: 40px;
  }
}

.mobile-search-button {
  width: 40px;
  height: 40px;
  gap: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #F2F2F2;
}
.mobile-search-button img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.mobile-search-button svg {
  width: 20px;
  height: 20px;
}

/*----------------------------------------*/
/* 2.24 offcanvas
/*----------------------------------------*/
.td-offcanvas {
  position: fixed;
  z-index: 99;
  background-color: var(--td-primary);
  width: 425px;
  inset-inline-end: 0;
  top: 0;
  padding: 20px 20px;
  height: 100vh;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .td-offcanvas {
    width: 300px;
  }
}
.td-offcanvas-logo {
  width: 130px;
}
.td-offcanvas-open {
  visibility: visible;
  opacity: 1;
  transform: translateX(0);
}
.td-offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}
.td-offcanvas-header .authentication-and-close {
  display: flex;
  align-items: center;
  gap: 15px;
}
.td-offcanvas-header .authentication-and-close .auth .auth-btn {
  background-color: var(--td-card-bg-1);
  height: 30px;
  padding: 10px 16px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  line-height: 1.8571428571;
  font-weight: 500;
}
.td-offcanvas-navbars {
  display: flex;
  flex-direction: column;
  margin-top: 30px;
}
.td-offcanvas-navbars .start-selling-mobile-btn {
  padding-top: 12px;
}
.td-offcanvas-buttons {
  margin-top: 20px;
  display: block;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-offcanvas-buttons {
    display: none;
  }
}
.td-offcanvas-overlay {
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  z-index: 98;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45s ease-in-out;
  background: rgba(24, 24, 24, 0.4);
}
.td-offcanvas-overlay-open {
  visibility: visible;
  opacity: 0.7;
}

.bar-button {
  display: none;
  margin-inline-start: 8px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .bar-button {
    display: block;
  }
}
.bar-button .td-offcanvas-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  justify-content: center;
}
.bar-button .td-offcanvas-toggle span {
  display: block;
  width: 30px;
  height: 2px;
  background-color: var(--td-heading);
}
@media (max-width: 767px) {
  .bar-button .td-offcanvas-toggle span {
    width: 22px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .bar-button .td-offcanvas-toggle span {
    width: 25px;
  }
}

.td-offcanvas-close .td-offcanvas-close-toggle SVG * {
  fill: var(--td-white);
}

/*----------------------------------------*/
/*  2.11 search_popup
/*----------------------------------------*/
.mobile-search-popup {
  padding-top: 40px;
  padding-bottom: 40px;
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  width: 100%;
  height: 100%;
  background-color: var(--td-white);
  z-index: 99;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-100%);
  transition: transform 0.6s ease, opacity 0.6s ease, visibility 0.6s ease;
}
@media (max-width: 767px) {
  .mobile-search-popup {
    padding-top: 28px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .mobile-search-popup {
    padding-top: 40px;
  }
}
.mobile-search-popup.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0%);
}
.mobile-search-popup .logo-and-close {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}
.mobile-search-popup .logo-and-close .logo img {
  height: 40px;
}
@media (max-width: 767px) {
  .mobile-search-popup .logo-and-close .logo img {
    height: 30px;
  }
}
.mobile-search-popup .logo-and-close SVG * {
  fill: var(--td-heading);
}

/*----------------------------------------*/
/*  2.7 notification
/*----------------------------------------*/
.notification {
  position: relative;
}
.notification .has-notification {
  position: absolute;
  top: -3px;
  inset-inline-end: -3px;
  width: 10px;
  height: 10px;
  background-color: var(--td-red);
  border-radius: 50%;
  display: none;
}
.notification .has-notification.active {
  display: block;
}
.notification-btn {
  width: 40px;
  height: 40px;
  gap: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #F2F2F2;
  transition: transform 0.3s ease;
  position: relative;
}
.notification-btn::after {
  content: "";
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--td-secondary);
}
.notification-btn svg {
  width: 20px;
  height: 20px;
}
.notification .notification-box {
  position: absolute;
  top: 45px;
  inset-inline-end: 0;
  width: 418px;
  flex-shrink: 0;
  overflow-y: auto;
  border-radius: 14px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 10;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .notification .notification-box {
    inset-inline-start: auto;
    inset-inline-end: 0;
  }
}
@media (max-width: 767px) {
  .notification .notification-box {
    top: 50px;
    width: 295px;
    inset-inline-end: -85px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .notification .notification-box {
    top: 50px;
    width: 270px;
    inset-inline-end: 0px;
  }
}
.notification .notification-box.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.notification .notification-box .notification-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 16px;
  background: #F1F1F1;
}
@media (max-width: 767px) {
  .notification .notification-box .notification-navigation {
    padding: 12px 12px 12px 12px;
  }
}
.notification .notification-box .notification-navigation h4 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
.notification .notification-box .notification-navigation .mark-all-read {
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  transition: all 0.3s ease-in-out;
}
.notification .notification-box .notification-navigation .mark-all-read:hover {
  color: var(--td-secondary);
}
.notification .notification-box .all-notification-list .notification-list {
  height: 300px;
  overflow-y: auto;
}
.notification .notification-box .all-notification-list .notification-list::-webkit-scrollbar {
  width: 0.3125rem;
}
.notification .notification-box .all-notification-list .notification-list::-webkit-scrollbar-track {
  background: #d6d6d6;
}
.notification .notification-box .all-notification-list .notification-list::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, rgba(8, 8, 8, 0.4) 0%, rgba(8, 8, 8, 0.4) 100%);
}
.notification .notification-box .all-notification-list .notification-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.notification .notification-box .all-notification-list .notification-list .list {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .notification .notification-box .all-notification-list .notification-list .list {
    padding: 4px 8px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .notification .notification-box .all-notification-list .notification-list .list {
    padding: 12px 12px;
  }
}
.notification .notification-box .all-notification-list .notification-list .list.info .list-item .icon {
  background-color: #6456FE;
}
.notification .notification-box .all-notification-list .notification-list .list.info .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list.success .list-item .icon {
  background-color: #31B269;
}
.notification .notification-box .all-notification-list .notification-list .list.success .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list.error .list-item .icon {
  background-color: #FB2D26;
}
.notification .notification-box .all-notification-list .notification-list .list.error .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list.pending .list-item .icon {
  background-color: #E830CF;
}
.notification .notification-box .all-notification-list .notification-list .list.pending .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list.primary .list-item .icon {
  background-color: #2674FB;
}
.notification .notification-box .all-notification-list .notification-list .list.primary .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list.warning .list-item .icon {
  background-color: #5B2C8E;
}
.notification .notification-box .all-notification-list .notification-list .list.warning .list-item .icon SVG * {
  fill: #fff;
}
.notification .notification-box .all-notification-list .notification-list .list:hover, .notification .notification-box .all-notification-list .notification-list .list.active {
  background: var(--td-card-bg-1);
}
.notification .notification-box .all-notification-list .notification-list .list .list-item {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 80%;
}
@media (max-width: 767px) {
  .notification .notification-box .all-notification-list .notification-list .list .list-item {
    gap: 8px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .notification .notification-box .all-notification-list .notification-list .list .list-item {
    gap: 16px;
  }
}
.notification .notification-box .all-notification-list .notification-list .list .list-item .icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(210, 210, 210, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}
.notification .notification-box .all-notification-list .notification-list .list .list-item .icon svg {
  height: 20px;
  width: 20px;
}
.notification .notification-box .all-notification-list .notification-list .list .list-item .texts h3 {
  display: inline-block;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.notification .notification-box .all-notification-list .notification-list .list p {
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  text-wrap: nowrap;
  width: 20%;
  text-align: right;
}
.notification .notification-box .all-notification-list .notification-list .list.active .list-item .texts h3 {
  position: relative;
}
.notification .notification-box .all-notification-list .notification-list .list.active .list-item .texts h3::after {
  position: absolute;
  top: 7px;
  inset-inline-end: -20px;
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--td-secondary);
}
.notification .notification-box .all-notification-list .action-btn {
  padding: 18px 25px;
}
.notification .notification-box .all-notification-list .action-btn a {
  color: var(--td-secondary);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.notification .notification-box .all-notification-list .action-btn a:hover {
  color: var(--td-primary);
  text-decoration: underline;
}

/* Notification container */
.all-notification .notification-container {
  position: fixed;
  top: 11%;
  inset-inline-end: 2.7%;
  z-index: 1000;
  max-width: 80%;
  --content-color: black;
  --background-color: #f3f3f3;
  --font-size-content: 16px;
  --icon-size: 1.6em;
  max-width: 80%;
  display: flex;
  flex-direction: column;
  gap: 0.5em;
  list-style-type: none;
  font-family: sans-serif;
  color: var(--content-color);
}
.all-notification .notification-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  gap: 1em;
  overflow: hidden;
  padding: 16px 20px;
  border-radius: 6px;
  box-shadow: rgba(111, 111, 111, 0.2) 0px 8px 24px;
  background-color: var(--background-color);
  transition: all 250ms ease;
  --grid-color: rgba(225, 225, 225, 0.7);
  background-image: linear-gradient(0deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent), linear-gradient(90deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent);
  background-size: 55px 55px;
}
.all-notification .notification-item svg {
  transition: 250ms ease;
}
.all-notification .notification-item:hover {
  transform: scale(1.01);
}
.all-notification .notification-item:active {
  transform: scale(1.05);
}
.all-notification .notification-item .notification-close:hover {
  background-color: rgba(204, 204, 204, 0.45);
}
.all-notification .notification-item .notification-close:hover svg {
  color: rgb(0, 0, 0);
}
.all-notification .notification-item .notification-close:active svg {
  transform: scale(1.1);
}
.all-notification .notification-item .notification-close {
  padding: 2px;
  border-radius: 5px;
  transition: all 250ms;
}
.all-notification .notification-container svg {
  width: var(--icon-size);
  height: var(--icon-size);
  color: var(--content-color);
}
.all-notification .notification-icon {
  display: flex;
  align-items: center;
}
.all-notification .success {
  color: #047857;
  background-color: #7dffbc;
  --grid-color: rgba(16, 185, 129, 0.25);
  background-image: linear-gradient(0deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent), linear-gradient(90deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent);
}
.all-notification .success svg {
  color: #047857;
}
.all-notification .success .notification-progress-bar {
  background-color: #047857;
}
.all-notification .success:hover {
  background-color: #5bffaa;
}
.all-notification .info {
  color: #1e3a8a;
  background-color: #7eb8ff;
  --grid-color: rgba(59, 131, 246, 0.25);
  background-image: linear-gradient(0deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent), linear-gradient(90deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent);
}
.all-notification .info svg {
  color: #1e3a8a;
}
.all-notification .info .notification-progress-bar {
  background-color: #1e3a8a;
}
.all-notification .info:hover {
  background-color: #5ba5ff;
}
.all-notification .warning {
  color: #78350f;
  background-color: #ffe57e;
  --grid-color: rgba(245, 159, 11, 0.25);
  background-image: linear-gradient(0deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent), linear-gradient(90deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent);
}
.all-notification .warning svg {
  color: #78350f;
}
.all-notification .warning .notification-progress-bar {
  background-color: #78350f;
}
.all-notification .warning:hover {
  background-color: #ffde59;
}
.all-notification .error {
  color: #7f1d1d;
  background-color: #ff7e7e;
  --grid-color: rgba(239, 68, 68, 0.25);
  background-image: linear-gradient(0deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent), linear-gradient(90deg, transparent 23%, var(--grid-color) 24%, var(--grid-color) 25%, transparent 26%, transparent 73%, var(--grid-color) 74%, var(--grid-color) 75%, transparent 76%, transparent);
}
.all-notification .error svg {
  color: #7f1d1d;
}
.all-notification .error .notification-progress-bar {
  background-color: #7f1d1d;
}
.all-notification .error:hover {
  background-color: #ff5f5f;
}
.all-notification .notification-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.5em;
}
.all-notification .notification-text {
  font-size: var(--font-size-content);
  user-select: none;
}
.all-notification .notification-close {
  cursor: pointer;
}
.all-notification .notification-progress-bar {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  height: 1px;
  background: var(--content-color);
  width: 100%;
  transform: translateX(100%);
  animation: progressBar 5s linear forwards infinite;
}
@keyframes progressBar {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

/*----------------------------------------*/
/*  2.9 chat
/*----------------------------------------*/
.chat {
  position: relative;
}
.chat .has-chat {
  position: absolute;
  top: -3px;
  inset-inline-end: -3px;
  width: 10px;
  height: 10px;
  background-color: var(--td-red);
  border-radius: 50%;
  display: none;
}
.chat .has-chat.active {
  display: block;
}
.chat-btn {
  width: 40px;
  height: 40px;
  gap: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #F2F2F2;
  position: relative;
}
.chat-btn::after {
  content: "";
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--td-secondary);
}
.chat-btn svg {
  width: 20px;
  height: 20px;
}
.chat-box {
  position: absolute;
  top: 45px;
  inset-inline-end: 0;
  width: 464px;
  flex-shrink: 0;
  overflow-y: auto;
  border-radius: 14px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .chat-box {
    inset-inline-start: auto;
    inset-inline-end: 0;
  }
}
@media (max-width: 767px) {
  .chat-box {
    top: 50px;
    width: 270px;
    inset-inline-end: -40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .chat-box {
    top: 50px;
    width: 330px;
    inset-inline-end: 0px;
  }
}
.chat-box.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.chat-box .chat-navigation {
  padding: 18px 16px;
  background: #F1F1F1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.chat-box .chat-navigation h4 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
.chat-box .chat-navigation .mark-all-read {
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  transition: all 0.3s ease-in-out;
}
.chat-box .chat-navigation .mark-all-read:hover {
  color: var(--td-secondary);
}
.chat-box .chat-list {
  height: 345px;
  overflow-y: auto;
}
.chat-box .chat-list::-webkit-scrollbar {
  width: 0.3125rem;
}
.chat-box .chat-list::-webkit-scrollbar-track {
  background: #d6d6d6;
}
.chat-box .chat-list::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, rgba(8, 8, 8, 0.4) 0%, rgba(8, 8, 8, 0.4) 100%);
}
.chat-box .chat-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.chat-box .chat-list .chat {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px 24px 16px 24px;
  transition: all 0.3s ease-in-out;
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
}
.chat-box .chat-list .chat .left {
  display: flex;
  align-items: center;
  gap: 12px !important;
}
.chat-box .chat-list .chat:hover, .chat-box .chat-list .chat.unread {
  background-color: rgba(255, 98, 41, 0.04);
}
.chat-box .chat-list .chat:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.chat-box .chat-list .chat .user {
  width: 36px;
  height: 36px;
  flex-shrink: 0;
  border-radius: 40px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
.chat-box .chat-list .chat .user img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: cover;
}
.chat-box .chat-list .chat .texts h6 {
  color: var(--td-heading);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.4285714286;
  margin-bottom: 4px;
}
.chat-box .chat-list .chat .texts p {
  color: var(--td-text-primary);
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.4;
}
.chat-box .chat-list .chat .texts p .check-icon {
  display: none;
}
.chat-box .chat-list .chat .texts p.has-seen .check-icon {
  display: inline-block;
}
.chat-box .action-btn {
  padding: 18px 25px;
}
.chat-box .action-btn a {
  color: var(--td-secondary);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;
}
.chat-box .action-btn a:hover {
  color: var(--td-primary);
  text-decoration: underline;
}
.chat-box-2 {
  width: 100%;
  flex-shrink: 0;
  overflow-y: auto;
  border-radius: 14px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(6px);
}
.chat-box-2 h4 {
  color: var(--td-white);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.6;
  padding: 24px 24px 12px 24px;
  border-bottom: 1px solid var(--td-card-bg-2);
}
@media (max-width: 767px) {
  .chat-box-2 h4 {
    padding: 24px 12px 5px 12px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .chat-box-2 h4 {
    padding: 24px 24px 12px 24px;
  }
}
.chat-box-2 .chat-list {
  height: 710px;
  overflow-y: auto;
}
.chat-box-2 .chat-list::-webkit-scrollbar {
  width: 0.3125rem;
}
.chat-box-2 .chat-list::-webkit-scrollbar-track {
  background: #d6d6d6;
}
.chat-box-2 .chat-list::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, rgba(8, 8, 8, 0.4) 0%, rgba(8, 8, 8, 0.4) 100%);
}
.chat-box-2 .chat-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.chat-box-2 .chat-list .chat {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px 12px 24px;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .chat-box-2 .chat-list .chat {
    padding: 12px 12px 12px 12px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .chat-box-2 .chat-list .chat {
    padding: 12px 24px 12px 24px;
  }
}
.chat-box-2 .chat-list .chat:hover, .chat-box-2 .chat-list .chat.active {
  background-color: rgba(255, 255, 255, 0.2);
}
.chat-box-2 .chat-list .chat:last-child {
  margin-bottom: 0;
}
.chat-box-2 .chat-list .chat .user {
  width: 45px;
  height: 45px;
  border-radius: 8px;
  flex-shrink: 0;
}
.chat-box-2 .chat-list .chat .user img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: cover;
}
.chat-box-2 .chat-list .chat .texts h6 {
  color: var(--td-white);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.4285714286;
}
.chat-box-2 .chat-list .chat .texts p {
  color: var(--td-text-primary);
  font-size: 0.625rem;
  font-weight: 600;
  line-height: 1.6;
}
.chat-box-2 .chat-list .chat .texts p .check-icon {
  display: none;
}
.chat-box-2 .chat-list .chat .texts p.has-seen .check-icon {
  display: inline-block;
}

.chat-time {
  display: block;
  color: var(--td-text-primary);
  font-size: 0.625rem;
  font-weight: 500;
  line-height: 1.6;
}

.for-mobile-toggle .mobile-chat-lists {
  margin-top: 10px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  height: 0;
  pointer-events: none;
}
.for-mobile-toggle .mobile-chat-lists.open {
  opacity: 1;
  transform: scaleY(1);
  height: auto;
  pointer-events: auto;
}

/*----------------------------------------*/
/*  2.10 user
/*----------------------------------------*/
.user-box {
  display: flex;
  align-items: center;
  position: relative;
}
.user-box .user {
  display: inline-flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  margin-inline-start: 8px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .user-box .user {
    display: none;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .user-box .user {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    margin-inline-start: 0px;
  }
}
@media (max-width: 767px) {
  .user-box .user {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    margin-inline-start: 0px;
  }
}
.user-box .user img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .user-box .user img {
    border-radius: 50%;
  }
}
@media (max-width: 767px) {
  .user-box .user img {
    border-radius: 50%;
  }
}
.user-box-mobile .user {
  display: none;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .user-box-mobile .user {
    display: inline-flex;
  }
}
.user-box .user-content {
  position: absolute;
  top: 44px;
  inset-inline-end: 0;
  width: 292px;
  border-radius: 14px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .user-box .user-content {
    top: 36px;
  }
}
@media (max-width: 767px) {
  .user-box .user-content {
    top: 36px;
    width: 250px;
  }
}
.user-box .user-content.open {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.user-box .user-content .user-box-content {
  margin-left: 16px;
  margin-right: 16px;
  margin-top: 16px;
}
.user-box .user-content .user-box-content .user-balance {
  padding: 16px;
  background: #2a59fe;
  border-radius: 16px;
  margin-top: 10px;
  color: #fff;
}
.user-box .user-content .user-box-content .user-balance h6 {
  font-size: 14px;
  color: #fff;
  margin-bottom: 10px;
}
.user-box .user-content .user-box-content .user-balance .topup-btn {
  background-color: #fff;
  color: #080808;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
}
.user-box .user-content .user-box-content h5 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
.user-box .user-content .user-box-content a {
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  transition: all 0.3s ease-in-out;
  display: block;
  margin-top: 4px;
}
.user-box .user-content .user-box-content a:hover {
  color: var(--td-secondary);
  text-decoration: underline;
}
.user-box .user-content .topup {
  padding: 14px;
  border-radius: 12px;
  background: #F1F1F1;
  margin: 16px;
}
.user-box .user-content .topup h5 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 10px;
}
.user-box .user-content .topup a {
  display: inline-flex;
  height: 26px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  background: var(--td-secondary);
  color: var(--td-white);
  font-size: 13px;
  font-weight: 600;
  line-height: normal;
}
.user-box .user-content .content {
  padding-top: 10px;
  margin: 16px;
  border-top: 1px solid rgba(48, 48, 48, 0.16);
}
.user-box .user-content .content ul {
  list-style-type: none;
}
.user-box .user-content .content ul li a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  position: relative;
}
.user-box .user-content .content ul li a .icon {
  display: inline-flex;
  height: 18px;
  width: 18px;
}
.user-box .user-content .content ul li a .icon img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.user-box .user-content .content ul li a .text {
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
}
.user-box .user-content .content ul li a:hover {
  border-radius: 10px;
  background: rgba(255, 98, 41, 0.08);
}
.user-box .user-content .content ul li button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 16px;
  position: relative;
  width: 100%;
  border-radius: 8px;
  background: rgba(255, 83, 83, 0.1);
}
.user-box .user-content .content ul li button .icon {
  display: inline-flex;
  height: 18px;
  width: 18px;
}
.user-box .user-content .content ul li button .icon img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.user-box .user-content .content ul li button .text {
  color: #FF5353;
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
}
.user-box .user-content .content ul li button:hover {
  background: rgba(255, 98, 41, 0.2);
}
.user-box .user-content .content ul li:last-child a::after {
  display: none;
}
.user-box .user-content .content ul li:last-child button::after {
  display: none;
}

/*----------------------------------------*/
/*  Heading
/*----------------------------------------*/
.websiteTitle {
  color: var(--td-white);
  text-align: center;
  font-size: 60px;
  font-weight: 800;
  line-height: normal;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .websiteTitle {
    font-size: 55px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .websiteTitle {
    font-size: 45px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .websiteTitle {
    font-size: 35px;
  }
}
@media (max-width: 767px) {
  .websiteTitle {
    font-size: 26px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .websiteTitle {
    font-size: 30px;
  }
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .section-title {
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }
}
.section-title .left .title-text {
  display: flex;
  align-items: center;
  gap: 10px;
}
.section-title .left .title-text h2 {
  color: var(--td-heading);
  font-size: 24px;
  font-weight: 700;
  line-height: 1.3333333333;
}
@media (max-width: 767px) {
  .section-title .left .title-text h2 {
    font-size: 20px;
  }
}
.section-title .right .swiper-arrows {
  display: flex;
  align-items: center;
  gap: 8px;
}
.section-title .right .swiper-arrows .swiper-btn {
  color: var(--td-secondary);
}
.section-title .right .swiper-arrows .swiper-btn.swiper-button-disabled {
  color: rgba(48, 48, 48, 0.4);
}
.section-title .right .swiper-arrows .swiper-btn .arrow-icon {
  font-size: 24px;
}

.middle-section-title {
  display: flex;
  justify-content: center;
  align-items: center;
}
.middle-section-title .middle {
  width: 415px;
}
.middle-section-title .middle h2 {
  color: var(--td-heading);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.3333333333;
  margin-bottom: 12px;
}
.middle-section-title .middle p {
  color: var(--td-text-primary);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
}

.common-page-header {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 58px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-header {
    padding: 50px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-header {
    padding: 45px 0;
  }
}
@media (max-width: 767px) {
  .common-page-header {
    padding: 30px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-header {
    padding: 40px 0;
  }
}
.common-page-header .content {
  max-width: 600px;
}
.common-page-header .content h2 {
  color: var(--td-heading);
  text-align: center;
  font-size: 30px;
  font-weight: 700;
  line-height: 1.4;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .common-page-header .content h2 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .common-page-header .content h2 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .common-page-header .content h2 {
    font-size: 22px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-page-header .content h2 {
    font-size: 24px;
  }
}

.all-games-filter {
  display: flex;
  justify-content: end;
}
.all-games-filter .filters {
  display: flex;
  align-items: center;
  gap: 10px;
}

/*----------------------------------------*/
/*  nice-select
/*----------------------------------------*/
.common-filter .nice-select {
  height: 40px;
  line-height: 38px;
  color: rgba(48, 48, 48, 0.8);
  font-size: 13px;
  font-weight: 600;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  backdrop-filter: blur(8px);
}
.common-filter .nice-select:after {
  border-bottom: 1px solid rgba(48, 48, 48, 0.8);
  border-right: 1px solid rgba(48, 48, 48, 0.8);
  height: 6px;
  width: 6px;
}
.common-filter .nice-select .list {
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  padding: 10px;
  left: auto;
  right: 0;
  z-index: 50;
  box-shadow: none;
}
.common-filter .nice-select .option:hover,
.common-filter .nice-select .option.focus,
.common-filter .nice-select .option.selected.focus {
  border-radius: 6px;
  background-color: #FFEFEA;
  color: var(--td-secondary);
  font-weight: 500;
}
.common-filter.common-filter-height-fixed .nice-select .list {
  height: 250px;
  overflow-y: auto;
}

.auth-nice-select .nice-select {
  height: 44px;
  line-height: 43px;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.2);
  width: 100%;
}
.auth-nice-select .nice-select:after {
  border-bottom: 1px solid rgba(48, 48, 48, 0.6);
  border-right: 1px solid rgba(48, 48, 48, 0.6);
  height: 7px;
  width: 7px;
}
.auth-nice-select .nice-select .list {
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  padding: 10px;
  left: auto;
  right: 0;
  z-index: 50;
  box-shadow: none;
  width: 100%;
}
.auth-nice-select .nice-select .option:hover,
.auth-nice-select .nice-select .option.focus,
.auth-nice-select .nice-select .option.selected.focus {
  border-radius: 6px;
  background-color: #FFEFEA;
  color: var(--td-secondary);
  font-weight: 500;
}

/*----------------------------------------*/
/*  pagination
/*----------------------------------------*/
.common-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-pagination ul {
  display: flex;
  align-items: center;
  gap: 10px;
  list-style-type: none;
}
.common-pagination ul li a {
  font-family: var(--td-heading-font);
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #F1F1F1;
  color: rgba(48, 48, 48, 0.6);
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  line-height: normal;
}
@media (max-width: 767px) {
  .common-pagination ul li a {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    font-size: 14px;
    gap: 6px;
  }
}
.common-pagination ul li a:hover, .common-pagination ul li a.active {
  border: 1px solid var(--td-secondary);
  background: var(--td-secondary);
  color: var(--td-white);
}
.common-pagination ul li a.navigation.disabled {
  cursor: not-allowed;
}
.common-pagination ul li a.navigation.disabled .arrow {
  opacity: 0.2;
  cursor: not-allowed;
}
.common-pagination ul li a.navigation:hover, .common-pagination ul li a.navigation.active {
  border-color: var(--td-secondary);
  background: var(--td-secondary);
}
.common-pagination ul li a.navigation:hover .arrow, .common-pagination ul li a.navigation.active .arrow {
  color: var(--td-white);
  opacity: 1;
}

/*----------------------------------------*/
/*  2.7 forms
/*----------------------------------------*/
input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
textarea {
  outline: none;
  height: 45px;
  width: 100%;
  padding: 0 15px;
  border: 1px solid #CACACA;
  color: rgba(8, 8, 8, 0.6);
  background: transparent;
  font-size: 14px;
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--td-primary);
  box-shadow: unset;
  opacity: 1;
}
input[type=text].input-exceptional,
input[type=search].input-exceptional,
input[type=email].input-exceptional,
input[type=tel].input-exceptional,
input[type=number].input-exceptional,
input[type=password].input-exceptional,
textarea.input-exceptional {
  background: #F4F4F4;
}
input[type=text].input-exceptional-2,
input[type=search].input-exceptional-2,
input[type=email].input-exceptional-2,
input[type=tel].input-exceptional-2,
input[type=number].input-exceptional-2,
input[type=password].input-exceptional-2,
textarea.input-exceptional-2 {
  height: 35px;
  background-color: var(--td-white);
}
input[type=text].input-design-2,
input[type=search].input-design-2,
input[type=email].input-design-2,
input[type=tel].input-design-2,
input[type=number].input-design-2,
input[type=password].input-design-2,
textarea.input-design-2 {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 0;
  color: var(--td-white);
}
input[type=text].input-design-2:focus,
input[type=search].input-design-2:focus,
input[type=email].input-design-2:focus,
input[type=tel].input-design-2:focus,
input[type=number].input-design-2:focus,
input[type=password].input-design-2:focus,
textarea.input-design-2:focus {
  border-color: var(--td-white);
  box-shadow: unset;
  opacity: 1;
  background-color: rgba(8, 8, 8, 0.2);
}
input[type=text].input-design-pxNone,
input[type=search].input-design-pxNone,
input[type=email].input-design-pxNone,
input[type=tel].input-design-pxNone,
input[type=number].input-design-pxNone,
input[type=password].input-design-pxNone,
textarea.input-design-pxNone {
  padding: 0 0;
}
input[type=text].input-design-pxNone:focus,
input[type=search].input-design-pxNone:focus,
input[type=email].input-design-pxNone:focus,
input[type=tel].input-design-pxNone:focus,
input[type=number].input-design-pxNone:focus,
input[type=password].input-design-pxNone:focus,
textarea.input-design-pxNone:focus {
  background-color: rgba(8, 8, 8, 0);
}

textarea {
  padding: 14px 24px;
}
textarea:focus {
  border-color: var(--td-heading);
}

.form-switch {
  display: flex;
  align-items: center;
}
.form-switch input[type=checkbox] {
  opacity: 1;
  position: relative;
  margin-inline-start: 0 !important;
  margin-top: 0;
  outline: none;
  margin-bottom: 0;
}
.form-switch input[type=checkbox]:checked {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}
.form-switch input[type=checkbox]:focus {
  outline: 0;
  box-shadow: none;
}
.form-switch input[type=checkbox] ~ label {
  padding-inline-start: 10px;
}
.form-switch input[type=checkbox] ~ label::before, .form-switch input[type=checkbox] ~ label::after {
  display: none;
}

.animate-custom button,
.animate-custom input,
.animate-custom optgroup,
.animate-custom select,
.animate-custom textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  display: none;
}
.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx::before {
  display: none;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span a {
  color: var(--td-secondary);
}
.animate-custom .cbx span a:hover {
  color: #000000;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: var(--td-white);
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-secondary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inline-start: 5px;
  color: var(--td-heading);
  font-weight: 500;
  font-size: 14px;
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: var(--td-secondary);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-secondary);
  background: var(--td-secondary);
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.was-validated .td-form-group .input-field {
  position: relative;
}
.was-validated .td-form-group .input-field input {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}
.was-validated .td-form-group .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
}

.td-form-group.has-right-icon .input-field .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}
.td-form-group.has-right-icon .input-icon i {
  font-size: 14px;
}
.td-form-group.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
  inset-inline-end: 20px !important;
  inset-inline-start: auto !important;
}
.td-form-group.has-right-icon .input-icon.eyeicon img {
  width: 18px;
}
.td-form-group.has-right-icon .input-icon.icon-selected svg * {
  stroke: rgba(21, 20, 21, 0.7);
  /* Change stroke color */
  fill: rgba(21, 20, 21, 0.7);
  /* Change stroke color */
  stroke-opacity: 1;
  /* Full opacity */
  transition: all 0.3s ease;
  /* Smooth animation */
}
.td-form-group.selected_icon .input-icon {
  inset-inline-end: 33px;
  cursor: pointer;
}
.td-form-group.has-left-icon .input-field .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-left-icon .input-icon {
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  width: max-content;
}
.td-form-group.has-left-icon .input-icon.eyeicon {
  cursor: pointer;
}
.td-form-group .input-field {
  position: relative;
}
.td-form-group .input-field.date-of-birth {
  position: relative;
}
.td-form-group .input-field.date-of-birth .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
}
.td-form-group .input-field.has-right-icon {
  position: relative;
}
.td-form-group .input-field.has-right-icon .form-control {
  color: #008080;
}
.td-form-group .input-field.has-right-icon .icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #F8F9FA;
}
.td-form-group .input-field.has-right-icon .icon .copy-icon {
  font-size: 14px;
  color: #6B7280;
}
.td-form-group .input-field.has-right-icon .icon .copy-tooltip {
  position: absolute;
  top: -30px;
  inset-inline-end: 0;
  background-color: #000;
  color: var(--td-white);
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}
.td-form-group .input-field.has-right-icon .icon.show-tooltip .copy-tooltip {
  opacity: 1;
}
.td-form-group .input-field .edit-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 15px;
  height: 20px;
  display: flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: var(--td-card-bg-1);
  color: var(--td-white);
  font-size: 0.625rem;
  font-weight: 400;
  line-height: 1.6;
}
.td-form-group .input-field.input-group {
  flex-wrap: nowrap;
}
.td-form-group .input-field .input-group-text {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.td-form-group .input-field.disabled input,
.td-form-group .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.td-form-group .input-field.disabled input:focus,
.td-form-group .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.td-form-group .input-field input,
.td-form-group .input-field textarea {
  font-size: 14px;
}
.td-form-group .input-field input::-webkit-input-placeholder,
.td-form-group .input-field textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::-moz-placeholder,
.td-form-group .input-field textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-moz-placeholder,
.td-form-group .input-field textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-ms-input-placeholder,
.td-form-group .input-field textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::placeholder,
.td-form-group .input-field textarea::placeholder {
  /* MODERN BROWSER */
  color: rgba(21, 20, 21, 0.65);
  font-size: 14px;
}
.td-form-group .input-field textarea {
  padding: 12px 15px;
  height: 150px;
  resize: none;
  line-height: 1.5;
  border-radius: 12px;
  font-size: 16px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: transparent;
  color: var(--td-heading);
}
.td-form-group .input-field textarea:focus {
  border-color: var(--td-secondary);
}
.td-form-group .input-field textarea::placeholder {
  color: #9BA2AE;
}
.td-form-group .input-field.height-large textarea {
  height: 237px;
}
.td-form-group .input-field .form-control {
  height: 52px;
  border-radius: 0px;
  background: transparent;
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 400;
  line-height: 100%;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
.td-form-group .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  color: rgba(0, 0, 0, 0.7);
}
.td-form-group .input-field .form-control:focus {
  border: 1px solid var(--td-secondary);
}
.td-form-group .input-field .form-control-2 {
  height: 44px;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.2);
}
.td-form-group .input-field .form-control-focus:focus {
  border: 1px solid var(--td-secondary);
}
.td-form-group .input-field-2 .form-control {
  font-size: 14px;
  border: 1px solid transparent;
  color: rgba(8, 8, 8, 0.7);
  background-color: rgba(8, 8, 8, 0.04);
}
.td-form-group .input-field-icon input {
  padding: 0 45px 0 15px;
}
[dir=rtl] .td-form-group .input-field-icon input {
  padding: 0 15px 0 45px;
}
.td-form-group .input-field-exceptional {
  margin-top: 8px;
}
.td-form-group .input-field-phone {
  position: relative;
}
.td-form-group .input-field-phone .form-control {
  padding: 0 15px 0 75px;
}
.td-form-group .input-field-phone .country-code {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 15px;
  padding-inline-end: 10px;
  border-inline-end: 1px solid #CACACA;
}
.td-form-group .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.td-form-group .input-label {
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  display: flex;
  margin-bottom: 0.5em;
}
.td-form-group .input-label span {
  padding-inline-start: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #EC0707;
}
.td-form-group .input-label-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-form-group .input-label-inner > p {
  font-size: 12px;
}
.td-form-group .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.td-form-group .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.td-form-group .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.td-form-group .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  border-radius: 10px;
  padding: 0 10px;
}
.td-form-group .input-select .nice-select .option.selected {
  font-weight: 500;
}
.td-form-group .input-select .nice-select .option:hover {
  background-color: #353535;
}
.td-form-group .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.td-form-group .input-select .nice-select.open, .td-form-group .input-select .nice-select:focus {
  background-color: #353535;
}
.td-form-group.input-fill .input-label {
  font-weight: 700;
}
.td-form-group.input-fill input,
.td-form-group.input-fill select,
.td-form-group.input-fill textarea {
  background-color: #FCFCFC;
  border: 1px solid rgba(21, 20, 21, 0.2);
}
.td-form-group.input-fill input:focus,
.td-form-group.input-fill select:focus,
.td-form-group.input-fill textarea:focus {
  border-color: var(--td-primary);
}
.td-form-group .form-select {
  height: 50px;
  border-radius: 8px;
  font-size: 14px;
}
.td-form-group .form-select:focus {
  font-size: 14px;
}
.td-form-group .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px 10px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-form-group .otp-verification {
    gap: 10px 10px;
  }
}
.td-form-group .otp-verification input {
  background: rgba(103, 107, 113, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 69.83px;
  height: 77px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-form-group .otp-verification input {
    height: 55px;
    width: 50px;
  }
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: #DC1D4B;
  display: none;
}
.feedback-invalid.active {
  display: block;
}

.input-attention {
  font-size: 12px;
  color: var(--tdvar(--td-danger));
}
.input-attention.xs {
  font-size: 10px;
}

.image-upload .td-form-group .input-label-2 {
  color: #6B7280;
  font-size: 14px;
  font-weight: 500;
  line-height: 100%;
  display: flex;
  margin-bottom: 0.4em;
}

*::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

*::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  opacity: 1;
}

.common-select2-dropdown .select2-container {
  width: 100% !important;
}
.common-select2-dropdown .select2-container.select2-container--open .select2-selection--single {
  border-radius: 20px 20px 0 0;
}
.common-select2-dropdown .select2-container .select2-selection--single {
  height: 45px;
  border-radius: 40px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single {
  border: 1px solid #CACACA;
  background: var(--td-white);
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: rgba(8, 8, 8, 0.6);
  line-height: 43px;
  font-size: 14px;
  padding-inline-end: 35px;
  padding-inline-start: 14px;
}
.common-select2-dropdown .select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 40px;
  position: absolute;
  top: 1px;
  inset-inline-end: 10px;
  width: 20px;
}
.common-select2-dropdown .select2-dropdown {
  background-color: var(--td-bg);
  border: 1px solid var(--td-card-bg-2);
  border-radius: 4px;
}
.common-select2-dropdown .select2-results__option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.common-select2-dropdown .select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
  color: var(--td-white);
  padding: 0 15px;
}
.common-select2-dropdown .select2-results__option {
  padding: 6px 15px;
  user-select: none;
  -webkit-user-select: none;
  font-size: 14px;
  color: var(--td-white);
}

.common-payment-form input[type=text],
.common-payment-form input[type=number] {
  outline: none;
  height: 40px;
  width: 100%;
  padding: 0 15px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
  font-size: 12px;
}
.common-payment-form input[type=text]::placeholder,
.common-payment-form input[type=number]::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.payment-method-checkbox {
  position: relative;
  margin: 10px 10px 0 0;
  /* Show checkmark only when input is checked */
}
.payment-method-checkbox input[type=radio] {
  display: none;
}
.payment-method-checkbox .check-box-image {
  width: 50px;
  height: 50px;
  background-color: var(--td-white);
  border: 2px solid rgba(8, 8, 8, 0.1);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}
.payment-method-checkbox .check-box-image .img {
  width: 40px;
  height: 40px;
}
.payment-method-checkbox .check-box-image .img img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.payment-method-checkbox .check-box-image::before {
  content: "✔";
  position: absolute;
  top: 3px;
  inset-inline-start: 3px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: green;
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: none;
  z-index: 2;
  align-items: center;
  justify-content: center;
}
.payment-method-checkbox .check-box-image::after {
  content: "";
  position: absolute;
  background: rgba(0, 255, 0, 0.1);
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  z-index: 1;
  display: none;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image {
  border-color: green;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image::before {
  display: flex;
}
.payment-method-checkbox input[type=radio]:checked + .check-box-image::after {
  display: block;
}

.custom-file-input {
  height: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  padding: 8px 15px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.04);
  position: relative;
}
.custom-file-input .upload-btn {
  height: 26px;
  display: flex;
  padding: 5px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;
}
.custom-file-input #fileInput {
  display: none;
}
.custom-file-input .preview-area {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.custom-file-input .preview-area .image-container {
  position: relative;
  display: inline-block;
  margin-inline-end: 10px;
}
.custom-file-input .preview-area .image-container img {
  width: 26px;
  height: 26px;
  object-fit: cover;
  border-radius: 4px;
}
.custom-file-input .preview-area .image-container .remove-btn {
  position: absolute;
  top: -4px;
  inset-inline-end: -4px;
  cursor: pointer;
  color: red;
  font-size: 12px;
  background: white;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-file-input .preview-area #fileName {
  color: rgba(255, 255, 255, 0.4);
  font-size: 14px;
  font-style: italic;
  font-weight: 400;
  line-height: 20px;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-file-input .preview-area-2 {
  position: relative;
}
.custom-file-input .preview-area-2 img {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}
.custom-file-input .preview-area-2 .remove-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: -40px;
  cursor: pointer;
}
.custom-file-input .hidden {
  display: none;
}

.product-details-form .form-title {
  border-bottom: 1px solid var(--td-card-bg-1);
  margin-top: 24px;
}
.product-details-form .form-title h5 {
  color: var(--td-white);
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.3333333333;
  padding-bottom: 8px;
}
.product-details-form .set-infomation-btn {
  margin-top: 16px;
}

.set-method-btn {
  margin-top: 30px;
}

.custom-quill-editor .ql-snow .ql-stroke {
  stroke: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-fill,
.custom-quill-editor .ql-snow .ql-stroke.ql-fill {
  fill: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-picker {
  color: var(--td-white);
}
.custom-quill-editor .ql-toolbar.ql-snow {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px 12px 0 0;
}
.custom-quill-editor .ql-container.ql-snow {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0 0 12px 12px;
}
.custom-quill-editor .ql-snow .ql-picker-options {
  background: #1A1E30;
}
.custom-quill-editor .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.custom-quill-editor .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: rgba(255, 255, 255, 0.3);
}
.custom-quill-editor .ql-snow .ql-editor h1 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h2 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h3 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h4 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h5 {
  color: var(--td-white);
}
.custom-quill-editor .ql-snow .ql-editor h6 {
  color: var(--td-white);
}

.common-payment-form {
  padding: 20px;
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: var(--td-card-bg-1);
  backdrop-filter: blur(5px);
}
@media (max-width: 767px) {
  .common-payment-form {
    padding: 16px;
  }
}
.common-payment-form .withdraw-button {
  margin-top: 20px;
}
@media (max-width: 767px) {
  .common-payment-form .withdraw-button {
    margin-top: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .common-payment-form .withdraw-button {
    margin-top: 20px;
  }
}
.common-payment-form .all-payment-method-here .single-payment-method {
  padding: 16px;
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 10px;
  position: relative;
}
.common-payment-form .all-payment-method-here .single-payment-method .img {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.common-payment-form .all-payment-method-here .single-payment-method .img img {
  width: 100%;
}
.common-payment-form .all-payment-method-here .single-payment-method .edit-and-cross {
  position: absolute;
  top: 10px;
  inset-inline-end: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.common-payment-form-exceptional {
  padding: 0px;
  border-radius: 0px;
  border: none;
  background: none;
  backdrop-filter: none;
}

/*----------------------------------------
	Image Preview 
-----------------------------------------*/
.file-upload-wrap .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.file-upload-wrap .input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #151415;
}
.file-upload-wrap #uploadItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 155px;
  text-align: center;
}
.upload-custom-file input[type=file] {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 2px;
  height: 2px;
  overflow: hidden;
  opacity: 0;
}
.upload-custom-file label {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  inset-inline-end: 0;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.4s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  -ms-transition: -ms-transform 0.4s;
  -o-transition: -o-transform 0.4s;
  transition: transform 0.4s;
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(21, 20, 21, 0.16);
  border-radius: 0px;
}
.upload-custom-file label span {
  display: block;
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
.upload-custom-file label span b {
  color: var(--td-text-primary);
  font-weight: 500;
}
.upload-custom-file label .type-file-text {
  margin-top: 5px;
  color: #E94E5B;
}
.upload-custom-file label .upload-icon {
  width: 40px;
  margin: 0 auto;
  margin-bottom: 4px;
}
.upload-custom-file label.file-ok {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  border-color: var(--td-primary);
}
.upload-custom-file label.file-ok span {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 0.3rem;
  color: #ffffff;
  background-color: var(--td-primary);
  font-weight: 500;
  font-size: 14px;
  margin: auto;
  text-decoration: none;
}
.upload-custom-file label.file-ok .upload-icon {
  display: none;
}
.upload-custom-file.without-image {
  height: 167px;
}
.upload-custom-file.without-image label {
  background-color: var(--td-text-primary);
}

.upload-thumb-close {
  position: absolute;
  inset-inline-end: 10px;
  top: 35px;
  z-index: 5;
  color: #E94E5B;
  display: none;
}

.file-upload-close,
.input-file-close {
  position: absolute;
  top: 8px;
  inset-inline-end: 8px;
  color: #F34141;
  font-size: 18px;
  z-index: 55;
}

.custom-input-label {
  color: rgba(8, 8, 8, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 4px;
}
.custom-input-label .uit--calender {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M19.5 4h-3V2.5a.5.5 0 0 0-1 0V4h-7V2.5a.5.5 0 0 0-1 0V4h-3A2.503 2.503 0 0 0 2 6.5v13A2.503 2.503 0 0 0 4.5 22h15a2.5 2.5 0 0 0 2.5-2.5v-13A2.5 2.5 0 0 0 19.5 4M21 19.5a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 3 19.5V11h18zm0-9.5H3V6.5C3 5.672 3.67 5 4.5 5h3v1.5a.5.5 0 0 0 1 0V5h7v1.5a.5.5 0 0 0 1 0V5h3A1.5 1.5 0 0 1 21 6.5z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label .system-uicons--door-alt {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 21 21'%3E%3Cg fill='none' fill-rule='evenodd' transform='translate(4 1)'%3E%3Cpath stroke='%23000' stroke-linecap='round' stroke-linejoin='round' d='M2.5 2.5h2v14h-2a2 2 0 0 1-2-2v-10a2 2 0 0 1 2-2M7.202.513l4 1.5A2 2 0 0 1 12.5 3.886v11.228a2 2 0 0 1-1.298 1.873l-4 1.5A2 2 0 0 1 4.5 16.614V2.386A2 2 0 0 1 7.202.513' stroke-width='1'/%3E%3Ccircle cx='6.5' cy='9.5' r='1' fill='%23000'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label .pepicons-pencil--persons {
  display: inline-block;
  width: 14px;
  height: 14px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cg fill='%23000' fill-rule='evenodd' clip-rule='evenodd'%3E%3Cpath d='M3.36 9.977a5.5 5.5 0 0 0-.923 3.05V14a.5.5 0 1 1-1 0v-.972A6.5 6.5 0 0 1 2.53 9.422l.108-.162a.5.5 0 1 1 .832.555z'/%3E%3Cpath d='M6.18 8.365c-1.09 0-2.107.544-2.711 1.45l-.832-.554a4.26 4.26 0 0 1 3.542-1.896h.22a.5.5 0 0 1 0 1zm3.078 1.6c.47.706.721 1.534.721 2.382h1a5.3 5.3 0 0 0-.889-2.936l-.1-.15a.5.5 0 1 0-.832.554z'/%3E%3Cpath d='M6.448 8.365c1.089 0 2.106.544 2.71 1.45l.832-.554a4.26 4.26 0 0 0-3.542-1.896h-.22a.5.5 0 1 0 0 1z'/%3E%3Cpath d='M6.25 7.25a2.25 2.25 0 1 0 0-4.5a2.25 2.25 0 0 0 0 4.5m0 1a3.25 3.25 0 1 0 0-6.5a3.25 3.25 0 0 0 0 6.5m4.259 4.936a5.5 5.5 0 0 0-.924 3.051v1.034a.5.5 0 1 1-1 0v-1.034a6.5 6.5 0 0 1 1.091-3.605l.133-.2a.5.5 0 0 1 .832.556z'/%3E%3Cpath d='M13.42 11.5a3.34 3.34 0 0 0-2.78 1.488l-.831-.555A4.34 4.34 0 0 1 13.42 10.5h.224a.5.5 0 1 1 0 1zm3.187 1.686a5.5 5.5 0 0 1 .924 3.051v1.034a.5.5 0 1 0 1 0v-1.034a6.5 6.5 0 0 0-1.092-3.605l-.133-.2a.5.5 0 1 0-.832.556z'/%3E%3Cpath d='M13.695 11.5a3.34 3.34 0 0 1 2.78 1.488l.832-.555a4.34 4.34 0 0 0-3.612-1.933h-.225a.5.5 0 1 0 0 1z'/%3E%3Cpath d='M13.5 10.5a2.25 2.25 0 1 0 0-4.5a2.25 2.25 0 0 0 0 4.5m0 1a3.25 3.25 0 1 0 0-6.5a3.25 3.25 0 0 0 0 6.5'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(8, 8, 8, 0.8);
  transform: translateY(1px);
}
.custom-input-label-2 {
  color: white;
}
.custom-input-label-2 .uit--calender {
  color: white;
}
.custom-input-label-2 .system-uicons--door-alt {
  color: white;
}
.custom-input-label-2 .pepicons-pencil--persons {
  color: white;
}

.custom-range-calender {
  position: relative;
}
.custom-range-calender .icon {
  position: absolute;
  inset-inline-start: 15px;
  top: 50%;
  transform: translateY(-50%);
}
.custom-range-calender input[type=text] {
  height: 56px;
  background-color: #F2F3F5;
  border-radius: 12px;
  border: none;
  padding: 0 15px 0 40px;
  font-size: 16px;
}
[dir=rtl] .custom-range-calender input[type=text] {
  padding: 0 40px 0 15px;
}

/*----------------------------------------*/
/*  Promo styles
/*----------------------------------------*/
.popup-overlay {
  position: fixed;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(21, 20, 21, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: all 0.3s;
  visibility: visible;
  opacity: 1;
}
.popup-overlay.hidden {
  visibility: hidden;
  opacity: 0;
}

.promo-popup-main {
  position: relative;
  display: flex;
  width: 700px;
  max-width: 90%;
  background: #FFF2ED;
  z-index: 1001;
  border-radius: 30px;
}
.promo-popup-main .promo-contents {
  flex: 1;
  border-radius: 30px;
  padding: 50px 50px;
  background: #FFF2ED;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents {
    padding: 30px 30px;
  }
}
.promo-popup-main .promo-contents .logo {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}
.promo-popup-main .promo-contents .heading {
  color: var(--td-secondary);
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 4px;
}
.promo-popup-main .promo-contents .discount {
  color: var(--td-heading);
  text-align: center;
  font-size: 52px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents .discount {
    font-size: 36px;
  }
}
.promo-popup-main .promo-contents .description {
  color: var(--td-heading);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents .description {
    margin-bottom: 16px;
  }
}
.promo-popup-main .promo-contents .subscription-form {
  margin-top: 10px;
  position: relative;
  width: 90%;
}
.promo-popup-main .promo-contents .email-input {
  border: 1px solid rgba(21, 20, 21, 0.16);
  height: 52px;
  padding-inline-end: 150px;
  border-radius: 10px;
  background: #fff;
}
.promo-popup-main .promo-contents .email-input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:-moz-placeholder {
  /* Firefox 4-18 */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input::placeholder {
  /* MODERN BROWSER */
  color: var(--td-text-primary) !important;
}
.promo-popup-main .promo-contents .email-input:focus {
  border-color: var(--td-secondary);
  color: var(--td-text-primary) !important;
}
@media (max-width: 480px) {
  .promo-popup-main .promo-contents .email-input {
    padding-inline-end: 16px;
  }
}
.promo-popup-main .promo-contents .submit-button {
  height: 44px;
  position: absolute;
  inset-inline-end: 5px;
  top: 50%;
  transform: translateY(-50%);
  padding: 0 16px;
  border: 0;
  border-radius: 8px;
}
@media (max-width: 767px) {
  .promo-popup-main .promo-contents .submit-button {
    position: inherit;
    top: inherit;
    transform: inherit;
    margin-top: 16px;
    width: 100%;
    inset-inline-end: 0px;
    height: 52px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .promo-popup-main .promo-contents .submit-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-top: 0px;
    width: unset;
    inset-inline-end: 10px;
    height: 36px;
  }
}
.promo-popup-main .promo-contents .submit-button:hover {
  background-color: #a68457;
}
.promo-popup-main .promo-contents .submit-button:hover:hover {
  color: var(--td-white);
  background-color: var(--td-heading);
}
.promo-popup-main .promp-image {
  flex: 1;
  position: relative;
}
@media (max-width: 767px) {
  .promo-popup-main .promp-image {
    display: none;
  }
}
.promo-popup-main .promp-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.promo-popup-main .close-btn {
  position: absolute;
  top: 15px;
  inset-inline-end: 15px;
  width: 30px;
  height: 30px;
  background-color: var(--td-secondary);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.promo-popup-main .image-overlay {
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  pointer-events: none;
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  border: 1px solid #FFE4C0;
  transform: translate(-50%, -50%);
}
[dir=rtl] .promo-popup-main .image-overlay {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.cookie {
  position: fixed;
  bottom: 20px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  z-index: 80;
}
.cookie .cookie-box {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 18px 20px;
  border-radius: 12px;
  background: #FFF2ED;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cookie .cookie-box {
    flex-direction: column;
    padding: 25px;
    border-radius: 20px;
    width: 400px;
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .cookie .cookie-box {
    flex-direction: column;
    padding: 25px;
    border-radius: 20px;
    width: 300px;
    gap: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .cookie .cookie-box {
    flex-direction: column;
    padding: 25px;
    border-radius: 20px;
    width: 400px;
    gap: 20px;
  }
}
.cookie .cookie-box .cookie-text {
  display: flex;
  align-items: center;
  gap: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .cookie .cookie-box .cookie-text {
    flex-direction: column;
  }
}
.cookie .cookie-box .cookie-text .icon {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}
@media (max-width: 767px) {
  .cookie .cookie-box .cookie-text .icon {
    width: 40px;
    height: 40px;
  }
}
.cookie .cookie-box .cookie-text .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cookie .cookie-box .cookie-text p {
  color: var(--td-heading);
  text-align: left;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.625;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .cookie .cookie-box .cookie-text p {
    text-align: center;
  }
}
.cookie .cookie-box .cookie-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}
.cookie .cookie-box .cookie-buttons .accept {
  background-color: var(--td-primary);
  padding: 6px 15px;
  border-radius: 8px;
  color: var(--td-white);
  font-size: 0.875rem;
  line-height: line-height(26, 14);
  font-weight: 500;
}
.cookie .cookie-box .cookie-buttons .rejected {
  background-color: rgba(255, 0, 0, 0.6);
  padding: 6px 15px;
  border-radius: 8px;
  color: var(--td-white);
  font-size: 0.875rem;
  line-height: line-height(26, 14);
  font-weight: 500;
}

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/
.td-alert-box {
  background: #010c1a;
  padding: 16px 20px;
  z-index: 1;
  position: relative;
  transition: 0.3s;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  column-gap: 0.75rem;
  align-items: center;
  border: 0.0625rem solid transparent;
  width: 351px;
}
@media (max-width: 480px) {
  .td-alert-box {
    padding: 0.625rem 0.75rem 0.625rem;
    width: 300px;
  }
}
.td-alert-box .alert-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  column-gap: 1rem;
  flex-grow: 1;
  overflow: hidden;
}
@media (max-width: 767px) {
  .td-alert-box .alert-content {
    column-gap: 0.75rem;
  }
}
.td-alert-box .alert-content .alert-title {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--td-body-font);
}
@media (max-width: 767px) {
  .td-alert-box .alert-content .alert-title {
    font-size: 0.875rem;
  }
}
.td-alert-box .alert-content .alert-message {
  font-size: 14px;
  position: relative;
  margin-top: 2px;
}
.td-alert-box .alert-icon {
  flex: 0 0 auto;
}
.td-alert-box .alert-icon svg {
  width: 2.25rem;
  height: 2.25rem;
}
.td-alert-box .close-btn {
  padding: 5px;
  position: absolute;
  right: -8px;
  top: -8px;
  display: flex;
  width: 30px;
  height: 30px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 24px;
  background: #FFDDE0;
}
.td-alert-box.hidden {
  opacity: 0;
  transform: translateY(-50%, 1.25rem);
  pointer-events: none;
}
.td-alert-box.has-success {
  border-left: 4px solid #0C9;
  background: #fff;
  border-radius: 16px;
  border-top: 1px solid rgba(48, 48, 48, 0.16);
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
}
.td-alert-box.has-warning {
  border-left: 4px solid #F2C94C;
  background: #fff;
  border-radius: 16px;
  border-radius: 16px;
  border-top: 1px solid rgba(48, 48, 48, 0.16);
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
}
.td-alert-box.has-info {
  border-left: 4px solid #5458F7;
  background: #fff;
  border-radius: 16px;
  border-radius: 16px;
  border-top: 1px solid rgba(48, 48, 48, 0.16);
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
  position: relative;
  overflow: hidden;
}
.td-alert-box.has-info::before {
  content: "";
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  position: absolute;
  background: #5458F7;
}
.td-alert-box.has-danger {
  border-left: 4px solid #E93A2D;
  background: #fff;
  border-radius: 16px;
  border-radius: 16px;
  border-top: 1px solid rgba(48, 48, 48, 0.16);
  border-right: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
}

.alert-show-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 999;
}

/* Header css*/
/*----------------------------------------*/
/*  3.1 Header-1
/*----------------------------------------*/
.header {
  height: 80px;
  display: flex;
  align-items: center;
  background-color: var(--td-white);
  justify-content: center;
  width: 100%;
}
.header-full {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.header-full .left {
  display: flex;
  align-items: center;
  gap: 32px;
}
.header-full .left .logo img {
  height: 35px;
}
@media (max-width: 767px) {
  .header-full .left .logo img {
    height: 22px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .header-full .left .logo img {
    height: 30px;
  }
}
.header-full .left .navigation-menu ul {
  list-style-type: none;
  display: flex;
  justify-content: center;
  gap: 20px;
}
.header-full .left .navigation-menu ul li a {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.8571428571;
  transition: all 0.3s ease-in-out;
}
.header-full .left .navigation-menu ul li a:hover {
  color: var(--td-white);
}
.header-full .right {
  display: flex;
  align-items: center;
  gap: 8px;
}
.header-full .right .navigation-menu {
  margin-inline-end: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .navigation-menu {
    margin-inline-end: 10px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .navigation-menu {
    display: none;
  }
}
.header-full .right .navigation-menu ul {
  list-style-type: none;
  display: flex;
  justify-content: center;
  gap: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .header-full .right .navigation-menu ul {
    gap: 16px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .navigation-menu ul {
    gap: 16px;
  }
}
.header-full .right .navigation-menu ul li a {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease-in-out;
}
.header-full .right .navigation-menu ul li a:after {
  content: "";
  position: absolute;
  inset-inline-start: 0;
  bottom: 0px;
  width: 0;
  height: 1px;
  background-color: var(--td-secondary);
  transition: width 0.3s ease-in-out;
}
.header-full .right .navigation-menu ul li a:hover, .header-full .right .navigation-menu ul li a.active {
  color: var(--td-secondary);
}
.header-full .right .navigation-menu ul li a:hover::after, .header-full .right .navigation-menu ul li a.active::after {
  width: 100%;
}
.header-full .right .auth-language-cta {
  display: flex;
  align-items: center;
}
.header-full .right .auth-language-cta .auth-language-cta-inside {
  display: flex;
  align-items: center;
  background: var(--td-card-bg-2);
  border-radius: 14px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .language-button {
    display: none;
  }
}
.header-full .right .auth-language-cta .language-button .nice-select {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  border: solid 1px transparent;
  border-radius: 0px;
  background-color: transparent;
  height: 42px;
  line-height: 40px;
  padding-left: 18px;
  padding-right: 30px;
}
.header-full .right .auth-language-cta .language-button .nice-select::after {
  border-bottom: 1px solid #303030;
  border-right: 1px solid #303030;
  height: 6px;
  width: 6px;
  right: 16px;
  content: "";
  display: block;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}
.header-full .right .auth-language-cta .language-button .nice-select .list {
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  padding: 10px;
  left: auto;
  right: 0;
}
.header-full .right .auth-language-cta .language-button .nice-select .option:hover,
.header-full .right .auth-language-cta .language-button .nice-select .option.focus,
.header-full .right .auth-language-cta .language-button .nice-select .option.selected.focus {
  border-radius: 6px;
  background-color: #FFEFEA;
  color: var(--td-secondary);
  font-weight: 500;
}
.header-full .right .auth-language-cta .auth-cta {
  display: flex;
  align-items: center;
  padding: 6px;
  gap: 24px;
  border-radius: 14px;
}
[dir=rtl] .header-full .right .auth-language-cta .auth-cta {
  border-radius: 14px 0px 0px 14px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta {
    gap: 16px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .header-full .right .auth-language-cta .auth-cta {
    border-radius: 14px;
  }
  [dir=rtl] .header-full .right .auth-language-cta .auth-cta {
    border-radius: 14px;
  }
}
@media (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta {
    padding: 0;
    border-radius: 0;
    background: transparent;
  }
}
.header-full .right .auth-language-cta .auth-cta .start-selling {
  position: relative;
}
.header-full .right .auth-language-cta .auth-cta .start-selling .left-bar {
  display: inline-block;
  position: absolute;
  width: 1px;
  height: 22px;
  background: linear-gradient(360deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 51%, rgba(255, 255, 255, 0) 100%);
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta .start-selling .left-bar {
    display: none;
  }
}
.header-full .right .auth-language-cta .auth-cta .start-selling .right-bar {
  display: inline-block;
  position: absolute;
  width: 1px;
  height: 22px;
  background: linear-gradient(360deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 51%, rgba(255, 255, 255, 0) 100%);
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 0;
}
.header-full .right .auth-language-cta .auth-cta .auth-btn {
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta .auth-btn {
    padding: 0px 16px 0 16px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta .auth-btn {
    display: none;
  }
}
.header-full .right .auth-language-cta .auth-cta-2 {
  padding: 13px 6px 13px 6px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .header-full .right .auth-language-cta .auth-cta-2 {
    display: none;
  }
}
.header-2 {
  position: fixed;
  top: 0;
  background: var(--td-white);
  width: 100%;
  z-index: 99;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(48, 48, 48, 0.16);
}
.header-2 .has-container-fluid {
  padding-left: 30px;
  padding-right: 30px;
}
@media (max-width: 767px) {
  .header-2 .has-container-fluid {
    padding-left: 10px;
    padding-right: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .header-2 .has-container-fluid {
    padding-left: 30px;
    padding-right: 30px;
  }
}
.header-2 .has-container-fluid .header-full .left {
  gap: 116px;
}

.full-page-overlay {
  position: absolute;
  top: 80px;
  inset-inline-start: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(8, 8, 8, 0.6);
  z-index: 3;
  visibility: hidden;
  opacity: 0;
}
.full-page-overlay.open {
  visibility: visible;
  opacity: 1;
}
.full-page-overlay-dashboard {
  height: 100vh;
  width: 100vw;
}

.has-container-fluid {
  padding-left: 100px;
  padding-right: 100px;
  width: 100%;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .has-container-fluid {
    padding-left: 90px;
    padding-right: 90px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .has-container-fluid {
    padding-left: 80px;
    padding-right: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .has-container-fluid {
    padding-left: 50px;
    padding-right: 50px;
  }
}
@media (max-width: 767px) {
  .has-container-fluid {
    padding-left: 20px;
    padding-right: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .has-container-fluid {
    padding-left: 30px;
    padding-right: 30px;
  }
}

.has-enable-disable-search-button {
  display: none;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .has-enable-disable-search-button {
    display: block;
  }
}

.offcanvas-nav {
  margin-bottom: 20px;
}
.offcanvas-nav ul {
  list-style-type: none;
}
.offcanvas-nav ul li {
  margin-bottom: 6px;
}
.offcanvas-nav ul li a {
  color: var(--td-white);
  transition: all 0.3s ease-in-out;
}
.offcanvas-nav ul li a:hover {
  color: var(--td-white);
}

.language-switch-mobile .nice-select {
  width: 100%;
  border-radius: 12px;
}
.language-switch-mobile .nice-select .list {
  width: 100%;
}

/*----------------------------------------*/
/*  3.2 Header-2
/*----------------------------------------*/
/*----------------------------------------*/
/*  About
/*----------------------------------------*/
.about-us-page-area .top-part {
  background: linear-gradient(58deg, rgba(255, 255, 255, 0.1) 0.14%, rgba(255, 41, 241, 0.1) 100.47%);
  position: relative;
}
.about-us-page-area .top-part .extra-pb-for-img {
  padding-bottom: 70px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us-page-area .top-part .extra-pb-for-img {
    padding-bottom: 0px;
  }
}
.about-us-page-area .top-part .company-stats {
  padding: 16px;
  border-radius: 16px;
  border: 1px dashed var(--td-secondary);
  background: #FFF;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 30%;
  right: 5%;
  width: 345px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us-page-area .top-part .company-stats {
    display: none;
  }
}
.about-us-page-area .top-part .company-stats .seller-count {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}
.about-us-page-area .top-part .company-stats .seller-count .icon {
  display: inline-flex;
  width: 50px;
  height: 50px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  background: rgba(255, 101, 54, 0.15);
}
.about-us-page-area .top-part .company-stats .seller-count .icon .count-icon {
  font-size: 40px;
  color: var(--td-secondary);
}
.about-us-page-area .top-part .company-stats .seller-count .text h4 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 700;
  line-height: normal;
}
.about-us-page-area .top-part .company-stats .seller-count .text p {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
.about-us-page-area .top-part .company-stats p {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
.about-us-page-area .top-part .right {
  margin-left: 16px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us-page-area .top-part .right {
    margin-left: 0px;
  }
}
.about-us-page-area .top-part .right .top-img {
  width: 100%;
  height: 170px;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .about-us-page-area .top-part .right .top-img {
    margin-bottom: 16px;
  }
}
.about-us-page-area .top-part .right .top-img img {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  object-fit: cover;
}
.about-us-page-area .top-part .right .middle-img {
  width: 100%;
  height: 312px;
}
.about-us-page-area .top-part .right .middle-img img {
  width: 100%;
  height: 100%;
  border-radius: 18px;
  object-fit: cover;
}
.about-us-page-area .top-part .right .bottom-img {
  width: 241px;
  height: 250px;
  border-radius: 18px;
  border: 15px solid #FFF6FE;
  position: absolute;
  bottom: -70px;
  left: -100px;
  overflow: hidden;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .about-us-page-area .top-part .right .bottom-img {
    display: none;
  }
}
.about-us-page-area .top-part .right .bottom-img img {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}
.about-us-page-area .top-part .left h2 {
  color: var(--td-heading);
  font-size: 40px;
  font-weight: 700;
  line-height: 1.25;
  width: 80%;
  margin-bottom: 16px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-us-page-area .top-part .left h2 {
    font-size: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-us-page-area .top-part .left h2 {
    font-size: 30px;
    width: 100%;
  }
}
@media (max-width: 767px) {
  .about-us-page-area .top-part .left h2 {
    font-size: 20px;
    width: 100%;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .about-us-page-area .top-part .left h2 {
    font-size: 24px;
    width: 100%;
  }
}
.about-us-page-area .top-part .left .des-1 {
  color: rgba(48, 48, 48, 0.8);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  margin-top: 18px;
}
@media (max-width: 767px) {
  .about-us-page-area .top-part .left .des-1 {
    margin-top: 10px;
    font-size: 14px;
  }
}
.about-us-page-area .top-part .left .des-2 {
  color: rgba(48, 48, 48, 0.8);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
  margin-top: 18px;
  width: 90%;
}
@media (max-width: 767px) {
  .about-us-page-area .top-part .left .des-2 {
    margin-top: 10px;
    font-size: 14px;
  }
}
.about-us-page-area .top-part .left .action-btn {
  margin-top: 30px;
}
.about-us-page-area .bottom-part h4 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
}
.about-us-page-area .bottom-part p {
  color: rgba(48, 48, 48, 0.8);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.625;
  margin-top: 10px;
}

.about-stats-area {
  background: #F1F1F1;
}
.about-stats-area .about-stats-area-content h2 {
  color: var(--td-heading);
  text-align: center;
  font-size: 30px;
  font-weight: 700;
  line-height: normal;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-stats-area .about-stats-area-content h2 {
    font-size: 26px;
  }
}
@media (max-width: 767px) {
  .about-stats-area .about-stats-area-content h2 {
    font-size: 24px;
  }
}
.about-stats-area .about-stats-area-content .all-stats-card {
  padding: 65px 30px;
  border-radius: 20px;
  background: #FFF;
  margin-top: 16px;
}
@media (max-width: 767px) {
  .about-stats-area .about-stats-area-content .all-stats-card {
    padding: 30px 10px;
  }
}
.about-stats-area .about-stats-area-content .all-stats-card .stats-card h2 {
  color: var(--td-secondary);
  text-align: center;
  font-size: 40px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .about-stats-area .about-stats-area-content .all-stats-card .stats-card h2 {
    font-size: 22px;
    margin-bottom: 10px;
  }
}
.about-stats-area .about-stats-area-content .all-stats-card .stats-card p {
  color: var(--td-heading);
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
}
@media (max-width: 767px) {
  .about-stats-area .about-stats-area-content .all-stats-card .stats-card p {
    font-size: 14px;
  }
}

/*----------------------------------------*/
/*  Hero
/*----------------------------------------*/
.hero-area {
  padding-top: 80px;
  padding-bottom: 429px;
  position: relative;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .hero-area {
    padding-top: 70px;
    padding-bottom: 350px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero-area {
    padding-top: 60px;
    padding-bottom: 300px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero-area {
    padding-top: 50px;
    padding-bottom: 215px;
  }
}
@media (max-width: 767px) {
  .hero-area {
    padding-top: 30px;
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-area {
    padding-top: 40px;
    padding-bottom: 160px;
  }
}
.hero-area .full-title-and-cta {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: center;
}
.hero-area .full-title-and-cta .hero-cta-button {
  display: flex;
  gap: 16px;
  align-items: center;
}
@media (max-width: 767px) {
  .hero-area .full-title-and-cta .hero-cta-button {
    flex-direction: column;
    align-items: start;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-area .full-title-and-cta .hero-cta-button {
    flex-direction: row;
    align-items: center;
  }
}
.hero-area .hero-img {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  margin: 0 auto;
  max-width: 1440px;
  padding: 0 40px;
}
.hero-area .hero-img .hero-full-img {
  width: 100%;
  height: 329px;
  display: flex;
  justify-content: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .hero-area .hero-img .hero-full-img {
    height: 270px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .hero-area .hero-img .hero-full-img {
    height: 220px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero-area .hero-img .hero-full-img {
    height: 170px;
  }
}
@media (max-width: 767px) {
  .hero-area .hero-img .hero-full-img {
    height: 60px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .hero-area .hero-img .hero-full-img {
    height: 125px;
  }
}
.hero-area .hero-img .hero-full-img img {
  width: 100%;
  height: 100%;
  object-fit: unset;
}

/*----------------------------------------*/
/*  Category
/*----------------------------------------*/
.category-area .category-all .category-all-card {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 28px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-area .category-all .category-all-card {
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .category-area .category-all .category-all-card {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
}
@media (max-width: 767px) {
  .category-area .category-all .category-all-card {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .category-area .category-all .category-all-card {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }
}

/*----------------------------------------*/
/*  All Games
/*----------------------------------------*/
.all-games-area {
  background: #F9F9F9;
}
.all-games-area .all-games-filter .filter-button-box {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.all-games-area .all-games-filter .filter-button-box .filter-button {
  display: inline-flex;
  padding: 10px 15px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  backdrop-filter: blur(8px);
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn {
  font-size: 18px;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.all-icon {
  color: #6456FE;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.games-icon {
  color: #6456FE;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.games-coin-icon {
  color: #FBAD26;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.software-icon {
  color: #FB2D26;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.payment-icon {
  color: #5B2C8E;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.item-icon {
  color: #2674FB;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.boosting-icon {
  color: #E830CF;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.coaching-icon {
  color: #2674FB;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.gift-icon {
  color: #19C8CF;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.account-icon {
  color: #31B269;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.telco-icon {
  color: #665773;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.skins-icon {
  color: #31ADB2;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.zalando-icon {
  color: #FF6229;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.google-play-icon {
  color: #932FBA;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.riotgames-icon {
  color: #FB2D26;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.itunes-icon {
  color: #26B4FB;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.airbnb-icon {
  color: #2674FB;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.xbox-icon {
  color: #9C9538;
}
.all-games-area .all-games-filter .filter-button-box .filter-button .tab-iocn.wallet-icon {
  color: #5B2C8E;
}
.all-games-area .all-games-filter .filter-button-box .filter-button span {
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 700;
  line-height: 20px;
}
.all-games-area .all-games-filter .filter-button-box .filter-button:hover, .all-games-area .all-games-filter .filter-button-box .filter-button.active {
  border: 1px solid var(--td-secondary);
  background-color: var(--td-secondary);
}
.all-games-area .all-games-filter .filter-button-box .filter-button:hover span, .all-games-area .all-games-filter .filter-button-box .filter-button.active span {
  color: var(--td-white);
}
.all-games-area .all-games-filter .filter-button-box .filter-button:hover .tab-iocn, .all-games-area .all-games-filter .filter-button-box .filter-button.active .tab-iocn {
  color: var(--td-white);
}

/*----------------------------------------*/
/*  Popular Seller
/*----------------------------------------*/
.popular-seller-area {
  background: var(--td-bg-1);
}
.popular-seller-area .popular-seller-area-content {
  padding: 32px;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.04);
}
@media (max-width: 767px) {
  .popular-seller-area .popular-seller-area-content {
    padding: 20px;
  }
}

/*----------------------------------------*/
/*  faq
/*----------------------------------------*/
.faq-area .faq-main-content .right {
  margin-left: 135px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .faq-area .faq-main-content .right {
    margin-left: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .faq-area .faq-main-content .right {
    margin-left: 0px;
  }
}
.faq-area .faq-main-content .right .faq-img {
  width: 100%;
  height: 100%;
}
.faq-area .faq-main-content .right .faq-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.faq .faq-content .left h2 {
  width: 90%;
  margin-bottom: 16px;
  color: var(--td-white);
}
.faq .faq-content .left p {
  width: 80%;
}
@media (max-width: 767px) {
  .faq .faq-content .left p {
    width: 100%;
  }
}
.faq .faq-content .left .img-box {
  height: 350px;
  margin-top: 24px;
}
@media (max-width: 767px) {
  .faq .faq-content .left .img-box {
    height: 240px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .faq .faq-content .left .img-box {
    height: 460px;
  }
}
.faq .faq-content .left .img-box img {
  height: 100%;
  object-fit: cover;
}

.common-faq-box .accordion .accordion-item {
  margin-bottom: 16px;
  border: none;
}
@media (max-width: 767px) {
  .common-faq-box .accordion .accordion-item {
    margin-bottom: 10px;
  }
}
.common-faq-box .accordion .accordion-item:first-of-type {
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  border-top-left-radius: 13px;
  border-top-right-radius: 13px;
}
.common-faq-box .accordion .accordion-item:last-of-type {
  border-bottom-left-radius: unset;
  border-bottom-right-radius: unset;
  border-bottom-left-radius: 13px;
  border-bottom-right-radius: 13px;
}
.common-faq-box .accordion .accordion-item .accordion-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--td-heading) !important;
  font-size: 16px;
  font-weight: 500;
  line-height: 120%;
  padding: 20px 40px 20px 20px;
}
@media (max-width: 767px) {
  .common-faq-box .accordion .accordion-item .accordion-button {
    padding: 20px 40px 20px 20px;
    font-size: 18px;
  }
}
.common-faq-box .accordion .accordion-item .accordion-button:focus {
  box-shadow: unset;
}
.common-faq-box .accordion .accordion-item .accordion-button:not(.collapsed) {
  color: var(--td-heading) !important;
  background-color: transparent;
  box-shadow: unset;
  padding: 20px 20px 20px 20px;
  border-radius: 12px 12px 0 0;
  border: 1px solid rgba(48, 48, 48, 0.16);
  border-bottom: none;
}
@media (max-width: 767px) {
  .common-faq-box .accordion .accordion-item .accordion-button:not(.collapsed) {
    padding: 20px 20px 20px 20px;
  }
}
.common-faq-box .accordion .accordion-item .accordion-button.collapsed {
  color: inherit;
  border-bottom-right-radius: unset;
  border-bottom-left-radius: unset;
  border-radius: 12px;
}
.common-faq-box .accordion .accordion-item .accordion-body {
  padding: unset;
  padding: 0px 50px 20px 20px;
  color: var(--td-text-primary);
  background-color: transparent;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  border-radius: 0 0 12px 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  border-top: none;
}
.common-faq-box .accordion .accordion-item .accordion-body span {
  color: var(--td-theme-bg-color);
}
.common-faq-box-2 .accordion .accordion-item {
  background-color: transparent;
  border-radius: 13px;
}

.common-faq-box .accordion .accordion-item .accordion-button {
  position: relative;
}
.common-faq-box .accordion .accordion-item .accordion-button::after {
  display: none !important;
}
.common-faq-box .accordion .accordion-item .accordion-button::before {
  content: "";
  background-image: url("../../assets/images/faq/accordion-arrow-plus.svg") !important;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  display: inline-block;
  width: 20px;
  height: 20px;
  position: absolute;
  inset-inline-end: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease-in-out;
}
.common-faq-box .accordion .accordion-item .accordion-button:not(.collapsed)::before {
  content: "";
  background-image: url("../../assets/images/faq/accordion-arrow-cross.svg") !important;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  display: inline-block;
  width: 20px;
  height: 20px;
  position: absolute;
  inset-inline-end: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease-in-out;
  transform: translateY(-50%) rotate(180deg);
}
.common-faq-box-2 .accordion .accordion-item .accordion-button {
  background-color: transparent;
  border: 1px solid rgba(48, 48, 48, 0.16);
}

/*----------------------------------------*/
/*  Ads banner
/*----------------------------------------*/
.ads-banner-area {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.ads-banner-area .ads-banner-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ads-banner-area .ads-banner-box .ads-banner-box-content {
  width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}
@media (max-width: 767px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content {
    gap: 20px;
  }
}
.ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
  color: var(--td-heading);
  font-size: 48px;
  font-weight: 700;
  line-height: 1.1875;
  text-align: center;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
    font-size: 35px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
    font-size: 26px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .ads-banner-area .ads-banner-box .ads-banner-box-content h2 {
    font-size: 28px;
  }
}
.ads-banner-area .ads-banner-box .ads-banner-box-content h2 span.shadow-color {
  color: rgba(255, 98, 41, 0.3019607843);
}
.ads-banner-area .ads-banner-box .ads-banner-box-content h2 span.deep {
  color: #FF6229;
}

/*----------------------------------------*/
/*  Product Details
/*----------------------------------------*/
.product-details-area .left .common-product-design .product-details .top {
  display: flex;
  justify-content: space-between;
  align-items: end;
  gap: 10px;
  margin-bottom: 18px;
}
@media (max-width: 767px) {
  .product-details-area .left .common-product-design .product-details .top {
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-details-area .left .common-product-design .product-details .top {
    flex-direction: row;
    justify-content: space-between;
    align-items: end;
    gap: 10px;
  }
}
.product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul {
  display: flex;
  align-items: center;
  gap: 6px;
}
.product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul li a {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul li a:hover, .product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul li a.active {
  color: var(--td-secondary);
}
.product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul li .icon {
  display: flex;
  align-items: center;
}
.product-details-area .left .common-product-design .product-details .top .product-breadcrumb ul li .icon .arrow-right {
  color: var(--td-heading);
  font-size: 16px;
}
.product-details-area .left .common-product-design .product-details .top .price-share h3 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 12px;
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share {
  display: flex;
  align-items: center;
  gap: 20px;
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share .wish {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share .wish svg path {
  fill: var(--td-text-secondary);
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share .wish.active svg path {
  fill: #FF6229;
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share .share {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .left .common-product-design .product-details .top .price-share .wish-share .share svg {
  fill: #26B4FB;
}
.product-details-area .left .common-product-design .product-details .delivery-method-and-speed {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 32px;
}
@media (max-width: 767px) {
  .product-details-area .left .common-product-design .product-details .delivery-method-and-speed {
    flex-direction: column;
    align-items: start;
    gap: 16px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-details-area .left .common-product-design .product-details .delivery-method-and-speed {
    flex-direction: row;
    align-items: end;
    gap: 10px;
  }
}
.product-details-area .left .common-product-design .product-details .delivery-method-and-speed .delivery-method {
  display: flex;
  align-items: center;
  gap: 6px;
}
.product-details-area .left .common-product-design .product-details .delivery-method-and-speed .delivery-method p {
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .left .common-product-design .product-details .delivery-method-and-speed .speed {
  display: flex;
  align-items: center;
  gap: 6px;
}
.product-details-area .left .common-product-design .product-details .delivery-method-and-speed .speed p {
  color: var(--td-heading);
  font-size: 13px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .left .common-product-design .product-details .details-saperator {
  border-color: rgba(48, 48, 48, 0.16);
  margin: 30px 0;
}
.product-details-area .left .common-product-design .product-details .product-details-box h2 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 30px;
}
.product-details-area .left .common-product-design .product-details .product-details-box h3 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 30px;
}
.product-details-area .left .common-product-design .product-details .product-details-box h4 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 30px;
}
.product-details-area .left .common-product-design .product-details .product-details-box h5 {
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 30px;
}
.product-details-area .left .common-product-design .product-details .product-details-box h6 {
  color: var(--td-heading);
  font-size: 12px;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 30px;
}
.product-details-area .left .common-product-design .product-details .product-details-box p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
  margin-top: 16px;
}
.product-details-area .left .common-product-design .product-details .product-details-box ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 16px;
}
.product-details-area .left .common-product-design .product-details .product-details-box ul li {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
  margin-bottom: 5px;
}
.product-details-area .right .game-details-right .your-order-card {
  padding: 24px;
  border-radius: 20px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
}
.product-details-area .right .game-details-right .your-order-card h5 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 20px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease p {
  color: var(--td-text-primary);
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box {
  margin: 0 70px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box {
    margin: 0 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box {
    margin: 0 130px;
  }
}
@media (max-width: 767px) {
  .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box {
    margin: 0 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box {
    margin: 0 100px;
  }
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator {
  position: relative;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .calculator-btn {
  display: flex;
  width: 36px;
  height: 36px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  border-radius: 7px 0px 0px 7px;
  background: var(--td-secondary);
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .calculator-btn i {
  font-size: 12px;
  color: var(--td-white);
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .form-control {
  height: 36px;
  width: 100%;
  border-radius: 6px;
  border: 1px solid rgba(255, 98, 41, 0.14);
  background: rgba(255, 98, 41, 0.08);
  color: var(--td-heading);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  border-radius: 10px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .increase-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 0;
  border-radius: 0 12px 12px 0;
}
[dir=rtl] .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .increase-btn {
  border-radius: 12px 0 0 12px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .decrease-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-radius: 12px 0 0 12px;
}
[dir=rtl] .product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .calculator .decrease-btn {
  border-radius: 0 12px 12px 0;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .calculator-box .error-msg {
  font-size: 0.75rem;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .voucher {
  width: 100%;
  margin-top: 20px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .voucher input[type=text],
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .voucher input[type=number] {
  width: 100%;
  display: flex;
  height: 44px;
  padding: 0px 4px 0px 16px;
  align-items: center;
  align-self: stretch;
  border-radius: 10px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: var(--td-heading);
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .voucher input[type=text]::placeholder,
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .voucher input[type=number]::placeholder {
  color: rgba(48, 48, 48, 0.6);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .total-price .left h6 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .total-price .right h6 {
  color: var(--td-heading);
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
.product-details-area .right .game-details-right .your-order-card .price-increase-decrease .buy-now {
  margin-top: 30px;
}
.product-details-area .right .game-details-right .seller-details-card {
  padding: 24px;
  border-radius: 16px;
  background: #F1F1F1;
  margin-top: 24px;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  background: #FFF;
  padding: 12px;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left .img-box {
  width: 50px;
  height: 50px;
  border-radius: 7px;
  overflow: hidden;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left .img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left .text h3 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 8px;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left .text h3 span {
  display: inline-flex;
  height: 24px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #FFF;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 13.986px;
  border-radius: 30px;
  background: #31B269;
}
.product-details-area .right .game-details-right .seller-details-card .seller-card-box .left .text p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px;
}
.product-details-area .right .game-details-right .seller-details-card .chat-seller {
  margin-top: 16px;
}

.has-sticky-component {
  position: sticky;
  top: 395px;
}

.suggested-product-area {
  background: #F9F9F9;
}

/*----------------------------------------*/
/*  checkout
/*----------------------------------------*/
.checkout-area-content .checkout-box {
  padding: 0 140px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .checkout-area-content .checkout-box {
    padding: 0 0px;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .header-box {
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  padding: 27px 24px;
  border-radius: 20px 20px 0 0;
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .left .payment-method-box .header-box {
    padding: 20px 14px;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .header-box h4 {
  color: var(--td-heading);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: normal;
  text-align: start;
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .left .payment-method-box .header-box h4 {
    font-size: 1.125rem;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item {
  border: 1px solid rgba(48, 48, 48, 0.16);
  border-top: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:last-child {
  border-radius: 0 0 20px 20px;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: 1px solid rgba(48, 48, 48, 0.16);
  cursor: pointer;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button {
    padding: 20px 20px;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button.open {
  border-bottom: 1px solid rgba(48, 48, 48, 0.16) !important;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check {
  padding-inline-start: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check .form-check-input:checked {
  background-color: #FF6229;
  border-color: #FF6229;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check .form-check-input {
    float: left;
    margin-left: -4px;
    margin-right: 4px;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input[type=checkbox] ~ label::before,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input[type=radio] ~ label::before {
  top: 3px;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input[type=checkbox] ~ label::after,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input[type=radio] ~ label::after {
  top: 6px;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input ~ label::before {
  background-color: #2c375d;
  border: 1px solid #2c375d;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input:checked ~ label::before {
  border: 1px solid #2c375d;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button .form-check input:checked ~ label::after {
  opacity: 1;
  background-color: #2A59FE;
  border-bottom: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-button.open {
  border-radius: 12px 12px 0px 0px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  background: var(--td-card-bg-1);
  backdrop-filter: blur(5px);
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content {
  padding: 20px 32px;
  border-inline-end: 1px solid var(--td-card-bg-2);
  border-inline-start: 1px solid var(--td-card-bg-2);
  background: var(--td-card-bg-1);
  backdrop-filter: blur(5px);
  height: 0;
  opacity: 0;
  overflow: hidden;
  padding: 0 32px;
  transition: height 0.5s ease, opacity 0.4s ease, padding 0.3s ease;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content:last-child.open {
  border-radius: 0 0 20px 20px;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content.open {
  opacity: 1;
  height: auto;
  overflow: visible;
  padding: 20px 32px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content.open {
    padding: 20px 20px;
  }
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=text],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=search],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=email],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=tel],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=number],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=password],
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form textarea {
  outline: none;
  height: 52px;
  width: 100%;
  padding: 0 15px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  color: var(--td-heading);
  color: rgba(48, 48, 48, 0.6);
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=text]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=search]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=email]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=tel]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=number]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=password]:focus,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form textarea:focus {
  border-color: var(--td-secondary) !important;
  color: var(--td-heading) !important;
  box-shadow: unset;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=text]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=search]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=email]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=tel]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=number]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form input[type=password]::placeholder,
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .common-payment-form textarea::placeholder {
  color: var(--td-text-secondary);
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item .method-content .all-payment-checkbox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:first-child .method-button {
  border-radius: 12px 12px 0px 0px;
  border-bottom: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:first-child .method-button.open {
  border-bottom: 1px solid rgba(255, 255, 255, 0.6);
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:last-child .method-button {
  border-radius: 0px 0px 12px 12px;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:last-child .method-button.open {
  border-radius: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:not(:first-child):not(:last-child) .method-button {
  border-radius: 0;
  border-bottom: 0;
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:not(:first-child):not(:last-child) .method-button.open {
  border-bottom: 1px solid rgba(255, 255, 255, 0.6);
}
.checkout-area-content .checkout-box .left .payment-method-box .method-item:last-child .method-content {
  border-bottom: 1px solid var(--td-card-bg-2);
  border-radius: 0 0 12px 12px;
}
.checkout-area-content .checkout-box .right {
  padding-inline-start: 8px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .checkout-area-content .checkout-box .right {
    padding-inline-start: 0px;
  }
}
.checkout-area-content .checkout-box .right h4 {
  color: var(--td-heading);
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3333333333;
  margin-bottom: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .checkout-area-content .checkout-box .right h4 {
    margin-bottom: 25px;
    font-size: 1.25rem;
  }
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .right h4 {
    margin-bottom: 20px;
    font-size: 1.25rem;
  }
}
.checkout-area-content .checkout-box .right .checkout-card-box .title {
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  padding: 27px 24px;
  border-radius: 20px 20px 0 0;
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .right .checkout-card-box .title {
    padding: 20px 14px;
  }
}
.checkout-area-content .checkout-box .right .checkout-card-box .title h4 {
  color: var(--td-heading);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 0px;
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .right .checkout-card-box .title h4 {
    font-size: 1.125rem;
  }
}
.checkout-area-content .checkout-box .right .checkout-card-box .full-box {
  border: 1px solid rgba(48, 48, 48, 0.16);
  border-top: 0;
  background: #FFF;
  padding: 20px;
  border-radius: 0 0 20px 20px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}
@media (max-width: 767px) {
  .checkout-area-content .checkout-box .right .checkout-card-box .checkout-card {
    gap: 15px;
  }
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .img-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .img-box .img {
  width: 72px;
  height: 72px;
  border-radius: 5px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .img-box .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .text h6 {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .text span {
  color: var(--td-secondary);
  font-size: 12px;
  font-weight: 600;
  line-height: normal;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-card .text p {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  line-height: normal;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment {
  margin-top: 32px;
  padding: 24px;
  border-radius: 8px;
  background: #F1F1F1;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point:last-child {
  margin-top: 0;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point.total {
  margin-bottom: 0px !important;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point .left p {
  text-align: left;
  color: rgba(48, 48, 48, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 13px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point .right p {
  text-align: right;
  color: #303030;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 13px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point-bold .left p {
  color: var(--td-heading);
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.25;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-point-bold .right p {
  color: var(--td-heading);
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.25;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .payment-line {
  border-color: rgba(48, 48, 48, 0.16);
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .pay-now-btn {
  margin-top: 32px;
}
.checkout-area-content .checkout-box .right .checkout-card-box .checkout-payment .pay-now-btn a {
  text-transform: uppercase;
}
.checkout-area-content .checkout-box .right .checkout-card-box .pay-now-btn {
  margin-top: 30px;
}

.transaction-success-area .transaction-success-area-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
.transaction-success-area .transaction-success-area-box .content {
  max-width: 900px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.transaction-success-area .transaction-success-area-box .content .success-icon {
  width: 140px;
  height: 140px;
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .transaction-success-area .transaction-success-area-box .content .success-icon {
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
  }
}
.transaction-success-area .transaction-success-area-box .content h5 {
  color: var(--td-heading);
  text-align: center;
  font-size: 30px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .transaction-success-area .transaction-success-area-box .content h5 {
    font-size: 20px;
  }
}
.transaction-success-area .transaction-success-area-box .content p {
  color: rgba(48, 48, 48, 0.8);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.625;
}
@media (max-width: 767px) {
  .transaction-success-area .transaction-success-area-box .content p {
    font-size: 14px;
  }
}
.transaction-success-area .transaction-success-area-box .content .cta-btn {
  margin-top: 40px;
}
@media (max-width: 767px) {
  .transaction-success-area .transaction-success-area-box .content .cta-btn {
    margin-top: 20px;
  }
}

.payment-method-checkbox {
  position: relative;
  margin: 3px 10px 0 0;
  /* Show checkmark only when input is checked */
}
.payment-method-checkbox input[type=radio] {
  display: none;
}
.payment-method-checkbox .check-box-image {
  width: 42px;
  height: 31px;
  background-color: var(--td-white);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}
.payment-method-checkbox .check-box-image .img {
  width: 40px;
  height: 40px;
}
.payment-method-checkbox .check-box-image .img img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.payment-method-checkbox .check-box-image::before {
  content: "✔";
  position: absolute;
  top: 3px;
  left: 3px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #28a745;
  color: white;
  font-size: 10px;
  font-weight: bold;
  display: none;
  z-index: 2;
  align-items: center;
  justify-content: center;
}
.payment-method-checkbox .check-box-image::after {
  content: "";
  position: absolute;
  background: rgba(40, 167, 69, 0.1);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  z-index: 1;
  display: none;
}
.payment-method-checkbox input[type=radio]:checked + label.check-box-image {
  border-color: #28a745;
}
.payment-method-checkbox input[type=radio]:checked + label.check-box-image::before {
  display: flex;
}
.payment-method-checkbox input[type=radio]:checked + label.check-box-image::after {
  display: block;
}

/*----------------------------------------*/
/*  Seller Profile
/*----------------------------------------*/
.seller-profile-details-area {
  background: #F9F9F9;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-image {
  width: 100%;
  height: 304px;
  overflow: hidden;
  border-radius: 12px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-image {
    height: 404px;
  }
}
@media (max-width: 767px) {
  .seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-image {
    height: 304px;
  }
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content h5 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-description {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
  margin-bottom: 16px;
  width: 80%;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats {
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  background: #FFF;
  max-width: 295px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats .point {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats .point:last-child {
  margin-bottom: 0;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats .point p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 0;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats .point h6 {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .seller-stats .point h6.level-tag {
  display: flex;
  height: 24px;
  padding: 0px 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 30px;
  background: #31B269;
  color: #FFF;
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: 1;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-content .action-btns {
  display: flex;
  gap: 13px;
  margin-top: 30px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating {
  display: flex;
  align-items: center;
  gap: 40px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating .followers h4 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 5px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating .followers p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating .bar {
  width: 1px;
  height: 66px;
  background: rgba(48, 48, 48, 0.16);
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating .following h4 {
  color: var(--td-heading);
  font-size: 30px;
  font-weight: 500;
  line-height: normal;
  margin-bottom: 5px;
}
.seller-profile-details-area .seller-profile-details-area-content .seller-profile .seller-rating .following p {
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 0;
}

.tab-filter .tab-button-box {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.tab-filter .tab-button-box .profile-filter-button {
  height: 40px;
  display: flex;
  padding: 10px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.16);
  backdrop-filter: blur(8px);
  color: rgba(48, 48, 48, 0.8);
  font-size: 13px;
  font-weight: 600;
  line-height: 1.5384615385;
}
.tab-filter .tab-button-box .profile-filter-button.active {
  background-color: var(--td-secondary);
  border: 1px solid var(--td-secondary);
  color: var(--td-white);
}

/*----------------------------------------*/
/*  blog details
/*----------------------------------------*/
.blog-details-area .blog-details-content hr {
  margin: 20px 0;
  border-color: rgba(48, 48, 48, 0.1);
}
.blog-details-area .blog-details-content .share-box .share h6 {
  color: var(--td-heading);
  font-size: 20px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 10px;
}
.blog-details-area .blog-details-content .share-box .share .share-icon {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-details-area .blog-details-content .share-box .share .share-icon a {
  display: flex;
  width: 30px;
  height: 30px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  border-radius: 8px;
  border: 1px solid rgba(48, 48, 48, 0.16);
}
.blog-details-area .blog-details-content .share-box .share .share-icon a .social-icon {
  font-size: 18px;
  color: var(--td-heading);
}
.blog-details-area .blog-details-content .share-box .share .share-icon a:hover {
  border-color: var(--td-secondary);
  background: var(--td-secondary);
}
.blog-details-area .blog-details-content .share-box .share .share-icon a:hover .social-icon {
  color: var(--td-white);
}
.blog-details-area .blog-details-content .trending-topic .blog-horizontal-card {
  margin-bottom: 10px;
}
.blog-details-area .blog-details-content .trending-topic .blog-horizontal-card:last-child {
  margin-bottom: 0;
}

.text-editor-content img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-top: 32px;
  border-radius: 16px;
}
.text-editor-content img:first-child {
  margin-top: 0px;
}
.text-editor-content h1 {
  color: var(--td-heading);
  font-size: 26px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content h2 {
  color: var(--td-heading);
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content h3 {
  color: var(--td-heading);
  font-size: 22px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content h4 {
  color: var(--td-heading);
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content h5 {
  color: var(--td-heading);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content h6 {
  color: var(--td-heading);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 1.6;
  margin-top: 32px;
}
.text-editor-content p {
  color: var(--td-text-primary);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.375;
  margin-top: 10px;
}
.text-editor-content ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 16px;
}
.text-editor-content ul li {
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  margin-bottom: 5px;
}
.text-editor-content ul li:last-child {
  margin-bottom: 0;
}

/*----------------------------------------*/
/*  Packages
/*----------------------------------------*/
.packages-area .packages-area-content {
  display: flex;
  justify-content: center;
}
.packages-area .packages-area-content .packages-box {
  max-width: 1095px;
}

/*----------------------------------------*/
/*  contact us
/*----------------------------------------*/
.contact-us-area .contact-us-area-content {
  display: flex;
  justify-content: center;
}
.contact-us-area .contact-us-area-content .contact-box-full {
  width: 1095px;
}
.contact-us-area .contact-us-area-content .contact-box {
  margin-top: 30px;
}
.contact-us-area .contact-us-area-content .contact-box .left {
  border-radius: 20px;
  background: rgba(255, 98, 41, 0.1);
  padding: 50px;
}
@media (max-width: 767px) {
  .contact-us-area .contact-us-area-content .contact-box .left {
    padding: 30px;
  }
}
.contact-us-area .contact-us-area-content .contact-box .right {
  width: 100%;
  height: 520px;
}
@media (max-width: 767px) {
  .contact-us-area .contact-us-area-content .contact-box .right {
    height: 450px;
  }
}
.contact-us-area .contact-us-area-content .contact-box .right img {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  object-fit: cover;
}

/*----------------------------------------*/
/*  Auth
/*----------------------------------------*/
.auth-area {
  position: relative;
}
.auth-area .auth-area-content {
  display: flex;
  align-items: center;
  height: 100vh;
  position: relative;
  z-index: 2;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .auth-area .auth-area-content {
    flex-direction: column;
  }
}
.auth-area .auth-area-content .left {
  width: 608px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-area .auth-area-content .left {
    width: 500px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .auth-area .auth-area-content .left {
    width: 100%;
    display: none;
  }
}
.auth-area .auth-area-content .left .auth-img {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
.auth-area .auth-area-content .left .auth-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.auth-area .auth-area-content .right {
  width: calc(100% - 608px);
  height: 100%;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-area .auth-area-content .right {
    width: calc(100% - 500px);
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .auth-area .auth-area-content .right {
    width: 100%;
  }
}
.auth-area .auth-area-content .right.sign-up-right {
  height: unset;
}
.auth-area .auth-area-content .right .auth-content-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 0 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box {
    height: 100%;
  }
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box {
    padding: 0 30px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-checking {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-checking {
    flex-direction: column;
    align-items: start;
    justify-content: start;
    gap: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-checking {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-checking .forgot-password .forgot-btn {
  color: var(--td-heading);
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  line-height: normal;
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-action {
  margin-top: 20px;
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-action {
    margin-top: 5px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .auth-action {
    margin-top: 20px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .switch-page {
  margin-top: 20px;
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .switch-page {
    margin-top: 5px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-forms .switch-page {
    margin-top: 20px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .switch-page p {
  color: var(--td-heading);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: normal;
}
.auth-area .auth-area-content .right .auth-content-box .auth-forms .switch-page p a {
  color: var(--td-secondary);
}
.auth-area .auth-area-content .right .auth-content-box .auth-content {
  max-width: 645px;
  padding: 50px;
  border-radius: 20px;
  background: #FFF;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-content {
    margin: 30px 0;
  }
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-content {
    padding: 16px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header {
  margin-bottom: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header {
    margin-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header {
    margin-bottom: 30px;
  }
}
.auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header-2 {
  margin-bottom: 40px;
}
.auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header h3 {
  color: var(--td-heading);
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}
.auth-area .auth-area-content .right .auth-content-box .auth-content .auth-header p {
  color: rgba(48, 48, 48, 0.8);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714;
}
.auth-area .auth-element {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  z-index: 1;
  height: 100vh;
}
.auth-area .auth-element img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#twoStepsForm {
  display: flex;
  justify-content: center;
  align-items: center;
}
#twoStepsForm .numeral-mask-wrapper {
  display: flex;
  align-items: center;
}
#twoStepsForm .numeral-mask-wrapper input {
  border: 1px solid #E1E1E1;
  border-radius: 10px;
  padding: 0px 0;
  color: #333;
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: normal;
}
@media (max-width: 767px) {
  #twoStepsForm .numeral-mask-wrapper input {
    width: 40px;
    height: 40px;
    font-size: 16px;
    font-weight: 500;
  }
}
#twoStepsForm .numeral-mask-wrapper input.form-control {
  background-color: transparent;
  margin-inline-end: 15px;
}
@media (max-width: 767px) {
  #twoStepsForm .numeral-mask-wrapper input.form-control {
    margin-inline-end: 5px;
  }
}
#twoStepsForm .numeral-mask-wrapper input:focus {
  border-color: var(--td-secondary);
  color: #333;
  box-shadow: none;
}
#twoStepsForm .numeral-mask-wrapper input:focus::placeholder {
  opacity: 0;
}
#twoStepsForm .numeral-mask-wrapper input:disabled {
  background-color: transparent;
}
#twoStepsForm .numeral-mask-wrapper-2 input {
  border-radius: 50%;
}

/*----------------------------------------*/
/*  5.1 footer-1
/*----------------------------------------*/
.footer-1 {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.footer-1 .footer-top .left .logo {
  margin-bottom: 30px;
}
.footer-1 .footer-top .left .logo img {
  height: 36px;
}
.footer-1 .footer-top .left p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5714285714;
}
.footer-1 .footer-top .left .email {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .footer-top .left .email {
    margin-top: 20px;
  }
}
.footer-1 .footer-top .left .email a {
  color: #FFF;
  font-size: 24px;
  font-weight: 600;
  line-height: 34, 24;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  padding-bottom: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .footer-top .left .email a {
    padding-bottom: 10px;
  }
}
.footer-1 .footer-top .left .email a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}
.footer-1 .footer-top .left .email a span {
  display: flex;
  align-items: center;
}
.footer-1 .footer-top .left .email a span .arrow-right {
  color: #fff;
  font-size: 30px;
}
.footer-1 .footer-top .right {
  margin-left: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .footer-top .right {
    margin-left: 0px;
  }
}
.footer-1 .footer-top .right .social-icons {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}
.footer-1 .footer-top .right .social-icons a {
  display: flex;
  align-items: center;
  gap: 2px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.5714285714;
  transition: all 0.3s ease-in-out;
}
.footer-1 .footer-top .right .social-icons a .social-icon {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
}
.footer-1 .footer-top .right .social-icons a:hover {
  color: var(--td-secondary);
}
.footer-1 .footer-top .right .social-icons a:hover .social-icon {
  color: var(--td-secondary);
}
.footer-1 .footer-top .right .footer-links {
  margin-top: 80px;
}
@media (max-width: 767px) {
  .footer-1 .footer-top .right .footer-links {
    margin-top: 45px;
  }
}
.footer-1 .footer-top .right .footer-links .footer-menu h6 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5714285714;
  margin-bottom: 22px;
}
@media (max-width: 767px) {
  .footer-1 .footer-top .right .footer-links .footer-menu h6 {
    margin-bottom: 16px;
  }
}
.footer-1 .footer-top .right .footer-links .footer-menu p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5714285714;
  width: 80%;
}
.footer-1 .footer-top .right .footer-links .footer-menu ul li {
  margin-bottom: 12px;
}
.footer-1 .footer-top .right .footer-links .footer-menu ul li:last-child {
  margin-bottom: 0;
}
.footer-1 .footer-top .right .footer-links .footer-menu ul li a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5714285714;
  transition: all 0.3s ease-in-out;
}
.footer-1 .footer-top .right .footer-links .footer-menu ul li a:hover {
  color: var(--td-secondary);
}
.footer-1 .footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
@media (max-width: 767px) {
  .footer-1 .footer-bottom {
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
}
.footer-1 .footer-bottom .left p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3846153846;
}
@media (max-width: 767px) {
  .footer-1 .footer-bottom .right {
    margin-top: 10px;
  }
}
.footer-1 .footer-bottom .right ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .footer-bottom .right ul {
    gap: 40px;
  }
}
@media (max-width: 767px) {
  .footer-1 .footer-bottom .right ul {
    gap: 10px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-1 .footer-bottom .right ul {
    gap: 40px;
  }
}
.footer-1 .footer-bottom .right ul li {
  position: relative;
}
.footer-1 .footer-bottom .right ul li::after {
  content: "";
  position: absolute;
  top: 50%;
  inset-inline-end: -30px;
  transform: translateY(-50%);
  width: 1px;
  height: 15px;
  background-color: rgba(255, 255, 255, 0.1);
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .footer-1 .footer-bottom .right ul li::after {
    inset-inline-end: -20px;
  }
}
@media (max-width: 767px) {
  .footer-1 .footer-bottom .right ul li::after {
    display: none;
  }
}
.footer-1 .footer-bottom .right ul li:last-child::after {
  display: none;
}
.footer-1 .footer-bottom .right ul li a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3846153846;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
}
.footer-1 .footer-bottom .right ul li a:hover {
  color: var(--td-secondary);
}

/*----------------------------------------*/
/*  5.2 footer-2
/*----------------------------------------*/
/*----------------------------------------*/
/*  6.1 dashboard
/*----------------------------------------*/
.bar-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--td-card-bg-1);
  background-color: var(--td-secondary);
}
.bar-icon svg {
  height: 10px;
  width: 10px;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .bar-icon {
    width: 35px;
    height: 35px;
    border-radius: 5px;
    background-color: var(--td-secondary);
  }
}

.page-wrapper {
  border-top: 1px solid rgba(48, 48, 48, 0.16);
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .page-wrapper.compact-wrapper .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.page-wrapper.compact-wrapper .app-page-body-wrapper div.app-sidebar-wrapper.close_icon ~ .app-page-body {
  margin-inline-start: 80px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.app-page-body {
  margin-inline-start: 290px;
  padding: 105px 30px 30px 30px;
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-page-body-wrapper .app-page-body {
    margin-inline-start: 0 !important;
  }
}

.app-page-header {
  max-width: 100vw;
  position: fixed;
  top: 0;
  z-index: 5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  margin-inline-start: 290px;
  width: calc(100% - 290px);
}
@media (max-width: 767px) {
  .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.app-page-header.dashboard-sticky {
  position: fixed;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
  top: 0;
  width: -webkit-fill-available;
  background: #131314;
}

.follow-box h4 {
  color: var(--td-white);
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .follow-box h4 {
    margin-top: 10px;
  }
}

/*----------------------------------------*/
/* 6.2 sidebar
/*----------------------------------------*/
.app-sidebar-wrapper {
  background: var(--td-white);
  margin-top: 80px;
  position: fixed;
  height: 100vh;
  top: 0;
  z-index: 9;
  line-height: inherit;
  text-align: left;
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .app-sidebar-wrapper .main-sidebar-header {
    display: flex;
    justify-content: end;
  }
}
.app-sidebar-wrapper.close_icon {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  margin-inline-start: 0;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-sidebar-wrapper.close_icon {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    margin-inline-start: -300px;
  }
}
.app-sidebar-wrapper .sidebar-inner {
  -webkit-box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--td-heading);
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .app-sidebar-wrapper .sidebar-inner {
    background-color: #11131A;
  }
}

.app-sidebar {
  width: 288px;
  inset-block-start: 0;
  inset-inline-start: 0;
  background: var(--td-bg);
  border-inline-end: 1px solid rgba(48, 48, 48, 0.16);
  padding: 30px 20px;
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  height: 100vh;
  overflow-y: auto;
}
.app-sidebar::-webkit-scrollbar {
  width: 0.125rem;
  height: 7px;
}
.app-sidebar::-webkit-scrollbar-track {
  background: #646464;
}
.app-sidebar::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 100%);
  border-radius: 0.625rem;
}
.app-sidebar::-webkit-scrollbar-thumb:hover {
  background: #646464;
}
.app-sidebar .nav > ul {
  padding-inline-start: 0px;
}
.app-sidebar .nav ul li {
  list-style: none;
  margin-bottom: 10px;
}
.app-sidebar .nav ul li ul li a {
  background-color: var(--td-card-bg-1);
}
.app-sidebar .nav ul li ul li a:hover, .app-sidebar .nav ul li ul li a.active {
  background-color: var(--td-card-bg-2);
}
.app-sidebar .nav ul li ul li a:hover .sidebar-menu-label, .app-sidebar .nav ul li ul li a.active .sidebar-menu-label {
  color: var(--td-white);
}
.app-sidebar .sidebar-menu {
  display: none;
}
.app-sidebar .sidebar-left,
.app-sidebar .sidebar-right {
  display: none;
}
.app-sidebar .main-menu > .slide.active .sidebar-menu .sidebar-menu-item:hover .side-menu-angle, .app-sidebar .main-menu > .slide:hover .sidebar-menu .sidebar-menu-item:hover .side-menu-angle {
  color: var(--td-primary) !important;
}
.app-sidebar .slide.has-sub .sidebar-menu {
  transform: translate(0, 0) !important;
  visibility: visible !important;
}
.app-sidebar .slide.has-sub {
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide.has-sub {
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide:hover .sidebar-menu-item, .app-sidebar .slide.active .sidebar-menu-item {
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.1);
  background: rgba(255, 98, 41, 0.08);
}
.app-sidebar .slide:hover .sidebar-menu-item .sidebar-menu-label, .app-sidebar .slide.active .sidebar-menu-item .sidebar-menu-label {
  color: var(--td-secondary);
}
.app-sidebar .slide:hover .sidebar-menu-item .side-menu-icon .dashbaord-icon, .app-sidebar .slide.active .sidebar-menu-item .side-menu-icon .dashbaord-icon {
  color: var(--td-secondary);
}
.app-sidebar .slide:hover .sidebar-menu-item.has-dropdown {
  border-radius: 12px !important;
}
.app-sidebar .slide.logout .sidebar-menu-item .sidebar-menu-label {
  color: rgba(233, 78, 91, 0.65);
}
.app-sidebar .slide.logout .sidebar-menu-item .side-menu-icon svg * {
  stroke: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child2 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child3 .sidebar-menu-item:hover {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-category .category-name {
  color: rgba(21, 20, 21, 0.7);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  padding: 4px 10px;
  white-space: nowrap;
  position: relative;
  margin-top: 15px;
  display: block;
}
[dir=rtl] .app-sidebar .sidebar-menu-category .category-name {
  text-align: right;
}
.app-sidebar .sidebar-menu-item {
  padding: 12px 16px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: 12px;
}
.app-sidebar .sidebar-menu-item.active {
  color: var(--td-white);
  background-color: var(--td-card-bg-2);
}
.app-sidebar .sidebar-menu-item.active .sidebar-menu-label,
.app-sidebar .sidebar-menu-item.active .side-menu-angle {
  color: var(--td-white);
}
.app-sidebar .sidebar-menu-item.active .side-menu-icon {
  color: var(--td-white);
}
.app-sidebar .sidebar-menu-item:hover {
  background-color: var(--td-card-bg-1);
}
.app-sidebar .sidebar-menu-item:hover .logout:hover .sidebar-menu-item .sidebar-menu-label {
  color: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu-item:hover .logout:hover .sidebar-menu-item .side-menu-icon svg * {
  stroke: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu-item .dropdown-icon {
  display: none;
}
.app-sidebar .sidebar-menu-item.has-dropdown {
  position: relative;
}
.app-sidebar .sidebar-menu-item.has-dropdown.open {
  background-color: var(--td-card-bg-2);
}
.app-sidebar .sidebar-menu-item.has-dropdown .dropdown-icon {
  display: inline-block;
  position: absolute;
  inset-inline-end: 20px;
  top: 50%;
  transform: translateY(-50%);
}
.app-sidebar .sidebar-menu-item.sidebar-menu-item-button {
  width: 100%;
}
.app-sidebar .sidebar-menu {
  padding: 0;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item {
  padding: 6px 6px;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item:before {
  position: absolute;
  content: "\e404";
  font-family: "Font Awesome 6 Pro";
  font-size: 12px;
  inset-inline-start: -10px;
  opacity: 0.8;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item.active {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 li, .app-sidebar .sidebar-menu.child2 li, .app-sidebar .sidebar-menu.child3 li {
  padding: 0;
  position: relative;
}
.app-sidebar .sidebar-menu.child1 li {
  padding-inline-start: 56px;
}
.app-sidebar .sidebar-menu.child2 li {
  padding-inline-start: 12px;
}
.app-sidebar .sidebar-menu.child3 li {
  padding-inline-start: 16px;
}
.app-sidebar .sidebar-menu-label {
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  transition: 0.3s;
  color: var(--td-heading);
  font-size: 15px;
  font-weight: 600;
  line-height: normal;
}
.app-sidebar .side-menu-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--td-heading);
}
.app-sidebar .side-menu-icon .dashbaord-icon {
  font-size: 20px;
  color: var(--td-primary);
}
.app-sidebar .side-menu-icon .dashbaord-icon.overview-icon {
  color: #2674FB;
}
.app-sidebar .side-menu-icon .dashbaord-icon.listing-icon {
  color: #1283B4;
}
.app-sidebar .side-menu-icon .dashbaord-icon.purchase-icon {
  color: #FBAD26;
}
.app-sidebar .side-menu-icon .dashbaord-icon.selling-icon {
  color: #31B269;
}
.app-sidebar .side-menu-icon .dashbaord-icon.support-icon {
  color: #00B2FF;
}
.app-sidebar .side-menu-icon .dashbaord-icon.follow-icon {
  color: #2674FB;
}
.app-sidebar .side-menu-icon .dashbaord-icon.payment-icon {
  color: #E830CF;
}
.app-sidebar .side-menu-icon .dashbaord-icon.history-icon {
  color: #FF9900;
}
.app-sidebar .side-menu-icon .dashbaord-icon.chat-icon {
  color: #2674FB;
}
.app-sidebar .side-menu-icon .dashbaord-icon.wishlist-icon {
  color: #FF6229;
}
.app-sidebar .side-menu-icon .dashbaord-icon.settings-icon {
  color: #9C9538;
}
.app-sidebar .side-menu-icon .dashbaord-icon.logout-icon {
  color: #FB2D26;
}
.app-sidebar .side-menu-angle {
  transform-origin: center;
  position: absolute;
  inset-inline-end: 20px;
  line-height: 1;
  font-size: 14px;
  transition: all 0.03s ease;
  opacity: 0.8;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 767px) {
  .close_sidebar.app-sidebar {
    inset-inline-start: 0px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 767px) {
  .app-sidebar {
    inset-inline-start: -300px;
  }
}
.app-sidebar.collapsed {
  inset-inline-start: -300px;
}
.app-sidebar.nav-folded {
  width: 61px;
  transition: 0.2s;
}
.app-sidebar.nav-folded .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-menu-item {
  padding: 16px 20px;
  display: inline-flex;
}
.app-sidebar.nav-folded .sidebar-menu-item .sidebar-menu-label,
.app-sidebar.nav-folded .sidebar-menu-item .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .main-logo {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .small-logo {
  display: block;
}
.app-sidebar.nav-folded .invite-card-content {
  display: none;
}
.app-sidebar.nav-folded .invite-card-box {
  padding: 8px;
  margin: 10px 10px;
}
.app-sidebar .app-sidebar.nav-folded.side-nav-hover .sidebar-menu-category .category-name {
  display: block;
}
.app-sidebar.side-nav-hover {
  width: 290px;
  transition: all 0.3s ease;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-category .category-name {
  display: block !important;
}
.app-sidebar.side-nav-hover .sidebar-menu-item {
  display: flex;
}
.app-sidebar.side-nav-hover .sidebar-logo .main-logo {
  display: block;
}
.app-sidebar.side-nav-hover .sidebar-logo .small-logo {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: block;
}
.app-sidebar.side-nav-hover .invite-card-box {
  padding: 8px 8px 8px 16px;
}
.app-sidebar.side-nav-hover .invite-card-content {
  display: block;
  transition: 0.2s ease;
  opacity: 1;
}

.toggle-sidebar {
  position: absolute;
  top: 40%;
  transform: translateY(-40%);
  inset-inline-end: -18px;
  z-index: 5;
  display: none;
}
@media (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .toggle-sidebar {
    display: block;
    position: relative;
    top: inherit;
    inset-inline-end: inherit;
    z-index: 5;
    transform: translateY(0%);
    margin-bottom: 14px;
  }
}
.toggle-sidebar.active .bar-icon {
  transform: rotate(-180deg);
}

.main-sidebar-footer {
  display: block;
  margin: 20px;
  padding: 20px;
  border-radius: 14px;
  border: 1px dashed rgba(63, 128, 255, 0.7);
  background: rgba(63, 128, 255, 0.1);
  transition: all 0.3s ease-in-out;
}
.main-sidebar-footer:hover {
  background: rgba(63, 128, 255, 0.2);
}
.main-sidebar-footer .content {
  margin-top: 12px;
}
.main-sidebar-footer .content .user-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.main-sidebar-footer .content .user-box .user-img {
  position: relative;
}
.main-sidebar-footer .content .user-box .user-img .img {
  height: 70px;
  width: 70px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid var(--td-card-bg-2);
}
.main-sidebar-footer .content .user-box .user-img .img img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
.main-sidebar-footer .content .user-box .user-img .user-badge {
  position: absolute;
  inset-inline-end: -4px;
  top: -10px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: #3CAB62;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main-sidebar-footer .content .user-box .user-img .user-badge p {
  color: #FFF;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 10px;
}
.main-sidebar-footer .content .user-box .user-text {
  margin-top: 10px;
}
.main-sidebar-footer .content .user-box .user-text h6 {
  color: var(--td-white);
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
}
.main-sidebar-footer .content .user-box .user-text p {
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  font-size: 10px;
  font-weight: 400;
  line-height: 14px;
}

.app-sidebar.nav-folded .main-sidebar {
  height: calc(100% - 200px);
}
.app-sidebar.nav-folded .main-sidebar-footer {
  margin: 5px;
  padding: 15px 5px;
}
.app-sidebar.nav-folded .main-sidebar-footer .content .user-box .user-img .img {
  height: 30px;
  width: 30px;
}
.app-sidebar.nav-folded .main-sidebar-footer .content .user-box .user-text {
  display: none;
}

.has-submenu-slide.open {
  border-radius: 12px;
  border: 1px solid rgba(48, 48, 48, 0.1);
  background: #FFF;
  overflow: hidden;
}
.has-submenu-slide .has-dropdown {
  cursor: pointer;
  position: relative;
  border-radius: 0 !important;
  background-color: transparent;
  border: 1px solid transparent;
}
.has-submenu-slide .has-dropdown:hover, .has-submenu-slide .has-dropdown.active {
  border: 1px solid transparent;
}
.has-submenu-slide .has-dropdown.open {
  background-color: rgba(255, 98, 41, 0.08);
  border: 1px solid transparent !important;
}
.has-submenu-slide .has-dropdown.open .dropdown-icon svg {
  transform: rotate(180deg);
}
.has-submenu-slide .has-submenu-slide-content {
  display: none;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}
.has-submenu-slide .has-submenu-slide-content li {
  margin-bottom: 0 !important;
}
.has-submenu-slide .has-submenu-slide-content li .sidebar-inside-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 13px 13px 13px 20px;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 600;
  line-height: normal;
  transition: all 0.3s ease-in-out;
  border-radius: 12px;
  border: none;
}
.has-submenu-slide .has-submenu-slide-content li .sidebar-inside-menu-item.active, .has-submenu-slide .has-submenu-slide-content li .sidebar-inside-menu-item:hover {
  background: rgba(255, 98, 41, 0.08);
  border-radius: 0 !important;
  border: none !important;
}
.has-submenu-slide .has-submenu-slide-content li .sidebar-inside-menu-item.active .sidebar-menu-label, .has-submenu-slide .has-submenu-slide-content li .sidebar-inside-menu-item:hover .sidebar-menu-label {
  color: var(--td-heading);
}
.has-submenu-slide .has-submenu-slide-content.open {
  display: block;
}

/*----------------------------------------*/
/*  dashboard card
/*----------------------------------------*/
.seller-overview-card {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 12px;
  padding: 24px 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  background-color: #FFF;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease-in-out;
}
.seller-overview-card:hover {
  background-color: #FFF2EE !important;
}
.seller-overview-card .icon {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  margin-bottom: 16px;
}
.seller-overview-card .icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.seller-overview-card .text p {
  color: var(--td-heading);
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 8px;
}
.seller-overview-card .text h3 {
  color: var(--td-secondary);
  font-size: 26px;
  font-weight: 600;
  line-height: normal;
}

/*# sourceMappingURL=styles.css.map */
