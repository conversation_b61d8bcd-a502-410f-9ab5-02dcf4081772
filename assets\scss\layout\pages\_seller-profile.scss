@use "../../utils" as *;

/*----------------------------------------*/
/*  3.2.10 Seller Profile
/*----------------------------------------*/
.seller-profile-details-area {
  .seller-profile-details-area-content {
    background: #ebf6fa;
    padding: 30px;
    border-radius: 30px;

    @media #{$xs} {
      padding: 20px;
      border-radius: 20px;
    }

    .seller-profile {
      .seller-image {
        position: relative;
        height: 140px;
        width: 140px;
        .img {
          height: 140px;
          width: 140px;
          border-radius: 50%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .has-pro {
          position: absolute;
          top: 8%;
          inset-inline-end: 8px;

          img {
            width: 24px;
            height: 24px;
          }

          @media #{$xs} {
            top: -5px;
            inset-inline-end: -5px;
          }
        }
      }

      .seller-content {
        h5 {
          color: var(--td-heading);
          font-size: 30px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 10px;
        }

        .seller-description {
          color: var(--td-heading);
          font-size: 14px;
          font-weight: 400;
          line-height: lh(22, 14);
          margin-bottom: 16px;
          width: 80%;

          @media #{$lg,$md,$xs} {
            width: 100%;
          }
        }

        .seller-buttons-box {
          display: flex;
          align-items: center;
          gap: 40px;

          @media #{$xs} {
            flex-direction: column;
            align-items: start;
            gap: 10px;
          }

          .action-btns {
            display: flex;
            gap: 13px;
          }

          .seller-rating {
            display: flex;
            align-items: center;
            gap: 30px;

            .followers {
              display: flex;
              flex-direction: column;
              align-items: center;

              h4 {
                color: var(--td-heading);
                font-size: 20px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 4px;
              }

              p {
                color: var(--td-text-primary);
                font-size: 14px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 0;
              }
            }

            .bar {
              width: 1px;
              height: 40px;
              background: rgba(48, 48, 48, 0.16);
            }

            .following {
              display: flex;
              flex-direction: column;
              align-items: center;

              h4 {
                color: var(--td-heading);
                font-size: 20px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 4px;
              }

              p {
                color: var(--td-text-primary);
                font-size: 14px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 0;
              }
            }
          }
        }
      }

      .seller-stats {
        padding: 16px;
        border-radius: 10px;
        border: 1px solid rgba(48, 48, 48, 0.16);
        background: #fff;
        max-width: 295px;

        @media #{$xs} {
          max-width: 100%;
        }

        .point {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 18px;

          &:last-child {
            margin-bottom: 0;
          }

          .star {
            display: flex;
            align-items: center;
            gap: 3px;

            img {
              width: 16px;
              height: 16px;
            }
          }

          p {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            margin-bottom: 0;
          }

          h6 {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 600;
            line-height: normal;

            &.level-tag {
              display: flex;
              height: 24px;
              padding: 0px 10px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 30px;
              background: #31b269;
              color: #fff;
              font-size: 13px;
              font-style: normal;
              font-weight: 600;
              line-height: lh(13, 13);
            }
          }
        }
      }
    }
  }
}

.tab-filter {
  .tab-button-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .profile-filter-button {
      height: 40px;
      display: flex;
      padding: 10px 15px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px solid rgba(48, 48, 48, 0.16);
      backdrop-filter: blur(8px);
      color: rgba(48, 48, 48, 0.8);
      font-size: 13px;
      font-weight: 600;
      line-height: lh(20, 13);

      &.active {
        background-color: var(--td-secondary);
        border: 1px solid var(--td-secondary);
        color: var(--td-white);
      }
    }
  }
}
