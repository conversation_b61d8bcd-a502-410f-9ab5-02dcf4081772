@use '../../utils' as *;

/*----------------------------------------*/
/*  Seller Profile
/*----------------------------------------*/
.seller-profile-details-area {
  background: #F9F9F9;

  .seller-profile-details-area-content {
    .seller-profile {
      .seller-image {
        width: 100%;
        height: 304px;
        overflow: hidden;
        border-radius: 12px;

        @media #{$md,$xs} {
          height: 404px;
        }

        @media #{$xs} {
          height: 304px;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .seller-content {
        h5 {
          color: var(--td-heading);
          font-size: 30px;
          font-weight: 600;
          line-height: normal;
          margin-bottom: 10px;
        }

        .seller-description {
          color: var(--td-text-primary);
          font-size: 14px;
          font-weight: 400;
          line-height: lh(22, 14);
          margin-bottom: 16px;
          width: 80%;
        }

        .seller-stats {
          padding: 16px;
          border-radius: 12px;
          border: 1px solid rgba(48, 48, 48, 0.16);
          background: #FFF;
          max-width: 295px;

          .point {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 18px;

            &:last-child {
              margin-bottom: 0;
            }

            p {
              color: var(--td-text-primary);
              font-size: 14px;
              font-weight: 500;
              line-height: normal;
              margin-bottom: 0;
            }

            h6 {
              color: var(--td-text-primary);
              font-size: 14px;
              font-weight: 600;
              line-height: normal;

              &.level-tag {
                display: flex;
                height: 24px;
                padding: 0px 10px;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border-radius: 30px;
                background: #31B269;
                color: #FFF;
                font-size: 13px;
                font-style: normal;
                font-weight: 600;
                line-height: lh(13, 13);
              }
            }
          }
        }

        .action-btns {
          display: flex;
          gap: 13px;
          margin-top: 30px;
        }
      }

      .seller-rating {
        display: flex;
        align-items: center;
        gap: 40px;

        .followers {
          h4 {
            color: var(--td-heading);
            font-size: 30px;
            font-weight: 500;
            line-height: normal;
            margin-bottom: 5px;
          }

          p {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 0;
          }
        }

        .bar {
          width: 1px;
          height: 66px;
          background: rgba(48, 48, 48, 0.16);
        }

        .following {
          h4 {
            color: var(--td-heading);
            font-size: 30px;
            font-weight: 500;
            line-height: normal;
            margin-bottom: 5px;
          }

          p {
            color: var(--td-text-primary);
            font-size: 14px;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

.tab-filter {
  .tab-button-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .profile-filter-button {
      height: 40px;
      display: flex;
      padding: 10px 15px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px solid rgba(48, 48, 48, 0.16);
      backdrop-filter: blur(8px);
      color: rgba(48, 48, 48, 0.80);
      font-size: 13px;
      font-weight: 600;
      line-height: lh(20, 13);

      &.active{
        background-color: var(--td-secondary);
        border: 1px solid var(--td-secondary);
        color: var(--td-white);
      }
    }
  }
}