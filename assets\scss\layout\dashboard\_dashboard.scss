@use '../../utils' as *;

/*----------------------------------------*/
/*  6.1 dashboard
/*----------------------------------------*/

// Bar icon
.bar-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--td-card-bg-1);
    background-color: var(--td-secondary);

    svg{
        height: 10px;
        width: 10px;
    }

    @media #{$xs,$sm,$md,$lg} {
        width: 35px;
        height: 35px;
        border-radius: 5px;
        background-color: var(--td-secondary);
    }
}

// Dashboard calculation styles
.page-wrapper {
    border-top: 1px solid rgba(48, 48, 48, 0.16);
    &.compact-wrapper {
        .app-page-header {
            @media #{$xs,$sm,$md,$lg} {
                margin-inline-start: 0;
                width: 100%;
            }
        }

        .app-page-body-wrapper {
            div {
                &.app-sidebar-wrapper {
                    &.close_icon {
                        &~.app-page-body {
                            margin-inline-start: 80px;
                            -webkit-transition: 0.3s;
                            transition: 0.3s;
                        }
                    }
                }
            }
        }
    }
}

.app-page-body {
    margin-inline-start: 290px;
    padding: 105px 30px 30px 30px;
    position: relative;
    -webkit-transition: 0.3s;
    transition: 0.3s;
}

.app-page-body-wrapper {
    .app-page-body {
        @media #{$xs,$sm,$md,$lg} {
            margin-inline-start: 0 !important;
        }
    }
}

// Dashboard header styles
.app-page-header {
    max-width: 100vw;
    position: fixed;
    top: 0;
    z-index: 5;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    margin-inline-start: 290px;
    width: calc(100% - 290px);

    @media #{$xs} {
        margin-inline-start: 0;
        width: 100%;
    }

    &.dashboard-sticky {
        position: fixed;
        animation: sticky 0.3s;
        -webkit-animation: sticky 0.3s;
        top: 0;
        width: -webkit-fill-available;
        background: #131314;
    }
}

.follow-box{
    h4{
        color: var(--td-white);

        @media #{$xs,$sm,$md,$lg} {
            margin-top: 10px;
        }
    }
}