@use "../../utils" as *;

/*----------------------------------------*/
/*  3.4.5 Dashboard
/*----------------------------------------*/

// Bar icon
.bar-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--td-card-bg-1);
    background-color: var(--td-secondary);

    svg {
        height: 10px;
        width: 10px;
    }

    @media #{$xs,$sm,$md,$lg} {
        width: 35px;
        height: 35px;
        border-radius: 5px;
        background-color: var(--td-secondary);
    }
}

// Dashboard calculation styles
.page-wrapper {
    border-top: 1px solid rgba(48, 48, 48, 0.16);
    &.compact-wrapper {
        .app-page-header {
            @media #{$xs,$sm,$md,$lg} {
                margin-inline-start: 0;
                width: 100%;
            }
        }

        .app-page-body-wrapper {
            div {
                &.app-sidebar-wrapper {
                    &.close_icon {
                        & ~ .app-page-body {
                            margin-inline-start: 80px;
                            -webkit-transition: 0.3s;
                            transition: 0.3s;
                        }
                    }
                }
            }
        }
    }
}

.app-page-body {
    margin-inline-start: 290px;
    padding: 105px 30px 30px 30px;
    position: relative;
    -webkit-transition: 0.3s;
    transition: 0.3s;
}

.app-page-body-wrapper {
    .app-page-body {
        @media #{$xs,$sm,$md,$lg} {
            margin-inline-start: 0 !important;
        }
    }
}

// Dashboard header styles
.app-page-header {
    max-width: 100vw;
    position: fixed;
    top: 0;
    z-index: 5;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    margin-inline-start: 290px;
    width: calc(100% - 290px);

    @media #{$xs} {
        margin-inline-start: 0;
        width: 100%;
    }

    &.dashboard-sticky {
        position: fixed;
        animation: sticky 0.3s;
        -webkit-animation: sticky 0.3s;
        top: 0;
        width: -webkit-fill-available;
        background: #131314;
    }
}

.follow-box {
    h4 {
        color: var(--td-white);

        @media #{$xs,$sm,$md,$lg} {
            margin-top: 10px;
        }
    }
}

a {
    &.link {
        color: var(--td-secondary);
    }
}

.full-user-dashboard {
    padding-top: 80px;
}

.user-header {
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(48, 48, 48, 0.16);

    .user-mobile-toggle {
        display: none;

        @media #{$xs,$md} {
            display: flex;
            align-items: center;
            cursor: pointer;

            .hamburger-icon {
                font-size: 30px;
                color: var(--td-heading);
            }
        }
    }

    ul {
        display: flex;
        align-items: center;
        gap: 5px;
        @media #{$xs,$md} {
            display: none;
        }
        li {
            position: relative;

            .user-header-link {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 13px 10px;

                .icon-and-text {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .icon {
                        display: flex;
                        align-self: center;

                        .user-header-icon {
                            font-size: 18px;
                            color: var(--td-heading);
                        }
                    }

                    .text {
                        font-size: 14px;
                        font-weight: 600;
                        color: var(--td-heading);
                    }
                }
                .dropdown-indicator {
                    display: flex;
                    align-self: center;

                    .user-header-dropdown-icon {
                        font-size: 18px;
                        color: var(--td-heading);
                    }
                }

                &.active {
                    border-bottom: 2px solid var(--td-secondary);
                }
            }

            .sub-menu {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                position: absolute;
                top: 100%;
                left: 0;
                background-color: #eeeeee;
                border-radius: 0 0 8px 8px;
                padding: 14px 18px;
                min-width: 210px;
                z-index: 5;
                display: none;

                // &.open {
                //     display: block;
                // }

                li {
                    margin-bottom: 0px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                    a {
                        color: var(--td-heading);
                        transition: all 0.3s ease-in-out;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: normal;
                        padding: 8px 10px;
                        width: 100%;
                        display: inline-block;

                        &:hover,
                        &.active {
                            color: var(--td-secondary);
                        }
                    }
                }
            }
        }
    }
}

.user-dashboard {
    margin-top: 30px;
}
