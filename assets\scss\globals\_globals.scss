@use "../utils" as u;
@use "../utils" as *;

/*-----------------------------------------------------------------------------------

  Project Name:  Gamecon - Easy, Secure and Best Way to Earn Money From Your Own Place
  Author: Tdevs
  Support: Tdevs
  Description:  Easy, Secure and Best Way to Earn Money From Your Own Place 
  Version: 1.0
  
-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************

	-----------------
    01. GLOBAL CSS
	-----------------
		1.1 Globals
		1.2 Spacing
		1.3 Typography

	-----------------
    02. COMPONENTS CSS
	-----------------
		2.1 Button
		2.2 Cards
		2.3 Search
		2.4 Offcanvas
		2.5 Search Popup
		2.6 Notification
		2.7 Chat
		2.8 User
		2.9 Heading
		2.10 Nice Select
		2.11 Pagination
		2.12 Forms
		2.13 Promo
		2.14 Alert
		2.15 Preloader
		2.16 Back To Top
		2.17 Custom Animation
		2.18 Error 404

	-----------------
    03. LAYOUT CSS
	-----------------
		3.1 Header
			3.1.1 Header One
			3.1.2 Header Two
		3.2 Pages
			3.2.1 About
			3.2.2 Hero
			3.2.3 Category
			3.2.4 All Games
			3.2.5 Popular Seller
			3.2.6 FAQ
			3.2.7 Ads Banner
			3.2.8 Product Details
			3.2.9 Checkout
			3.2.10 Seller Profile
			3.2.11 Blog Details
			3.2.12 Package
			3.2.13 Contact Us
			3.2.14 Auth
		3.3 Footer
			3.3.1 Footer One
			3.3.2 Footer Two
		3.4 Dashboard
			3.4.1 Badge
			3.4.2 Common Dashboard Table
			3.4.3 Dashbaord Card
			3.4.4 Dashboard Chat
			3.4.5 Dashboard
			3.4.6 Error Fix
			3.4.7 Main Page
			3.4.8 Modal
			3.4.9 Product Listing
			3.4.10 Referral Program
			3.4.11 Referrals Tree
			3.4.12 Settings
			3.4.13 Sidebar
			3.4.14 Upload System


	---------------------------------
	04. UTILS CSS
	---------------------------------
		4.1 Extends
		4.2 Functions
		4.3 Mixins
		4.4 Root
		4.5 Variables



/*----------------------------------------*/
/*   1.1 Globals
/*----------------------------------------*/

// Google Fonts
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Space+Grotesk:wght@300..700&display=swap");

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
@media (min-width: 1601px) {
	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl,
	.container-xxl {
		max-width: 1345px;
	}
}

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder {
	.mfp-content {
		line-height: 0;
		width: 100%;
		max-width: 1280px;

		@media #{$xxl,$x3l} {
			max-width: 1000px;
		}

		@media #{$xl} {
			max-width: 850px;
		}

		@media #{$lg} {
			max-width: 820px;
		}

		@media #{$md} {
			max-width: 750px;
		}
	}
}

.mfp-close {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);

	&:hover {
		color: var(--td-white);
	}

	&::after {
		position: absolute;
		content: "\f00d";
		height: 100%;
		width: 100%;
		font-family: var(--td-ff-fontawesome);
		font-size: 20px;
		font-weight: 900;
		inset-inline-end: -5px;
		margin-top: -16px;

		@media #{$xs,$sm,$md} {
			inset-inline-end: 15px;
			margin-top: -30px;
		}
	}
}

.home-2 {
	background-color: var(--td-bg);
}
